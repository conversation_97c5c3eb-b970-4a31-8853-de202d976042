{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"dataDirect": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist/datadirect-admin"}, "index": "src/index.html", "polyfills": ["zone.js", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.css"], "scripts": [], "browser": "src/main.ts"}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.development.ts", "with": "src/environments/environment.production.ts"}], "budgets": [{"type": "initial", "maximumWarning": "10000kb", "maximumError": "1000mb"}, {"type": "anyComponentStyle", "maximumWarning": "100000kb", "maximumError": "100000kb"}], "outputHashing": "all"}, "staging": {"fileReplacements": [{"replace": "src/environments/environment.development.ts", "with": "src/environments/environment.staging.ts"}], "budgets": [{"type": "initial", "maximumWarning": "10000kb", "maximumError": "1000mb"}, {"type": "anyComponentStyle", "maximumWarning": "100000kb", "maximumError": "100000kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "dataDirect:build:production"}, "staging": {"buildTarget": "dataDirect:build:staging"}, "development": {"buildTarget": "dataDirect:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "dataDirect:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/datatables.net-dt/css/jquery.dataTables.css", "node_modules/datatables.net-buttons-dt/css/buttons.dataTables.css", "src/styles.css"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.min.js", "node_modules/datatables.net/js/jquery.dataTables.js", "node_modules/@popperjs/core/dist/umd/popper.min.js", "node_modules/jquery/dist/jquery.min.js", "node_modules/jszip/dist/jszip.js", "node_modules/datatables.net-buttons/js/dataTables.buttons.js", "node_modules/datatables.net-buttons/js/buttons.colVis.js", "node_modules/datatables.net-buttons/js/buttons.flash.js", "node_modules/datatables.net-buttons/js/buttons.html5.js", "node_modules/datatables.net-buttons/js/buttons.print.js", "src/assets/custom.js"]}}}}}, "cli": {"analytics": false}}