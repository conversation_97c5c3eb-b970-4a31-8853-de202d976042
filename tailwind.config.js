/** @type {import('tailwindcss').Config} */
const plugin = require("tailwindcss/plugin");

module.exports = {
  content: ["./src/**/*.{html,ts}"],

  theme: {
    extend: {
      colors: {
        primary: "#00aeef",
        secondary: "#6c757d",
        success: "#22c55e",
        danger: "#dc3545",
        warning: "#ffc107",
        ring: "#6c757d",
        dark: "#2d4456",
        foreground: "#374151",
        background: "#ffffff",
        info: "#0dcaf0",
        light: "#f8f9fa",
        muted: "#6c757d",
        "primary-dark": "#0d4f8a",
        "secondary-dark": "#5a6268",
      },
      boxShadow: {
        DEFAULT:
          "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
        outline: "0 0 0 2px rgba(20, 104, 170, 0.25)",
      },
    },
  },
  plugins: [
    require("./tailwind-bootstrap-plugin"),
    require("tailwind-scrollbar")({ nocompatible: true }),
    plugin(function ({ addUtilities, theme }) {
      addUtilities({
        ".modern-scrollbar": {
          overflowY: "auto", // Vertical scroll only
          scrollbarWidth: "none", // Hide scrollbar by default
          scrollBehavior: "smooth", // Smooth scrolling
        },
        ".modern-scrollbar:hover": {
          scrollbarWidth: "thin", // Thin scrollbar on hover
        },
        ".modern-scrollbar::-webkit-scrollbar": {
          width: "0.4rem", // Adjust width of the scrollbar
          height: "0.4rem", // Adjust width of the scrollbar
        },
        ".modern-scrollbar::-webkit-scrollbar-track": {
          backgroundColor: "transparent", // Transparent track
        },
        ".modern-scrollbar::-webkit-scrollbar-thumb": {
          backgroundColor: theme("colors.gray.400"), // Gray thumb
          borderRadius: "9999px", // Round thumb
        },
        ".modern-scrollbar:hover::-webkit-scrollbar-thumb": {
          backgroundColor: theme("colors.gray.400"), // Hover thumb color
        },
        ".modern-scrollbar:hover": {
          scrollbarWidth: "auto", // Reset scrollbar width on hover to auto
        },
      });
    }),
  ],
};
