/* Import PrimeNG themes and components */
@import "primeicons/primeicons.css";

/* html {
  font-size: 14px;
} */

:root {
  font-family: Inter, sans-serif;
  font-feature-settings:
    "cv11",
    "liga" 1,
    "calt" 1; /* fix for Chrome */
}
@supports (font-variation-settings: normal) {
  :root {
    font-family: InterVariable, sans-serif;
  }
}

/* Tailwind CSS import */
@layer tailwind-base, primeng, tailwind-utilities;

@layer tailwind-base {
  @tailwind base;
}

@layer tailwind-utilities {
  @tailwind components;
  @tailwind utilities;
}

table {
  font-size: 14px !important;
}

/* Autofill styling */
@layer components {
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-background-clip: content-box !important;
    -webkit-text-fill-color: #374151 !important;
    transition: background-color 5000s ease-in-out 0s;
    box-shadow: inset 0 0 0 30px #fcfbfc !important;
  }

  .form-control {
    @apply flex w-full px-3 py-1 text-sm transition-colors border border-gray-200 rounded-md shadow-sm bg-background h-9 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:bg-background disabled:opacity-75;
  }

  .p-datatable > .p-datatable-wrapper {
    @apply !overflow-hidden;
  }
}

/* Custom SweetAlert styling */
.swal2-container {
  z-index: 9999 !important; /* Ensure above other elements */
}

.p-datatable > .p-datatable-wrapper {
  @apply !modern-scrollbar;
}

.p-datatable .p-sortable-column.p-highlight {
  background: #eff6ff !important; /* Ensure it overrides */
  color: #374151 !important; /* Ensure it overrides */
}

.p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
  color: #374151 !important; /* Ensure it overrides */
}

th.p-element.p-sortable-column.p-highlight {
  background: #f9fafb !important;
}
.p-component {
  font-size: 14px;
}
