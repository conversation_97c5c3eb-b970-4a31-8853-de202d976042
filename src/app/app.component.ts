import { Component } from "@angular/core";
import { RouterOutlet } from "@angular/router";
import { faCog } from "@fortawesome/free-solid-svg-icons";
import { PrimeNG } from "primeng/config";
import { IdleMonitorComponent } from "./features/auth/idle/idle-monitor.component";
import { NetworkStatusComponent } from "./features/network-status/network-status.component";

@Component({
  selector: "app-root",
  templateUrl: "./app.component.html",
  standalone: true,
  imports: [RouterOutlet, IdleMonitorComponent, NetworkStatusComponent],
})
export class AppComponent {
  title = "dataDirect";

  cogIcon = faCog;
  today = new Date();

  constructor(private primeng: PrimeNG) {}

  ngOnInit() {
    this.primeng.zIndex = {
      modal: 1100, // dialog, sidebar
      overlay: 1000, // dropdown, overlaypanel
      menu: 1000, // overlay menus
      tooltip: 1100, // tooltip
    };
  }
}
