import { DatePipe } from "@angular/common";
import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
} from "@angular/common/http";
import {
  ApplicationConfig,
  importProvidersFrom,
  provideZoneChangeDetection,
} from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { provideAnimationsAsync } from "@angular/platform-browser/animations/async";
import { provideRouter } from "@angular/router";
import { provideCharts, withDefaultRegisterables } from "ng2-charts";
import { NgxEditorModule } from "ngx-editor";
import { MessageService } from "primeng/api";
import { providePrimeNG } from "primeng/config";
import { ToastModule } from "primeng/toast";
import { routes } from "./app.routes";
import { AppTheme } from "./app.theme";
import { AuthInterceptor } from "./features/auth/core/interceptors/auth.interceptor";

export const appConfig: ApplicationConfig = {
  providers: [
    provideAnimationsAsync(),
    providePrimeNG({
      theme: {
        preset: AppTheme,
        options: {
          prefix: "p",
          darkModeSelector: false,
          cssLayer: {
            name: "primeng",
            order: "tailwind-base, primeng, tailwind-utilities",
          },
        },
      },
    }),
    DatePipe,
    // Configure HTTP interceptors
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    // Enable zone.js change detection with event coalescing
    provideZoneChangeDetection({ eventCoalescing: true }),

    // Configure application routing
    provideRouter(routes),

    // Configure charts
    provideCharts(withDefaultRegisterables()),

    // Configure HTTP client with interceptors
    provideHttpClient(withInterceptorsFromDi()),

    // Import additional modules
    importProvidersFrom(
      ReactiveFormsModule,
      BrowserAnimationsModule,
      ToastModule,
      NgxEditorModule,
    ),

    // Add PrimeNG message service
    MessageService,

    // Provide NgxEditor localization configuration
    {
      provide: "NGX_EDITOR_CONFIG",
      useValue: {
        locals: {
          // Menu localization
          bold: "Bold",
          italic: "Italic",
          code: "Code",
          underline: "Underline",
          strike: "Strike",
          blockquote: "Blockquote",
          bullet_list: "Bullet List",
          ordered_list: "Ordered List",
          heading: "Heading",
          h1: "Header 1",
          h2: "Header 2",
          h3: "Header 3",
          h4: "Header 4",
          h5: "Header 5",
          h6: "Header 6",
          align_left: "Left Align",
          align_center: "Center Align",
          align_right: "Right Align",
          align_justify: "Justify",
          text_color: "Text Color",
          background_color: "Background Color",

          // Link-related localization
          insertLink: "Insert Link",
          removeLink: "Remove Link",
          url: "URL",
          text: "Text",
          openInNewTab: "Open in new tab",
          insert: "Insert",

          // Image-related localization
          insertImage: "Insert Image",
          altText: "Alt Text",
          title: "Title",
          remove: "Remove",
        },
      },
    },
  ],
};
