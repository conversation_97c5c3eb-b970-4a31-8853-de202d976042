import { Routes } from "@angular/router";
import { Roles } from "./core/enums/roles.enum";
import { AuthGuard } from "./features/auth/core/guards/auth.guard";
import { PublicGuard } from "./features/auth/core/guards/public.guard";

// Import Components
import { LoginComponent } from "./features/auth/login/login.component";
import { UnauthorizedComponent } from "./features/auth/unauthorized/unauthorized.component";
import { DashboardLayoutComponent } from "./shared/layouts/dashboard/dashboard-layout.component";

// Lazy-loaded route configurations
const authRoutes: Routes = [
  {
    path: "",
    component: LoginComponent,
    canActivate: [PublicGuard],
  },
  {
    path: "unauthorized",
    component: UnauthorizedComponent,
    canActivate: [AuthGuard],
  },
];

const adminRoutes: Routes = [
  {
    path: "dashboard",
    loadComponent: () =>
      import("./features/dashboard/dashboard.component").then(
        (m) => m.DashboardComponent,
      ),
    canActivate: [AuthGuard],
  },
  {
    path: "admins",
    loadComponent: () =>
      import("./features/admins/admins.component").then(
        (m) => m.AdminsComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
    },
  },
  {
    path: "change-password",
    loadComponent: () =>
      import("./features/auth/change-password/change-password.component").then(
        (m) => m.ChangePasswordComponent,
      ),
    canActivate: [AuthGuard],
  },

  {
    path: "ats/products",
    loadChildren: () =>
      import("./features/ats/products/products.routes").then(
        (m) => m.ATS_PRODUCTS_ROUTES,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
      ],
    },
  },
  {
    path: "products-management",
    loadComponent: () =>
      import("./features/products/products.component").then(
        (m) => m.ProductsComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.FINANCE_ADMIN,
      ],
    },
  },
  {
    path: "clients",
    loadChildren: () =>
      import("./features/clients/clients.routes").then((m) => m.CLIENTS_ROUTES),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.FINANCE_ADMIN,
        Roles.VOT,
      ],
    },
  },
  {
    path: "historical-products",
    loadComponent: () =>
      import(
        "./features/historical-products/historical-products.component"
      ).then((m) => m.HistoricalProductsComponent),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.FINANCE_ADMIN,
        Roles.VOT,
      ],
    },
  },
  {
    path: "addons",
    loadComponent: () =>
      import("./features/addons/addons.component").then(
        (m) => m.AddonsComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.FINANCE_ADMIN,
        Roles.VOT,
      ],
    },
  },
  {
    path: "package-categories",
    loadComponent: () =>
      import("./features/package-categories/package-categories.component").then(
        (m) => m.PackageCategoriesComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
      ],
    },
  },
  {
    path: "product-packages",
    loadComponent: () =>
      import("./features/packages/packages.component").then(
        (m) => m.ProductPackagesComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.FINANCE_ADMIN,
        Roles.VOT,
      ],
    },
  },
  {
    path: "historical-packages",
    loadComponent: () =>
      import(
        "./features/historical-packages/historical-packages.component"
      ).then((m) => m.HistoricalPackagesComponent),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.FINANCE_ADMIN,
        Roles.VOT,
      ],
    },
  },
  {
    path: "periods",
    loadComponent: () =>
      import("./features/periods/periods.component").then(
        (m) => m.PeriodsComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
      ],
    },
  },
  {
    path: "currency",
    loadComponent: () =>
      import("./features/currency/currency.component").then(
        (m) => m.CurrencyComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
    },
  },
  {
    path: "subscriptions",
    loadComponent: () =>
      import("./features/subscriptions/subscriptions.component").then(
        (m) => m.SubscriptionsComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.FINANCE_ADMIN,
        Roles.VOT,
      ],
    },
  },
  {
    path: "subscriptions/initiated",
    loadComponent: () =>
      import("./features/subscriptions/initiated-transactions.component").then(
        (m) => m.InitiatedTransactionsComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.FINANCE_ADMIN,
        Roles.VOT,
      ],
    },
  },
  {
    path: "banks",
    loadComponent: () =>
      import("./features/bank/bank-list.component").then(
        (m) => m.BankListComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
        Roles.OPPS_MANAGER,
      ],
    },
  },
  {
    path: "bank-deposits",
    loadComponent: () =>
      import("./features/bank-deposits/bank-deposits.component").then(
        (m) => m.BankDepositsComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.FINANCE_ADMIN,
        Roles.VOT,
      ],
    },
  },
  {
    path: "banks/create",
    loadComponent: () =>
      import("./features/bank/components/bank-form.component").then(
        (m) => m.BankFormComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
    },
  },
  {
    path: "banks/branches",
    loadComponent: () =>
      import("./features/bank/components/branch-form.component").then(
        (m) => m.BranchFormComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
    },
  },
  {
    path: "products/vot-users",
    loadComponent: () =>
      import("./features/vot/vot-users/vot-users.component").then(
        (m) => m.VotUsersComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.FINANCE_ADMIN,
        Roles.VOT,
      ],
    },
  },

  {
    path: "ats/vot-users",
    loadComponent: () =>
      import("./features/vot/ats-vot-users/ats-vot-users.component").then(
        (m) => m.AtsVotUsersComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.FINANCE_ADMIN,
        Roles.VOT,
      ],
    },
  },

  {
    path: "department",
    loadComponent: () =>
      import("./features/department/department.component").then(
        (m) => m.DepartmentComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
    },
  },

  {
    path: "group",
    loadComponent: () =>
      import("./features/group/group.component").then((m) => m.GroupComponent),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
      ],
    },
  },

  {
    path: "role",
    loadComponent: () =>
      import("./features/role/role.component").then((m) => m.RoleComponent),
    canActivate: [AuthGuard],
    data: {
      roles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
    },
  },
  {
    path: "client-groups",
    loadComponent: () =>
      import("./features/client-groups/client-groups.component").then(
        (m) => m.ClientGroupsComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
      ],
    },
  },
  {
    path: "package-exemptions",
    loadComponent: () =>
      import("./features/package-exemptions/package-exemptions.component").then(
        (m) => m.PackageExemptionsComponent,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
      ],
    },
  },
  {
    path: "historical-data",
    loadChildren: () =>
      import("./features/historical-data/historical-data.routes").then(
        (m) => m.HISTORICAL_DATA_ROUTES,
      ),
    canActivate: [AuthGuard],
    data: {
      roles: [
        Roles.SUPER_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
      ],
    },
  },
];

export const routes: Routes = [
  // Authentication routes
  ...authRoutes,

  // Main admin routes
  {
    path: "admin",
    component: DashboardLayoutComponent,
    canActivate: [AuthGuard],
    children: adminRoutes,
  },

  // Redirect to login for any unmatched routes
  {
    path: "**",
    redirectTo: "",
  },
];
