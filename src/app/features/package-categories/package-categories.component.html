<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter dropdown -->
      <div class="mr-3 flex items-center">
        <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
        <div class="relative flex items-center">
          <i class="pi pi-filter absolute left-2 text-gray-500"></i>
          <select
            [ngModel]="statusFilter"
            (ngModelChange)="updateStatusFilter($event)"
            class="pl-8 pr-4 py-2 border rounded-lg text-sm"
          >
            <option [value]="null">All Statuses</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
        </div>
      </div>
    </ng-template>

    <ng-template #end>
      <div class="flex items-center gap-2">
        <ng-container *appButtonPermission="'create'">
          <app-button size="sm" (click)="toggleModal('addItem')">
            <i class="pi pi-plus mr-1"></i>
            Create
          </app-button>
        </ng-container>

        <app-button
          size="sm"
          class="!gap-2"
          variant="secondary"
          (click)="
            reportExportService.exportToExcel(
              exportData,
              'package-categories.xlsx'
            )
          "
        >
          <i class="text-sm pi pi-upload"></i>
          Export
        </app-button>
      </div>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isDataLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading package categories...</span>
      </div>
    </div>
  } @else {
    <!-- Package Categories Table -->
    <p-table
      [value]="filteredPackageCategories()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
    >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold"
            >Manage Package Categories</app-text
          >
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />
            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search categories..."
            />
          </p-iconfield>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>
          <th pSortableColumn="name">
            <div class="flex items-center justify-between gap-2">
              Name
              <p-sortIcon field="name" />
              <p-columnFilter
                type="text"
                field="name"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="description">
            <div class="flex items-center justify-between gap-2">
              Description
              <p-sortIcon field="description" />
            </div>
          </th>
          <th pSortableColumn="expires">
            <div class="flex items-center justify-between gap-2">
              Expires
              <p-sortIcon field="expires" />
            </div>
          </th>
          <th pSortableColumn="productCategoryType">
            <div class="flex items-center justify-between gap-2">
              Type
              <p-sortIcon field="productCategoryType" />
              <p-columnFilter
                type="text"
                field="productCategoryType"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-packageCategory let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(packageCategory)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ packageCategory.name }}</td>
          <td>{{ packageCategory.description }}</td>
          <td>{{ packageCategory.expires ? "Yes" : "No" }}</td>
          <td>{{ packageCategory.productCategoryType }}</td>
          <td>{{ packageCategory.status }}</td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button
                  size="icon"
                  (click)="toggleModal('viewItem', packageCategory)"
                >
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', packageCategory)"
                >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-packageCategory>
        <tr>
          <td colspan="7">
            <div class="p-4 bg-gray-50 rounded-md">
              <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                <!-- Basic Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">Name</h5>
                  <p class="text-gray-800">{{ packageCategory.name }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Description
                  </h5>
                  <p class="text-gray-800">{{ packageCategory.description }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">
                    {{ packageCategory.status }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Expires
                  </h5>
                  <p class="text-gray-800">
                    {{ packageCategory.expires ? "Yes" : "No" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Category Type
                  </h5>
                  <p class="text-gray-800">
                    {{ packageCategory.productCategoryType }}
                  </p>
                </div>
                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                      packageCategory.createdDate | date: "yyyy-MM-dd HH:mm:ss"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                      packageCategory.modifiedDate
                        ? (packageCategory.modifiedDate
                          | date: "yyyy-MM-dd HH:mm:ss")
                        : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ packageCategory.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ packageCategory.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  (click)="toggleModal('editItem', packageCategory)"
                >
                  Edit
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="7" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No package categories found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- View Modal -->
<app-modal
  [isVisible]="modals.viewItem"
  [config]="{ title: 'View Package Category' }"
>
  <div class="flex flex-col gap-3">
    <div>
      <label class="form-label">Name</label>
      <input
        type="text"
        [value]="selectedPackageCategory?.name"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Description</label>
      <input
        type="text"
        [value]="selectedPackageCategory?.description"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Expires</label>
      <input
        type="text"
        [value]="selectedPackageCategory?.expires ? 'Yes' : 'No'"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Category Type</label>
      <input
        type="text"
        [value]="selectedPackageCategory?.productCategoryType"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Status</label>
      <input
        type="text"
        [value]="selectedPackageCategory?.status"
        class="form-control"
        disabled
      />
    </div>

    <div class="flex justify-end">
      <app-button (click)="toggleModal('viewItem')">Close</app-button>
    </div>
  </div>
</app-modal>

<!-- Add Modal -->
<app-modal
  [isVisible]="modals.addItem"
  [config]="{ title: 'Add Package Category' }"
>
  <form
    [formGroup]="addPackageCategoryForm"
    (ngSubmit)="handlePackageCategoriesubmit('add')"
    class="flex flex-col gap-6"
  >
    <div class="grid gap-3 md:grid-cols-2">
      <app-form-field
        label="Name"
        type="text"
        [control]="addPackageCategoryForm.controls.name"
        [required]="true"
      />

      <app-form-field
        label="Expires"
        placeholder="Expires"
        type="select"
        [options]="[
          { value: false, label: 'No' },
          { value: true, label: 'Yes' },
        ]"
        [control]="addPackageCategoryForm.controls.expires!"
      />

      <app-form-field
        label="Product Package Type"
        placeholder="Product Package Type"
        type="select"
        [options]="[
          { value: ProductCategoryType.LIVE, label: 'Live' },
          { value: ProductCategoryType.HISTORICAL, label: 'Historical' },
          { value: ProductCategoryType.VOT, label: 'VOT' },
        ]"
        [control]="addPackageCategoryForm.controls.productCategoryType!"
      />

      <app-form-field
        label="Description"
        placeholder="Description"
        type="textarea"
        [control]="addPackageCategoryForm.controls.description"
      />
    </div>

    <div class="flex items-center justify-end gap-3">
      <app-button (click)="toggleModal('addItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- Edit Modal -->
<app-modal
  [isVisible]="modals.editItem"
  [config]="{ title: 'Edit Package Category' }"
>
  <form
    [formGroup]="editPackageCategoryForm"
    (ngSubmit)="handlePackageCategoriesubmit('update')"
  >
    <div class="flex flex-col gap-3">
      <app-form-field
        label="Name"
        type="text"
        [control]="editPackageCategoryForm.controls.name!"
        [required]="true"
      />

      <app-form-field
        label="Description"
        placeholder="Description"
        type="textarea"
        [control]="editPackageCategoryForm.controls.description!"
      />

      <app-form-field
        label="Expires"
        placeholder="Expires"
        type="select"
        [options]="[
          { value: false, label: 'No' },
          { value: true, label: 'Yes' },
        ]"
        [control]="editPackageCategoryForm.controls.expires!"
      />

      <app-form-field
        label="Product Package Type"
        placeholder="Product Package Type"
        type="select"
        [options]="[
          { value: ProductCategoryType.LIVE, label: 'Live' },
          { value: ProductCategoryType.HISTORICAL, label: 'Historical' },
          { value: ProductCategoryType.VOT, label: 'VOT' },
        ]"
        [control]="editPackageCategoryForm.controls.productCategoryType!"
      />

      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
        [control]="editPackageCategoryForm.get('status')!"
        [required]="true"
      />
    </div>

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="toggleModal('editItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<p-toast />
