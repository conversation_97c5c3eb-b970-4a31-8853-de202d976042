import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import {
  CreatePackageCategoryRequest,
  UpdatePackageCategoryRequest,
} from "./package-categories.model";

@Injectable({
  providedIn: "root",
})
export class PackageCategoriesService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getPackageCategories(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/package-categories`);
  }

  public createPackageCategory(
    packageCategoryData: CreatePackageCategoryRequest,
  ): Observable<any> {
    return this.http.post<any>(
      `${this.apiServerUrl}/package-categories`,
      packageCategoryData,
    );
  }

  public updatePackageCategory(
    packageCategoryData: UpdatePackageCategoryRequest,
  ): Observable<any> {
    return this.http.put<any>(
      `${this.apiServerUrl}/package-categories`,
      packageCategoryData,
    );
  }
}
