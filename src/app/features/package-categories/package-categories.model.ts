import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { Status } from "@/app/core/enums/status.enum";
import { BaseResponse } from "@/app/core/interfaces/common.interface";

export interface PackageCategory extends BaseResponse {
  name: string;
  expires: boolean;
  description: string;
  productCategoryType: ProductCategoryType;
}

export interface CreatePackageCategoryRequest {
  name: string;
  expires: boolean;
  description: string;
  productCategoryType: ProductCategoryType;
}

export interface UpdatePackageCategoryRequest {
  id: string;
  name?: string;
  description?: string;
  expires?: boolean;
  status?: Status;
  productCategoryType?: ProductCategoryType;
}
