import { DatePipe } from "@angular/common";
import { Component, inject, OnInit, signal } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { TableModule } from "primeng/table";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { Option } from "@/app/core/utils/map-to-options.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { onlyLettersAndHyphensValidator } from "@/app/core/validators/custom.validators";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import {
  CreatePackageCategoryRequest,
  PackageCategory,
  UpdatePackageCategoryRequest,
} from "./package-categories.model";
import { PackageCategoriesService } from "./package-categories.service";

type ModalKey = "addItem" | "editItem" | "viewItem";

@Component({
  selector: "app-packageCategories",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ModalComponent,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    FormFieldComponent,
    ButtonPermissionDirective,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    DatePipe
],
  templateUrl: "./package-categories.component.html",
  providers: [DatePipe],
})
export class PackageCategoriesComponent implements OnInit {
  private packageCategoriesService = inject(PackageCategoriesService);
  private fb = inject(FormBuilder);
  private readonly datePipe = inject(DatePipe);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;

  ProductCategoryType = ProductCategoryType;

  // Use signal for reactive data management
  packageCategories = signal<PackageCategory[]>([]);
  filteredPackageCategories = signal<PackageCategory[]>([]);
  selectedPackageCategory: PackageCategory | null = null;
  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  get packageCategoryTableOptions(): Option[] {
    return [];
  }

  get exchangeOptions(): Option[] {
    return [
      { value: Exchange.VFEX, label: Exchange.VFEX },
      { value: Exchange.ZSE, label: Exchange.ZSE },
    ];
  }

  // Export data formatted for Excel
  get exportData(): any[] {
    return this.filteredPackageCategories().map((cat) => ({
      "Category Name": cat.name,
      Description: cat.description,
      Expires: cat.expires ? "Yes" : "No",
      "Category Type": cat.productCategoryType,
      Status: cat.status,
      "Created Date": this.datePipe.transform(
        cat.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": cat.modifiedDate
        ? this.datePipe.transform(cat.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": cat.createdUserName ?? "-",
      "Modified By": cat.modifiedUserName ?? "-",
    }));
  }

  addPackageCategoryForm = this.fb.nonNullable.group({
    name: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    description: [
      "",
      [
        Validators.minLength(2),
        Validators.maxLength(100),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    expires: [false, [Validators.required]],
    productCategoryType: [ProductCategoryType.LIVE, [Validators.required]],
  }) as FormGroup<FormControlType<CreatePackageCategoryRequest>>;

  editPackageCategoryForm = this.fb.nonNullable.group({
    id: [""],
    name: [
      "",
      [
        Validators.minLength(2),
        Validators.maxLength(50),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    description: [
      "",
      [
        Validators.minLength(2),
        Validators.maxLength(100),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    expires: [false, [Validators.required]],
    productCategoryType: [ProductCategoryType.LIVE, [Validators.required]],
    status: ["", Validators.required],
  }) as FormGroup<FormControlType<UpdatePackageCategoryRequest>>;

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  isDataLoading = false;
  isPackageCategoryTablesLoading = false;

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadPackageCategories();
    });
  }

  toggleRow(packageCategory: PackageCategory): void {
    if (this.expandedRows[packageCategory.id]) {
      delete this.expandedRows[packageCategory.id];
    } else {
      this.expandedRows[packageCategory.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.packageCategories();

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (cat) => cat?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (cat: any) =>
          cat.name.toLowerCase().includes(term) ||
          cat.description?.toLowerCase().includes(term) ||
          false ||
          cat.productCategoryType.toLowerCase().includes(term) ||
          cat.status.toLowerCase().includes(term),
      );
    }

    this.filteredPackageCategories.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  loadPackageCategories(): void {
    this.isDataLoading = true;
    this.packageCategoriesService.getPackageCategories().subscribe({
      next: (response) => {
        this.packageCategories.set(response.data);

        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);

        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load package categories");
        this.isDataLoading = false;
      },
    });
  }

  toggleModal(mode: ModalKey, packageCategory?: PackageCategory): void {
    this.selectedPackageCategory = packageCategory || null;

    if (mode === "editItem" && packageCategory) {
      this.editPackageCategoryForm.patchValue({
        id: packageCategory.id,
        name: packageCategory.name,
        description: packageCategory.description,
        expires: packageCategory.expires,
        productCategoryType: packageCategory.productCategoryType,
        status: packageCategory.status,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  handlePackageCategoriesubmit(mode: "add" | "update"): void {
    const form =
      mode === "add"
        ? this.addPackageCategoryForm
        : this.editPackageCategoryForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.packageCategoriesService.createPackageCategory(
            form.value as CreatePackageCategoryRequest,
          )
        : this.packageCategoriesService.updatePackageCategory(
            form.value as UpdatePackageCategoryRequest,
          );

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Package category has been added successfully`
              : `Package category has been updated successfully`,
          type: "success",
        });
        this.loadPackageCategories();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(
          error,
          `Failed to ${mode} package category: ${error.message}`,
        );
      },
    });
  }

  // Export to Excel function
}
