import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { CreateRoleRequest, UpdateRoleRequest } from "./role.model";

@Injectable({
  providedIn: "root",
})
export class RolesService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getRoles(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/roles`);
  }

  public getActiveRoles(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/roles/active`);
  }

  public createRole(roleData: CreateRoleRequest): Observable<any> {
    return this.http.post<any>(`${this.apiServerUrl}/roles`, roleData);
  }

  public updateRole(roleData: UpdateRoleRequest): Observable<any> {
    return this.http.put<any>(`${this.apiServerUrl}/roles`, roleData);
  }
}
