<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter dropdown -->
      <div class="mr-3 flex items-center">
        <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
        <div class="relative flex items-center">
          <i class="pi pi-filter absolute left-2 text-gray-500"></i>
          <select
            [ngModel]="statusFilter"
            (ngModelChange)="updateStatusFilter($event)"
            class="pl-8 pr-4 py-2 border rounded-lg text-sm"
          >
            <option [value]="null">All Statuses</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
        </div>
      </div>
    </ng-template>

    <ng-template #end>
      <div class="flex gap-2">
        <app-button
          size="sm"
          class="!gap-2"
          variant="secondary"
          (click)="reportExportService.exportToExcel(exportData, 'roles.xlsx')"
        >
          <i class="text-sm pi pi-upload"></i>
          Export
        </app-button>

        <ng-container *appButtonPermission="'create'">
          <app-button size="sm" (click)="toggleModal('addItem')">
            <i class="pi pi-plus mr-1"></i>
            Create
          </app-button>
        </ng-container>
      </div>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isDataLoading || isRoleLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading roles...</span>
      </div>
    </div>
  } @else {
    <!-- Roles Table -->
    <p-table
      [value]="filteredRoles()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
    >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold">Manage Roles</app-text>
          <div class="flex items-center gap-3">
            <p-iconfield>
              <p-inputicon styleClass="pi pi-search" />
              <input
                pSize="small"
                pInputText
                type="text"
                [(ngModel)]="searchTerm"
                (ngModelChange)="onSearch($event)"
                placeholder="Search roles..."
              />
            </p-iconfield>
          </div>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>

          <th pSortableColumn="createdDate">
            <div class="flex items-center gap-2">
              Created Date
              <p-sortIcon field="createdDate" />
            </div>
          </th>

          <th pSortableColumn="role">
            <div class="flex items-center justify-between gap-2">
              Role Name
              <p-sortIcon field="role" />
              <p-columnFilter
                type="text"
                field="role"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="weight">
            <div class="flex items-center justify-between gap-2">
              Weight
              <p-sortIcon field="weight" />
              <p-columnFilter
                type="numeric"
                field="weight"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-role let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(role)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ role.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ role.role }}</td>
          <td>{{ role.weight }}</td>
          <td>{{ role.status }}</td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button size="icon" (click)="toggleModal('viewItem', role)">
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', role)"
                >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-role>
        <tr>
          <td colspan="6">
            <div class="p-4 bg-gray-50 rounded-md">
              <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                <!-- Basic Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Role Name
                  </h5>
                  <p class="text-gray-800">{{ role.role }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Weight
                  </h5>
                  <p class="text-gray-800">{{ role.weight }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">
                    {{ role.status }}
                  </p>
                </div>
                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ role.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                      role.modifiedDate
                        ? (role.modifiedDate | date: "yyyy-MM-dd HH:mm:ss")
                        : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ role.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ role.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  (click)="toggleModal('editItem', role)"
                >
                  Edit
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No roles found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- Add Role Modal -->
<app-modal [isVisible]="modals.addItem" [config]="{ title: 'Add Role' }">
  <form
    [formGroup]="addRoleForm"
    (ngSubmit)="handleRoleSubmit('add')"
    class="flex flex-col gap-6"
  >
    <div class="grid gap-3 md:grid-cols-2">
      <app-form-field
        label="Role Name"
        type="text"
        [control]="addRoleForm.controls.role"
        [required]="true"
        placeholder="Role Name"
      />
      <app-form-field
        label="Weight"
        type="number"
        [control]="addRoleForm.controls.weight"
        [required]="true"
        placeholder="Weight"
      />
    </div>

    <div class="flex items-center justify-end gap-3">
      <app-button (click)="toggleModal('addItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- View Role Modal -->
<app-modal [isVisible]="modals.viewItem" [config]="{ title: 'View Role' }">
  <div class="flex flex-col gap-3">
    <div class="flex flex-col gap-3">
      <div>
        <label class="form-label">Created Date</label>
        <input
          type="text"
          [value]="selectedRole?.createdDate | date: 'yyyy-MM-dd HH:mm:ss'"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Role</label>
        <input
          type="text"
          [value]="selectedRole?.role"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Weight</label>
        <input
          type="text"
          [value]="selectedRole?.weight"
          class="form-control"
          disabled
        />
      </div>

      <div>
        <label class="form-label">Status</label>
        <input
          type="text"
          [value]="selectedRole?.status"
          class="form-control"
          disabled
        />
      </div>

      <div class="flex justify-end">
        <app-button (click)="toggleModal('viewItem')">Close</app-button>
      </div>
    </div>
  </div>
</app-modal>

<!-- Edit Role Modal -->
<app-modal [isVisible]="modals.editItem" [config]="{ title: 'Edit Role' }">
  <form [formGroup]="editRoleForm" (ngSubmit)="handleRoleSubmit('update')">
    <div class="flex flex-col gap-3">
      <app-form-field
        label="Role"
        type="text"
        [control]="editRoleForm.controls.role!"
        [required]="true"
      />
      <app-form-field
        label="Weight"
        type="number"
        [control]="editRoleForm.controls.weight!"
        [required]="true"
      />

      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
        [control]="editRoleForm.get('status')!"
        [required]="true"
      />
    </div>

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="toggleModal('editItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<p-toast />
