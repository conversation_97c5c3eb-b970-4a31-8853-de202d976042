import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { CommonModule, DatePipe } from "@angular/common";
import { Component, inject, OnInit, signal } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye, faShield } from "@fortawesome/free-solid-svg-icons";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { SpinnerComponent } from "../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import { CreateRoleRequest, Role, UpdateRoleRequest } from "./role.model";
import { RolesService } from "./role.service";
import { ReportExportService } from "@/app/core/services/report-export-service";

type ModalKey = "addItem" | "editItem" | "viewItem";

@Component({
  selector: "app-roles",
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    SpinnerComponent,
    DatePipe,
    ModalComponent,
    FormFieldComponent,
    ButtonPermissionDirective,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
  ],
  templateUrl: "./role.component.html",
  providers: [DatePipe],
})
export class RoleComponent implements OnInit {
  private rolesService = inject(RolesService);
  private fb = inject(FormBuilder);
  private datePipe = inject(DatePipe);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;
  readonly passwordIcon = faShield;

  // Use signal pattern for reactive state management
  roles = signal<Role[]>([]);
  filteredRoles = signal<Role[]>([]);

  selectedRole: Role | null = null;
  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  isDataLoading = false;
  isRoleLoading = false;

  get exportData(): any[] {
    return this.filteredRoles().map((role) => ({
      "Role Name": role.role,
      Weight: role.weight,
      Status: role.status,
      "Created Date": this.datePipe.transform(
        role.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": role.modifiedDate
        ? this.datePipe.transform(role.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": role.createdUserName ?? "-",
      "Modified By": role.modifiedUserName ?? "-",
    }));
  }

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadRoles();
    });
  }

  toggleRow(role: Role): void {
    if (this.expandedRows[role.id]) {
      delete this.expandedRows[role.id];
    } else {
      this.expandedRows[role.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.roles();

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (role) => role?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (role: any) =>
          role.role.toLowerCase().includes(term) ||
          role.status.toLowerCase().includes(term) ||
          role.weight.toString().includes(term),
      );
    }

    this.filteredRoles.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  loadRoles() {
    this.isRoleLoading = true;
    this.rolesService.getRoles().subscribe({
      next: (response) => {
        this.roles.set(response.data);

        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);

        this.isRoleLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load roles");
        this.isRoleLoading = false;
      },
    });
  }

  toggleModal(mode: ModalKey, role?: Role): void {
    this.selectedRole = role || null;

    if (mode === "editItem" && role) {
      this.editRoleForm.patchValue({
        id: role.id,
        role: role.role,
        weight: role.weight,
        status: role.status,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  addRoleForm = this.fb.nonNullable.group({
    role: ["", [Validators.required, Validators.minLength(2)]],
    weight: [0, [Validators.required, Validators.min(1)]],
  }) as FormGroup<FormControlType<CreateRoleRequest>>;

  editRoleForm = this.fb.nonNullable.group({
    id: [""],
    role: ["", [Validators.required, Validators.minLength(2)]],
    status: ["", Validators.required],
    weight: [0, [Validators.required, Validators.min(1)]],
  }) as FormGroup<FormControlType<UpdateRoleRequest>>;

  handleRoleSubmit(mode: "add" | "update"): void {
    const form = mode === "add" ? this.addRoleForm : this.editRoleForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.rolesService.createRole(form.value as CreateRoleRequest)
        : this.rolesService.updateRole(form.value as UpdateRoleRequest);

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Role has been added successfully`
              : `Role has been updated successfully`,
          type: "success",
        });
        this.loadRoles();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(error, `Failed to ${mode} roles`);
      },
    });
  }
}
