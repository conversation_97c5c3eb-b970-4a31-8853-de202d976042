import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import {
  CreateDepartmentRequest,
  UpdateDepartmentRequest,
} from "./department.model";

@Injectable({
  providedIn: "root",
})
export class DepartmentsService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getDepartments(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/departments`);
  }

  public getActiveDepartments(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/departments/active`);
  }

  public createDepartment(
    departmentData: CreateDepartmentRequest,
  ): Observable<any> {
    return this.http.post<any>(
      `${this.apiServerUrl}/departments`,
      departmentData,
    );
  }

  public updateDepartment(
    departmentData: UpdateDepartmentRequest,
  ): Observable<any> {
    return this.http.put<any>(
      `${this.apiServerUrl}/departments`,
      departmentData,
    );
  }
}
