<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter dropdown -->
      <div class="mr-3 flex items-center">
        <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
        <div class="relative flex items-center">
          <i class="pi pi-filter absolute left-2 text-gray-500"></i>
          <select
            [ngModel]="statusFilter"
            (ngModelChange)="updateStatusFilter($event)"
            class="pl-8 pr-4 py-2 border rounded-lg text-sm"
            >
            <option [value]="null">All Statuses</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
        </div>
      </div>
    </ng-template>

    <ng-template #end>
      <div class="flex gap-2">
        <app-button
          size="sm"
          class="!gap-2"
          variant="secondary"
          (click)="
            reportExportService.exportToExcel(exportData, 'departments.xlsx')
          "
          >
          <i class="text-sm pi pi-upload"></i>
          Export
        </app-button>

        <app-button
          size="sm"
          (click)="toggleModal('addItem')"
          class="!gap-2"
          *appButtonPermission="'create'"
          >
          <i class="text-sm pi pi-plus"></i>
          Create
        </app-button>
      </div>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isDataLoading || isDepartmentLoading || isRoleLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading departments...</span>
      </div>
    </div>
  } @else {
    <!-- Department Table -->
    <p-table
      [value]="filteredDepartments()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
      >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold">Manage Departments</app-text>
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />
            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search departments..."
              />
          </p-iconfield>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>

          <th pSortableColumn="createdDate">
            <div class="flex items-center gap-2">
              Created Date
              <p-sortIcon field="createdDate" />
            </div>
          </th>

          <th pSortableColumn="department">
            <div class="flex items-center justify-between gap-2">
              Department Name
              <p-sortIcon field="department" />
              <p-columnFilter
                type="text"
                field="department"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>

          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>

          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-department let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(department)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ department.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ department.department }}</td>
          <td>{{ department.status }}</td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button
                  variant="primary"
                  size="icon"
                  (click)="toggleModal('viewItem', department)"
                  >
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', department)"
                  >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-department>
        <tr>
          <td colspan="5">
            <div class="p-4 bg-gray-50 rounded-md">
              <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                <!-- Basic Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Department Name
                  </h5>
                  <p class="text-gray-800">{{ department.department }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">
                    {{ department.status }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ department.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>

                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ department.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                    department.modifiedDate
                    ? (department.modifiedDate
                    | date: "yyyy-MM-dd HH:mm:ss")
                    : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ department.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ department.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Description if available -->
              @if (department.description) {
                <div class="mt-4">
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Description
                  </h5>
                  <p class="text-gray-800 whitespace-pre-line">
                    {{ department.description }}
                  </p>
                </div>
              }

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  (click)="toggleModal('editItem', department)"
                  *appButtonPermission="'edit'"
                  >
                  Edit
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="5" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No departments found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- Add Department Modal -->
<app-modal [isVisible]="modals.addItem" [config]="{ title: 'Add Department' }">
  <form
    [formGroup]="addDepartmentForm"
    (ngSubmit)="handleDepartmentSubmit('add')"
    class="flex flex-col gap-6"
    >
    <div class="grid gap-3 md:grid-cols-2">
      <app-form-field
        label="Department Name"
        type="text"
        [control]="addDepartmentForm.controls.department"
        [required]="true"
        placeholder="Department Name"
        />
    </div>

    <div class="flex items-center justify-end gap-3">
      <app-button (click)="toggleModal('addItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- View Department Modal -->
<app-modal
  [isVisible]="modals.viewItem"
  [config]="{ title: 'View Department' }"
  >
  <div class="flex flex-col gap-3">
    <div class="flex flex-col gap-3">
      <div>
        <label class="form-label">Created Date</label>
        <input
          type="text"
          [value]="
            selectedDepartment?.createdDate | date: 'yyyy-MM-dd HH:mm:ss'
          "
          class="form-control"
          disabled
          />
      </div>
      <div>
        <label class="form-label">Department</label>
        <input
          type="text"
          [value]="selectedDepartment?.department"
          class="form-control"
          disabled
          />
      </div>

      <div>
        <label class="form-label">Status</label>
        <input
          type="text"
          [value]="selectedDepartment?.status"
          class="form-control"
          disabled
          />
      </div>

      <div class="flex justify-end">
        <app-button (click)="toggleModal('viewItem')">Close</app-button>
      </div>
    </div>
  </div>
</app-modal>

<!-- Edit Department Modal -->
<app-modal
  [isVisible]="modals.editItem"
  [config]="{ title: 'Edit Department' }"
  >
  <form
    [formGroup]="editDepartmentForm"
    (ngSubmit)="handleDepartmentSubmit('update')"
    >
    <div class="flex flex-col gap-3">
      <app-form-field
        label="Department"
        type="text"
        [control]="editDepartmentForm.controls.department!"
        [required]="true"
        />

      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
        [control]="editDepartmentForm.get('status')!"
        [required]="true"
        />
    </div>

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="toggleModal('editItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<p-toast />
