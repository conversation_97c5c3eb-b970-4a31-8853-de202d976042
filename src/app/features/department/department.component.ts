import { Component, inject, OnInit, signal } from "@angular/core";
import {
  Form<PERSON>uilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye, faShield } from "@fortawesome/free-solid-svg-icons";
import { TableModule } from "primeng/table";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { onlyLettersAndHyphensValidator } from "@/app/core/validators/custom.validators";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { CommonModule, DatePipe } from "@angular/common";
import { ActivatedRoute, Router } from "@angular/router";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import {
  CreateDepartmentRequest,
  Department,
  UpdateDepartmentRequest,
} from "../department/department.model";
import { DepartmentsService } from "../department/department.service";
import { Role } from "../role/role.model";

type ModalKey = "addItem" | "editItem" | "viewItem";

@Component({
  selector: "app-departments",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    DatePipe,
    ModalComponent,
    FormFieldComponent,
    ButtonPermissionDirective,
    CommonModule,
    ToastModule,
    ButtonModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
  ],
  templateUrl: "./department.component.html",
  providers: [DatePipe],
})
export class DepartmentComponent implements OnInit {
  private departmentsService = inject(DepartmentsService);
  private fb = inject(FormBuilder);
  private datePipe = inject(DatePipe);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;
  readonly passwordIcon = faShield;

  // Use signals for reactive state
  filteredDepartments = signal<Department[]>([]);
  departments = signal<Department[]>([]);

  // Export data getter
  get exportData(): any[] {
    return this.filteredDepartments().map((dept) => ({
      "Department Name": dept.department,
      Status: dept.status,
      "Created Date": this.datePipe.transform(
        dept.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": dept.modifiedDate
        ? this.datePipe.transform(dept.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": dept.createdUserName ?? "-",
      "Modified By": dept.modifiedUserName ?? "-",
    }));
  }

  roles: Role[] = [];
  selectedDepartment: Department | null = null;
  statusFilter: string | null = null;

  isDataLoading = false;
  isDepartmentLoading = false;
  isRoleLoading = false;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadDepartments();
    });
  }

  loadDepartments() {
    this.isDepartmentLoading = true;
    this.departmentsService.getDepartments().subscribe({
      next: (response) => {
        this.departments.set(response.data);

        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);

        this.isDepartmentLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load departments");
        this.isDepartmentLoading = false;
      },
    });
  }

  toggleRow(department: Department): void {
    if (this.expandedRows[department.id]) {
      delete this.expandedRows[department.id];
    } else {
      this.expandedRows[department.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.departments();

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (dep) => dep?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (dep: any) =>
          dep.department.toLowerCase().includes(term) ||
          dep.status.toLowerCase().includes(term) ||
          dep.createdDate.toString().includes(term),
      );
    }

    this.filteredDepartments.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  toggleModal(mode: ModalKey, department?: Department): void {
    this.selectedDepartment = department || null;

    if (mode === "editItem" && department) {
      this.editDepartmentForm.patchValue({
        id: department.id,
        department: department.department,
        status: department.status,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  addDepartmentForm = this.fb.nonNullable.group({
    department: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        onlyLettersAndHyphensValidator(),
      ],
    ],
  }) as FormGroup<FormControlType<CreateDepartmentRequest>>;

  editDepartmentForm = this.fb.nonNullable.group({
    id: [""],
    department: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    status: ["", Validators.required],
  }) as FormGroup<FormControlType<UpdateDepartmentRequest>>;

  handleDepartmentSubmit(mode: "add" | "update"): void {
    const form =
      mode === "add" ? this.addDepartmentForm : this.editDepartmentForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.departmentsService.createDepartment(
            form.value as CreateDepartmentRequest,
          )
        : this.departmentsService.updateDepartment(
            form.value as UpdateDepartmentRequest,
          );

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Department has been added successfully`
              : `Department has been updated successfully`,
          type: "success",
        });
        this.loadDepartments();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(error, `Failed to ${mode} departments`);
      },
    });
  }
}
