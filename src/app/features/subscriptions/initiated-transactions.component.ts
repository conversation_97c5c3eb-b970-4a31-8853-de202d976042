import { Component, inject, OnInit, signal } from "@angular/core";
import {
  Form<PERSON>uilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { TableModule } from "primeng/table";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { handleError } from "@/app/core/utils/error-handler.util";
import { Option } from "@/app/core/utils/map-to-options.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { CommonModule, DatePipe } from "@angular/common";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import Swal from "sweetalert2";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import { Subscription, UpdatePackageSubscription } from "./subscriptions.model";
import { SubscriptionService } from "./subscriptions.service";
import { ReportExportService } from "@/app/core/services/report-export-service";

type ModalKey = "addItem" | "editItem" | "viewItem";

@Component({
  selector: "app-subscriptions",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    DatePipe,
    CommonModule,
    FormsModule,
    TableModule,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    CardComponent,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    ButtonComponent,
    TextComponent,
    ModalComponent,
    FormFieldComponent,
    ButtonPermissionDirective,
  ],
  templateUrl: "./initiated-transactions.component.html",
  providers: [DatePipe],
})
export class InitiatedTransactionsComponent implements OnInit {
  private readonly subscriptionService = inject(SubscriptionService);
  private readonly fb = inject(FormBuilder);
  private readonly datePipe = inject(DatePipe);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;

  filteredSubscriptions = signal<Subscription[]>([]);

  subscriptions = signal<Subscription[]>([]);
  subscriptionTables: any[] = [];
  selectedSubscription: Subscription | null = null;

  get exportData(): any[] {
    return this.filteredSubscriptions().map((sub) => ({
      "Subscription Name": sub.name,
      "Reference Number": sub.refNo,
      Status: sub.status,
      Amount: sub.amount,
      "Expiry Date": this.datePipe.transform(sub.expiryDate, "yyyy-MM-dd"), // Format expiryDate
      "End Date": this.datePipe.transform(sub.endDate, "yyyy-MM-dd"), // Format endDate
      "Expiry Period": sub.expiryPeriod,
      "Client First Name": sub.client.firstName,
      "Client Last Name": sub.client.lastName,
      "Product Package": sub.productPackage.name,
      "Created Date": this.datePipe.transform(
        sub.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": sub.modifiedDate
        ? this.datePipe.transform(sub.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": sub.createdUserName ?? "-",
      "Modified By": sub.modifiedUserName ?? "-",
    }));
  }

  get subscriptionTableOptions(): Option[] {
    return this.subscriptionTables.map((table) => ({
      value: table,
      label: table,
    }));
  }

  get exchangeOptions(): Option[] {
    return [
      { value: Exchange.VFEX, label: Exchange.VFEX },
      { value: Exchange.ZSE, label: Exchange.ZSE },
    ];
  }

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  isDataLoading = false;
  isSubscriptionTablesLoading = false;

  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  ngOnInit(): void {
    this.loadData();
  }

  toggleRow(sub: Subscription): void {
    if (this.expandedRows[sub.id]) {
      delete this.expandedRows[sub.id];
    } else {
      this.expandedRows[sub.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    if (!term) {
      this.filteredSubscriptions.set(this.subscriptions());
      return;
    }

    const filtered = this.subscriptions().filter(
      (sub) =>
        sub.name.toLowerCase().includes(term.toLowerCase()) ||
        sub.refNo.toLowerCase().includes(term.toLowerCase()) ||
        sub.client.firstName.toLowerCase().includes(term.toLowerCase()) ||
        sub.client.lastName.toLowerCase().includes(term.toLowerCase()) ||
        sub.productPackage.name.toLowerCase().includes(term.toLowerCase()) ||
        sub.expiryDate.toString().includes(term.toLowerCase()),
    );
    this.filteredSubscriptions.set(filtered);
  }

  toggleModal(mode: ModalKey, subscription?: Subscription): void {
    this.selectedSubscription = subscription || null;
    if (mode === "editItem" && subscription) {
      this.editPackageSubscriptionForm.patchValue({
        id: subscription.id,
        name: subscription.name,
        refNo: subscription.refNo,
        status: subscription.status,
        amount: subscription.amount,
        expiryDate: subscription.expiryDate,
        endDate: subscription.endDate,
        expiryPeriod: subscription.expiryPeriod,
      });
    }
    this.modals[mode] = !this.modals[mode];
  }

  loadData(): void {
    this.isDataLoading = true;
    this.subscriptionService.getInitiatedTransactions().subscribe({
      next: (response) => {
        const processedData = response.data.map(
          (subscription: Subscription) => ({
            ...subscription,
            expiryDate: this.convertToDate(subscription.expiryDate),
          }),
        );
        this.subscriptions.set(processedData);
        this.filteredSubscriptions.set(processedData);

        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load subscriptions");
        this.isDataLoading = false;
      },
    });
  }

  editPackageSubscriptionForm = this.fb.nonNullable.group({
    id: ["", [Validators.required]],
    name: ["", [Validators.required]],
    refNo: ["", [Validators.required]],
    status: ["", Validators.required],
    amount: [0, [Validators.required, Validators.min(0)]],
    expiryDate: [0, [Validators.required]],
    endDate: [null as string | null],
    expiryPeriod: [null as number | null],
  }) as FormGroup<FormControlType<UpdatePackageSubscription>>;

  handlePackageSubscriptionSubmit(): void {
    if (this.editPackageSubscriptionForm.invalid) {
      markFormGroupTouched(this.editPackageSubscriptionForm);
      return;
    }

    Swal.fire({
      title: "Are you sure?",
      text: "You want to activate this subscription?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, activate it!",
    }).then((result) => {
      if (result.isConfirmed) {
        this.subscriptionService
          .updatePackageSubscription(
            this.editPackageSubscriptionForm.value as UpdatePackageSubscription,
          )
          .subscribe({
            next: () => {
              showToast({
                message: `Subscription has been updated successfully`,
                type: "success",
              });
              this.loadData();
              this.toggleModal("editItem");
              this.editPackageSubscriptionForm.reset();
            },
            error: (error) => {
              handleError(error, `Failed to update product: ${error.message}`);
            },
          });
      }
    });
  }

  convertToDate(date: any): Date | null {
    if (date === null || date === undefined) {
      return null;
    }
    return typeof date === "number" ? new Date(date * 1000) : new Date(date);
  }
}
