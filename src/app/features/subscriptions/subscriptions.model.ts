import { BaseResponse } from "@/app/core/interfaces/common.interface";
import { Addon } from "../addons/addons.model";
import { Client } from "../clients/clients.model";
import { Package } from "../packages/packages.model";
import { Period } from "../periods/periods.model";
import { Product } from "../products/products.model";

export interface Subscription extends BaseResponse {
  name: string;
  client: Client;
  refNo: string;
  productPackage: Package;
  productCategoryType: string | null;
  peiod: Period;
  addons: string[];
  amount: number;
  expiryDate: number;
  endDate: string | null;
  pollUrl: string | null;
  subscribedProducts: Product[] | null;
  subscribedAddons: Addon[] | null;
  subscribedPeriod: Period | null;
  expiryPeriod: number | null;
}

export interface UpdatePackageSubscription {
  id: string;
  name: string;
  refNo: string;
  amount: number;
  expiryDate: number;
  endDate: string | null;
  expiryPeriod: number | null;
  status: string;
}
