import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { UpdatePackageSubscription } from "./subscriptions.model";

@Injectable({
  providedIn: "root",
})
export class SubscriptionService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getSubscriptions(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/package-subscriptions`);
  }

  public getInitiatedTransactions(): Observable<any> {
    return this.http.get<any>(
      `${this.apiServerUrl}/package-subscriptions/initiated`,
    );
  }

  public updatePackageSubscription(
    data: UpdatePackageSubscription,
  ): Observable<any> {
    return this.http.put<any>(
      `${this.apiServerUrl}/package-subscriptions`,
      data,
    );
  }
}
