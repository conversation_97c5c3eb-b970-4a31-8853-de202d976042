<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- <app-button size="sm" class="!gap-2">
      <i class="text-sm pi pi-plus"></i>
      New Subscription
    </app-button> -->
  </ng-template>

  <ng-template #end>
    <app-button
      size="sm"
      class="!gap-2"
      variant="secondary"
        (click)="
          reportExportService.exportToExcel(
            exportData,
            'initiates-transactions.xlsx'
          )
        "
      >
      <i class="text-sm pi pi-upload"></i>
      Export
    </app-button>
  </ng-template>
</p-toolbar>

<!-- Loading State -->
@if (isDataLoading || isSubscriptionTablesLoading) {
  <div class="flex items-center justify-center p-8">
    <div class="flex flex-col items-center gap-4">
      <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
      <span class="text-gray-600">Loading initiated transactions...</span>
    </div>
  </div>
} @else {
  <!-- Subscription Table -->
  <p-table
    [value]="filteredSubscriptions()"
    [paginator]="true"
    [rows]="10"
    [rowsPerPageOptions]="[10, 25, 50]"
    dataKey="id"
    [expandedRowKeys]="expandedRows"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    [showCurrentPageReport]="true"
    styleClass="p-datatable-elegant"
    >
    <ng-template #caption>
      <div class="flex flex-wrap items-center justify-between gap-2">
        <app-text variant="title/semibold">Initiated Transactions</app-text>
        <p-iconfield>
          <p-inputicon styleClass="pi pi-search" />

          <input
            pSize="small"
            pInputText
            type="text"
            [(ngModel)]="searchTerm"
            (ngModelChange)="onSearch($event)"
            placeholder="Search initiated transactions..."
            />
        </p-iconfield>
      </div>
    </ng-template>

    <!-- Table Header -->
    <ng-template pTemplate="header">
      <tr class="bg-gray-50 text-nowrap">
        <th style="width: 4rem"></th>

        <th pSortableColumn="createdDate">
          <div class="flex items-center gap-2">
            Created Date
            <p-sortIcon field="createdDate" />
          </div>
        </th>

        <th pSortableColumn="name">
          <div class="flex items-center justify-between gap-2">
            Name
            <p-sortIcon field="name" />
            <p-columnFilter
              type="text"
              field="name"
              display="menu"
              class="ml-auto"
              />
          </div>
        </th>

        <th pSortableColumn="client.firstName">
          <div class="flex items-center justify-between gap-2">
            Client
            <p-sortIcon field="client.firstName" />
            <p-columnFilter
              type="text"
              field="client.firstName"
              display="menu"
              class="ml-auto"
              />
          </div>
        </th>

        <th pSortableColumn="refNo">
          <div class="flex items-center justify-between gap-2">
            Reference No.
            <p-sortIcon field="refNo" />
            <p-columnFilter
              type="text"
              field="refNo"
              display="menu"
              class="ml-auto"
              />
          </div>
        </th>

        <th pSortableColumn="productPackage.name">
          <div class="flex items-center justify-between gap-2">
            Package
            <p-sortIcon field="productPackage.name" />
            <p-columnFilter
              type="text"
              field="productPackage.name"
              display="menu"
              class="ml-auto"
              />
          </div>
        </th>

        <th pSortableColumn="period.name">
          <div class="flex items-center justify-between gap-2">
            Period
            <p-sortIcon field="period.name" />
            <p-columnFilter
              type="text"
              field="period.name"
              display="menu"
              class="ml-auto"
              />
          </div>
        </th>

        <th pSortableColumn="amount">
          <div class="flex items-center justify-between gap-2">
            Amount
            <p-sortIcon field="amount" />
            <p-columnFilter
              type="numeric"
              field="amount"
              display="menu"
              class="ml-auto"
              />
          </div>
        </th>

        <th pSortableColumn="expiryDate">
          <div class="flex items-center justify-between gap-2">
            Expiry Date
            <p-sortIcon field="expiryDate" />
            <p-columnFilter
              type="date"
              field="expiryDate"
              display="menu"
              class="ml-auto"
              />
          </div>
        </th>

        <th pSortableColumn="endDate">
          <div class="flex items-center justify-between gap-2">
            End Date
            <p-sortIcon field="endDate" />
            <p-columnFilter
              type="date"
              field="endDate"
              display="menu"
              class="ml-auto"
              />
          </div>
        </th>

        <th pSortableColumn="status">
          <div class="flex items-center justify-between gap-2">
            Status
            <p-sortIcon field="status" />
            <p-columnFilter
              type="text"
              field="status"
              display="menu"
              class="ml-auto"
              />
          </div>
        </th>

        <th>Actions</th>
      </tr>
    </ng-template>

    <!-- Table Body -->
    <ng-template pTemplate="body" let-subscription let-expanded="expanded">
      <tr>
        <td>
          <button
            type="button"
            pButton
            pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
            (click)="toggleRow(subscription)"
            class="p-button-text p-button-rounded p-button-plain"
            [class.expanded]="expanded"
          ></button>
        </td>
        <td>{{ subscription.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
        <td>{{ subscription.name }}</td>
        <td>
          {{ subscription.client.firstName }}
          {{ subscription.client.lastName }}
        </td>
        <td>{{ subscription.refNo }}</td>
        <td>{{ subscription.productPackage.name }}</td>
        <td class="text-nowrap">
          {{ subscription.period.length }}
          {{ subscription.period.name }} (s)
        </td>
        <td>{{ subscription.amount }}</td>
        <td>{{ subscription.expiryDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
        <td>{{ subscription.endDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
        <td>{{ subscription.status }}</td>
        <td>
          <div class="flex gap-2">
            <ng-container *appButtonPermission="'edit'">
              <app-button
                variant="success"
                size="icon"
                (click)="toggleModal('editItem', subscription)"
                >
                <fa-icon [icon]="editIcon"></fa-icon>
              </app-button>
            </ng-container>
          </div>
        </td>
      </tr>
    </ng-template>

    <!-- Expanded Row Template -->
    <ng-template pTemplate="rowexpansion" let-subscription>
      <tr>
        <td colspan="6">
          <div class="p-4 bg-gray-50 rounded-md">
            <div
              class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
              >
              <!-- Basic Information -->
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">Name</h5>
                <p class="text-gray-800">{{ subscription.name }}</p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Reference No.
                </h5>
                <p class="text-gray-800">{{ subscription.refNo }}</p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Status
                </h5>
                <p class="text-gray-800">
                  {{ subscription.status }}
                </p>
              </div>

              <!-- Client Information -->
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Client
                </h5>
                <p class="text-gray-800">
                  {{ subscription.client.firstName }}
                  {{ subscription.client.lastName }}
                </p>
              </div>

              <!-- Package & Period Information -->
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Package
                </h5>
                <p class="text-gray-800">
                  {{ subscription.productPackage.name }}
                </p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Period
                </h5>
                <p class="text-gray-800">
                  {{ subscription.period.length }}
                  {{ subscription.period.name }}(s)
                </p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Amount
                </h5>
                <p class="text-gray-800">
                  {{ subscription.amount | currency }}
                </p>
              </div>

              <!-- Dates Information -->
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Expiry Date
                </h5>
                <p class="text-gray-800">
                  {{ subscription.expiryDate | date: "yyyy-MM-dd HH:mm:ss" }}
                </p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  End Date
                </h5>
                <p class="text-gray-800">
                  {{ subscription.endDate | date: "yyyy-MM-dd HH:mm:ss" }}
                </p>
              </div>

              <!-- Audit -->
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Created Date
                </h5>
                <p class="text-gray-800">
                  {{ subscription.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                </p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Modified Date
                </h5>
                <p class="text-gray-800">
                  {{
                  subscription.modifiedDate
                  ? (subscription.modifiedDate
                  | date: "yyyy-MM-dd HH:mm:ss")
                  : "-"
                  }}
                </p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Created By
                </h5>
                <p class="text-gray-800">
                  {{ subscription.createdUserName ?? "-" }}
                </p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Modified By
                </h5>
                <p class="text-gray-800">
                  {{ subscription.modifiedUserName ?? "-" }}
                </p>
              </div>
            </div>

            <!-- Description if available -->
            @if (subscription.description) {
              <div class="mt-4">
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Description
                </h5>
                <p class="text-gray-800 whitespace-pre-line">
                  {{ subscription.description }}
                </p>
              </div>
            }

            <!-- Products if available -->
            @if (subscription.subscribedProducts?.length) {
              <div class="mt-4">
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Products
                </h5>
                <p class="text-gray-800 whitespace-pre-line">
                  @for (
                    product of subscription.subscribedProducts; track
                    product; let last = $last) {
                    <span
                      >
                      {{ product.name }}@if (!last) {
                      <span>, </span>
                    }
                  </span>
                }
              </p>
            </div>
          }

          <!-- Addons if available -->
          @if (subscription.subscribedAddons?.length) {
            <div class="mt-4">
              <h5 class="text-sm font-semibold text-gray-600 mb-1">Addons</h5>
              <p class="text-gray-800 whitespace-pre-line">
                @for (
                  addon of subscription.subscribedAddons; track
                  addon; let last = $last) {
                  <span
                    >
                    {{ addon.name }}@if (!last) {
                    <span>, </span>
                  }
                </span>
              }
            </p>
          </div>
        }

        <!-- Action Buttons -->
        <div class="mt-4 flex justify-end">
          <app-button
            size="sm"
            variant="secondary"
            icon="pi pi-pencil"
            (click)="toggleModal('editItem', subscription)"
            >
            Edit
          </app-button>
        </div>
      </div>
    </td>
  </tr>
</ng-template>
<!-- Empty State -->
<ng-template pTemplate="emptymessage">
  <tr>
    <td colspan="12" class="p-8 text-center">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl text-gray-400 pi pi-inbox"></i>
        <span class="text-gray-600">No initiated transactions found</span>
      </div>
    </td>
  </tr>
</ng-template>
</p-table>
}
</app-card>

<!-- View Subscription Modal -->
<app-modal
  [isVisible]="modals.viewItem"
  [config]="{ title: 'View Subscription' }"
  >
  <div class="flex flex-col gap-3">
    <div class="flex justify-end">
      <app-button (click)="toggleModal('viewItem')">Close</app-button>
    </div>
  </div>
</app-modal>

<app-modal
  [isVisible]="modals.editItem"
  [config]="{ title: 'Manage Package Subscription' }"
  >
  <form
    [formGroup]="editPackageSubscriptionForm"
    (ngSubmit)="handlePackageSubscriptionSubmit()"
    >
    <div class="grid grid-cols-2 gap-4">
      <div>
        <label class="form-label">Package Name</label>
        <input
          class="form-control"
          type="text"
          [value]="selectedSubscription?.name"
          disabled
          />
      </div>
      <div>
        <label class="form-label">Reference Number</label>
        <input
          class="form-control"
          type="text"
          [value]="selectedSubscription?.refNo"
          disabled
          />
      </div>
      <div>
        <label class="form-label">Amount</label>
        <input
          class="form-control"
          type="text"
          [value]="selectedSubscription?.amount"
          disabled
          />
      </div>
      <div>
        <label class="form-label">Expiry Date</label>
        <input
          class="form-control"
          type="text"
          [value]="
            selectedSubscription?.expiryDate | date: 'yyyy-MM-dd HH:mm:ss'
          "
          disabled
          />
      </div>
      <div>
        <label class="form-label">End Date</label>
        <input
          class="form-control"
          type="text"
          [value]="selectedSubscription?.endDate"
          disabled
          />
      </div>

      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'INACTIVE', label: 'Inactive' },
          { value: 'DROPPED', label: 'Activate Paynow Dropped Transaction' },
        ]"
        [control]="editPackageSubscriptionForm.get('status')!"
        [required]="true"
        />
    </div>

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="toggleModal('editItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<p-toast />
