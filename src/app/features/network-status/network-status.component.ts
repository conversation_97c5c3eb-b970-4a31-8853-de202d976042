
import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faWifi } from "@fortawesome/free-solid-svg-icons";
import { TextComponent } from "../../shared/ui/text/text.component";

@Component({
  selector: "app-network-status",
  standalone: true,
  imports: [TextComponent, FontAwesomeModule],
  template: `
    <!-- Offline notification -->
    @if (isOffline) {
      <div
        class="fixed inset-0 bg-background flex flex-col items-center justify-center z-50"
        >
        <div
          class="p-8 rounded-lg max-w-md flex flex-col items-center justify-center text-center"
          >
          <div
            class="w-24 h-24 mb-6 bg-purple-100 rounded-full flex items-center justify-center"
            >
            <fa-icon [icon]="wifiIcon" class="text-4xl text-purple-600"></fa-icon>
          </div>
          <app-text variant="subHeading/semibold" class="mb-2">
            Connect to the internet
          </app-text>
          <app-text variant="small/normal" class="mb-6 text-muted">
            You're offline. Check your connection.
          </app-text>
          <button
            (click)="retry()"
            class="px-6 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
            >
            Retry
          </button>
        </div>
      </div>
    }
    
    <!-- Back online notification -->
    @if (showOnlineMessage) {
      <div
        class="fixed inset-x-0 bottom-0 z-50 bg-green-600 text-white py-2 text-center"
        >
        <span class="font-semibold">Back online</span>
      </div>
    }
    `,
})
export class NetworkStatusComponent implements OnInit, OnDestroy {
  wifiIcon = faWifi;
  isOffline = false;
  showOnlineMessage = false;
  private onlineTimer: any = null;
  private checkInterval: any = null;

  // Bind event handlers once to avoid issues with event removal
  private readonly onlineListener = this.handleOnline.bind(this);
  private readonly offlineListener = this.handleOffline.bind(this);

  ngOnInit() {
    this.isOffline = !navigator.onLine;

    window.addEventListener("online", this.onlineListener);
    window.addEventListener("offline", this.offlineListener);

    // Periodically check actual internet connectivity
    this.checkInterval = setInterval(
      this.checkInternetConnection.bind(this),
      5000,
    );
  }

  ngOnDestroy() {
    window.removeEventListener("online", this.onlineListener);
    window.removeEventListener("offline", this.offlineListener);

    if (this.onlineTimer) clearTimeout(this.onlineTimer);
    if (this.checkInterval) clearInterval(this.checkInterval);
  }

  handleOnline() {
    this.checkInternetConnection();
  }

  handleOffline() {
    console.log("Offline detected");
    this.isOffline = true;
    this.showOnlineMessage = false;

    if (this.onlineTimer) {
      clearTimeout(this.onlineTimer);
      this.onlineTimer = null;
    }
  }

  async checkInternetConnection() {
    try {
      // Try fetching a known reliable endpoint (Google or your API)
      const response = await fetch("https://www.google.com/generate_204", {
        method: "HEAD",
        cache: "no-store",
        mode: "no-cors",
      });

      if (response) {
        if (this.isOffline) this.showReconnectionMessage();
        this.isOffline = false;
      }
    } catch (error) {
      if (!this.isOffline) this.handleOffline();
    }
  }

  showReconnectionMessage() {
    console.log("Back online");
    this.isOffline = false;
    this.showOnlineMessage = true;

    if (this.onlineTimer) clearTimeout(this.onlineTimer);
    this.onlineTimer = setTimeout(() => (this.showOnlineMessage = false), 3000);
  }

  retry(): void {
    // Force the browser to check connection again
    window.location.reload();
  }
}
