import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/app/core/interfaces/common.interface";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { onlyLettersAndHyphensValidator } from "@/app/core/validators/custom.validators";
import { ButtonComponent } from "@/app/shared/ui/button/button.component";
import { CardComponent } from "@/app/shared/ui/card/card.component";
import { FormFieldComponent } from "@/app/shared/ui/form-field/form-field.component";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { ModalComponent } from "@/app/shared/ui/modal/modal.component";
import { TextComponent } from "@/app/shared/ui/text/text.component";
import { CommonModule, DatePipe } from "@angular/common";
import { Component, OnInit, inject } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import { CreateGroupRequest, Group, UpdateGroupRequest } from "./group.model";
import { GroupService } from "./group.service";

@Component({
  selector: "app-client-groups",
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    DatePipe,
    ModalComponent,
    FormFieldComponent,
    ButtonPermissionDirective,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
  ],
  templateUrl: "./group.component.html",
  providers: [DatePipe],
})
export class GroupComponent implements OnInit {
  private groupService = inject(GroupService);
  private fb = inject(FormBuilder);
  private datePipe = inject(DatePipe);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;

  groups: Group[] = [];
  filteredGroups: Group[] = [];
  selectedGroup: Group | null = null;
  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  isGroupsLoading = false;

  get exportData(): any[] {
    return this.filteredGroups.map((group) => ({
      "Group Name": group.name,
      Status: group.status,
      "Created Date": this.datePipe.transform(
        group.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": group.modifiedDate
        ? this.datePipe.transform(group.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": group.createdUserName ?? "-",
      "Modified By": group.modifiedUserName ?? "-",
    }));
  }

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadGroups();
    });
  }

  loadGroups() {
    this.isGroupsLoading = true;
    this.groupService.getGroups().subscribe({
      next: (response) => {
        this.groups = response.data;
        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);
        this.isGroupsLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load client groups");
        this.isGroupsLoading = false;
      },
    });
  }

  toggleRow(group: Group): void {
    if (this.expandedRows[group.id]) {
      delete this.expandedRows[group.id];
    } else {
      this.expandedRows[group.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.groups;

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (group) => group?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (group: any) =>
          group.name.toLowerCase().includes(term) ||
          group.status.toLowerCase().includes(term) ||
          (group.createdDate && group.createdDate.toString().includes(term)),
      );
    }

    this.filteredGroups = filtered;
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  toggleModal(mode: ModalKey, group?: Group): void {
    this.selectedGroup = group || null;

    if (mode === "editItem" && group) {
      this.editGroupForm.patchValue({
        id: group.id,
        name: group.name,
        status: group.status,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  addGroupForm = this.fb.nonNullable.group({
    name: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        onlyLettersAndHyphensValidator(),
      ],
    ],
  }) as FormGroup<FormControlType<CreateGroupRequest>>;

  editGroupForm = this.fb.nonNullable.group({
    id: [""],
    name: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    status: ["", Validators.required],
  }) as FormGroup<FormControlType<UpdateGroupRequest>>;

  handleGroupSubmit(mode: "add" | "update"): void {
    const form = mode === "add" ? this.addGroupForm : this.editGroupForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.groupService.createGroup(form.value as CreateGroupRequest)
        : this.groupService.updateGroup(form.value as UpdateGroupRequest);

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Group has been added successfully`
              : `Group has been updated successfully`,
          type: "success",
        });
        this.loadGroups();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(error, `Failed to ${mode} groups`);
      },
    });
  }
}
