import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { CreateGroupRequest, UpdateGroupRequest } from "./group.model";

@Injectable({
  providedIn: "root",
})
export class GroupService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getGroups(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/groups`);
  }

  public getActiveGroups(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/groups/active`);
  }

  public createGroup(groupData: CreateGroupRequest): Observable<any> {
    return this.http.post<any>(`${this.apiServerUrl}/groups`, groupData);
  }

  public updateGroup(groupData: UpdateGroupRequest): Observable<any> {
    return this.http.put<any>(`${this.apiServerUrl}/groups`, groupData);
  }
}
