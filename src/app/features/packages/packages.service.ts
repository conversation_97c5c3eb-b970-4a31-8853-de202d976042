import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { CreatePackagePayload, UpdatePackagePayload } from "./packages.model";

@Injectable({
  providedIn: "root",
})
export class PackagesService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getPackages(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/product-packages`);
  }

  public createPackage(packageData: CreatePackagePayload): Observable<any> {
    return this.http.post<any>(
      `${this.apiServerUrl}/product-packages`,
      packageData,
    );
  }

  public updatePackage(packageData: UpdatePackagePayload): Observable<any> {
    return this.http.put<any>(
      `${this.apiServerUrl}/product-packages`,
      packageData,
    );
  }
}
