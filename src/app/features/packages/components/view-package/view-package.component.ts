import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/app/core/interfaces/common.interface";
import { CurrencyPipe, NgClass } from "@angular/common";
import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { Package } from "../../packages.model";

@Component({
  selector: "app-view-package",
  templateUrl: "./view-package.component.html",
  standalone: true,
  imports: [
    NgClass,
    ButtonModule,
    DialogModule,
    ButtonComponent,
    CurrencyPipe,
    ButtonPermissionDirective,
    FontAwesomeModule
],
})
export class ViewPackageComponent implements OnInit {
  @Input({ required: true })
  package!: any;
  @Output() modalEvent = new EventEmitter<{
    mode: ModalKey;
    productPackage?: Package | null;
  }>();

  constructor() {}

  ngOnInit(): void {
    // Additional initialization logic if needed
  }

  closeModal(): void {
    this.modalEvent.emit({ mode: "viewItem" });
  }

  editPackage(): void {
    this.modalEvent.emit({ mode: "editItem", productPackage: this.package });
  }
}
