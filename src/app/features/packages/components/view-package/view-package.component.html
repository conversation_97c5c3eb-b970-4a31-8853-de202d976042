<div class="p-4">
  <!-- Package Information Card -->
  <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
    <div class="flex justify-between items-start mb-4">
      <div>
        <h2 class="text-2xl font-semibold text-gray-800">
          {{ package?.name }}
        </h2>
        <p class="text-gray-600 mt-1">
          {{ package?.productPackageCategoryName }}
        </p>
      </div>
      <div class="flex items-center">
        <span
          class="px-3 py-1 rounded-full text-sm"
          [ngClass]="{
            'bg-green-100 text-green-800': package?.status === 'ACTIVE',
            'bg-red-100 text-red-800': package?.status === 'INACTIVE',
          }"
          >
          {{ package?.status }}
        </span>
        @if (package?.isRecommended) {
          <span
            class="ml-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
            >
            Recommended
          </span>
        }
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-4">
      <div>
        <h3 class="text-sm font-medium text-gray-500">Category</h3>
        <p class="mt-1 text-gray-900">
          {{ package?.productPackageCategoryName }}
        </p>
      </div>
      <div>
        <h3 class="text-sm font-medium text-gray-500">Type</h3>
        <p class="mt-1 text-gray-900">{{ package?.productCategoryType }}</p>
      </div>
      <div>
        <h3 class="text-sm font-medium text-gray-500">Base Price</h3>
        <p class="mt-1 text-gray-900">{{ package?.price | currency: "USD" }}</p>
      </div>
      <div>
        <h3 class="text-sm font-medium text-gray-500">Expiry Period</h3>
        <p class="mt-1 text-gray-900">{{ package?.expiryPeriod }} days</p>
      </div>
      <div>
        <h3 class="text-sm font-medium text-gray-500">Currency</h3>
        <p class="mt-1 text-gray-900">{{ package?.currencyCode || "USD" }}</p>
      </div>
    </div>

    <!-- Description -->
    @if (package?.description) {
      <div class="mb-6">
        <h3 class="text-sm font-medium text-gray-500 mb-2">Description</h3>
        <p class="text-gray-700 whitespace-pre-line">
          {{ package?.description }}
        </p>
      </div>
    }
  </div>

  <!-- Pricing Options -->
  <h3 class="text-lg font-medium text-gray-900 mb-3">Pricing Options</h3>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
    @for (period of package?.periods; track period) {
      <div
        class="bg-white rounded-lg shadow-sm border p-4 hover:shadow-md transition-shadow"
        [ngClass]="{ 'border-blue-500': period.isDefault }"
        >
        <div class="flex justify-between items-start">
          <div>
            <span class="text-2xl font-bold text-gray-900">{{
              period.price | currency: "USD"
            }}</span>
            <span class="text-gray-600 ml-1"
              >/{{ period.length }} {{ period.name }}(s)</span
              >
            </div>
            @if (period.isDefault) {
              <span
                class="text-blue-600 text-xs font-medium uppercase"
                >Default</span
                >
              }
            </div>
            @if (period.discount > 0) {
              <div class="mt-2">
                <span class="text-green-600 text-sm font-medium"
                  >Save {{ period.discount }}%</span
                  >
                  <span class="ml-2 line-through text-gray-500 text-sm">
                    {{ package?.price * period.length | currency: "USD" }}
                  </span>
                </div>
              }
            </div>
          }
        </div>

        <!-- Included Products -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Products List -->
          <div class="bg-white rounded-lg shadow-sm border p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              Included Products ({{ package?.products?.length || 0 }})
            </h3>
            @if (package?.products && package?.products.length > 0) {
              <div
                class="space-y-3"
                >
                @for (product of package?.products; track product) {
                  <div
                    class="flex items-center p-3 rounded-md bg-gray-50"
                    >
                    <span class="text-green-500 mr-2">
                      <i class="pi pi-check-circle"></i>
                    </span>
                    <div>
                      <p class="text-sm font-medium text-gray-800">{{ product.name }}</p>
                      @if (product.description) {
                        <p class="text-xs text-gray-600 mt-1">
                          {{ product.description }}
                        </p>
                      }
                    </div>
                  </div>
                }
              </div>
            }
            @if (!package?.products || package?.products.length === 0) {
              <p
                class="text-gray-500 text-sm"
                >
                No products included in this package.
              </p>
            }
          </div>

          <!-- Addons List -->
          <div class="bg-white rounded-lg shadow-sm border p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              Available Addons ({{ package?.addons?.length || 0 }})
            </h3>
            @if (package?.addons && package?.addons.length > 0) {
              <div
                class="space-y-3"
                >
                @for (addon of package?.addons; track addon) {
                  <div
                    class="flex items-center p-3 rounded-md bg-gray-50"
                    >
                    <span class="text-blue-500 mr-2">
                      <i class="pi pi-plus-circle"></i>
                    </span>
                    <div>
                      <p class="text-sm font-medium text-gray-800">{{ addon.name }}</p>
                      @if (addon.description) {
                        <p class="text-xs text-gray-600 mt-1">
                          {{ addon.description }}
                        </p>
                      }
                    </div>
                  </div>
                }
              </div>
            }
            @if (!package?.addons || package?.addons.length === 0) {
              <p
                class="text-gray-500 text-sm"
                >
                No addons available for this package.
              </p>
            }
          </div>
        </div>

        <!-- Action Buttons -->
        <!-- <div class="flex justify-end gap-3 mt-6">
        <app-button variant="outline" (click)="closeModal()">Close</app-button>
        <app-button
          variant="outline"
          *appButtonPermission="'create'"
          (click)="editPackage()"
          >
          <i class="pi pi-pencil mr-2"></i>
          Edit Package
        </app-button>
      </div> -->
    </div>
