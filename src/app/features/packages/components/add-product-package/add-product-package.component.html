<form
  [formGroup]="form"
  (ngSubmit)="addProductPackage()"
  class="flex flex-col gap-6"
>
  <div class="flex flex-col gap-2">
    <app-text variant="title/semibold">General Information</app-text>
    <div class="grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-5">
      <app-form-field
        label="Package Category"
        type="select"
        [options]="packageCategoriesOptions"
        [control]="form.controls.productPackageCategoryId"
        [required]="true"
      />

      <app-form-field
        label="Package Name"
        type="text"
        placeholder="Package Name"
        [control]="form.controls.name"
        [required]="true"
      />

      <app-form-field
        label="Set as Recommended"
        type="select"
        [options]="[
          { label: 'No', value: false },
          { label: 'Yes', value: true },
        ]"
        [control]="form.controls.isRecommended"
        [required]="true"
      />

      <app-form-field
        label="Set the Price for a month"
        type="number"
        step="0.01"
        (input)="onPriceChange()"
        [control]="form.controls.price"
        [required]="true"
      />

      <app-form-field
        label="Expiry Period (Days)"
        type="number"
        [control]="form.controls.expiryPeriod"
        [required]="true"
      />
    </div>
  </div>

  <hr />

  <div class="flex flex-col gap-2">
    <app-text variant="title/semibold">Configure Periods</app-text>

    <div class="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-6">
      <app-form-field
        label="Period"
        type="select"
        [options]="periodsOptions"
        [control]="form.controls.selectedPeriodId"
        [required]="true"
      />

      <app-form-field
        label="Multiplier"
        type="number"
        [control]="form.controls.multiplier"
        [required]="true"
      />

      <app-form-field
        label="Price"
        type="number"
        [control]="form.controls.initPrice"
        [required]="true"
      />

      <app-form-field
        label="Discount (%)"
        type="number"
        [control]="form.controls.discount"
        (input)="onDiscountChange()"
        [required]="true"
        min="0"
        max="100"
      />

      <app-form-field
        label="Final Price"
        type="number"
        [control]="form.controls.finalPrice"
        [required]="true"
      />

      <div class="flex items-end">
        <app-button
          type="button"
          (click)="onAdd()"
          [disabled]="
            form.get('selectedPeriodId')?.invalid ||
            !form.get('selectedPeriodId')?.value
          "
        >
          Add
        </app-button>
      </div>
    </div>
  </div>

  @if (packagePeriods.length > 0) {
    <app-text variant="title/semibold">Configure Periods</app-text>

    <div class="modern-scrollbar">
      <table class="table w-full">
        <thead
          class="text-xs font-semibold tracking-wider text-gray-600 uppercase bg-gray-100 text-nowrap"
        >
          <tr>
            <th class="text-start">isDefault</th>
            <th class="text-start">Period</th>
            <th class="text-start">Price</th>
            <th class="text-start">Discount</th>
            <th class="text-start">Actions</th>
          </tr>
        </thead>

        <tbody>
          @for (period of packagePeriods; track period.id; let i = $index) {
            <tr>
              <td>
                <input
                  type="radio"
                  name="isDefault"
                  [id]="'isDefault-' + i"
                  [checked]="period.isDefault"
                  (change)="onDefaultChange(period)"
                />
              </td>
              <td>{{ period.periodLength }} {{ period.periodName }}(s)</td>
              <td>{{ period.price }}</td>
              <td>{{ period.discount }} %</td>
              <td>
                <app-button
                  type="button"
                  (click)="onRemove(i)"
                  variant="danger"
                >
                  Remove
                </app-button>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }

  <hr />

  <div class="grid gap-3 md:grid-cols-2">
    <div class="flex flex-col gap-3">
      <app-text variant="title/semibold">Select Products</app-text>

      <table class="table">
        <thead
          class="text-xs font-semibold tracking-wider text-gray-600 uppercase bg-gray-100 text-nowrap"
        >
          <tr>
            <th class="text-start">Product</th>
            <th class="text-start">Actions</th>
          </tr>
        </thead>
        <tbody>
          @for (product of products; track product) {
            <tr>
              <td>{{ product.name }}</td>
              <td>
                <app-button
                  type="button"
                  size="sm"
                  [variant]="isProductAdded(product) ? 'danger' : 'primary'"
                  (click)="
                    isProductAdded(product)
                      ? onRemoveProduct(product)
                      : onAddProduct(product)
                  "
                >
                  {{ isProductAdded(product) ? "Remove" : "Add" }}
                </app-button>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>

    <div class="flex flex-col gap-3">
      <app-text variant="title/semibold">Select Addons</app-text>

      <table class="table">
        <thead
          class="text-xs font-semibold tracking-wider text-gray-600 uppercase bg-gray-100 text-nowrap"
        >
          <tr>
            <th class="text-start">Addon</th>
            <th class="text-start">Actions</th>
          </tr>
        </thead>
        <tbody>
          @for (addon of addons; track addon) {
            <tr>
              <td>{{ addon.name }}</td>
              <td>
                <app-button
                  type="button"
                  size="sm"
                  [variant]="isAddonAdded(addon) ? 'danger' : 'primary'"
                  (click)="
                    isAddonAdded(addon)
                      ? onRemoveAddon(addon)
                      : onAddAddon(addon)
                  "
                >
                  {{ isAddonAdded(addon) ? "Remove" : "Add" }}
                </app-button>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
  <hr />

  <div class="flex items-center justify-end gap-3 mt-4">
    <app-button variant="outline" (click)="toggleModal()"> Cancel </app-button>
    <app-button type="submit" [loading]="isLoading">Submit</app-button>
  </div>
</form>
