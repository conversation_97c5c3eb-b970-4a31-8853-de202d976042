import { <PERSON><PERSON><PERSON><PERSON> } from "@/app/core/interfaces/common.interface";
import { handleError } from "@/app/core/utils/error-handler.util";
import { ProductsService } from "@/app/features/products/products.service";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";

import { Component, EventEmitter, inject, Output } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { Router } from "@angular/router";
import Swal from "sweetalert2";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { FormFieldComponent } from "../../../../shared/ui/form-field/form-field.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { AddonsService } from "../../../addons/addons.service";
import { PackageCategoriesService } from "../../../package-categories/package-categories.service";
import { PeriodsService } from "../../../periods/periods.service";
import {
  CreatePackagePayload,
  CreatePackageRequest,
  Package,
} from "../../packages.model";
import { PackagesService } from "../../packages.service";

interface ModalEvent {
  mode: ModalKey;
  productPackage?: Package | null;
}

@Component({
  selector: "app-add-product-package",
  standalone: true,
  imports: [
    FormsModule,
    TextComponent,
    ButtonComponent,
    ReactiveFormsModule,
    FormFieldComponent
],
  templateUrl: "./add-product-package.component.html",
})
export class AddProductPackageComponent {
  @Output() modalEvent = new EventEmitter<ModalEvent>();

  @Output() loadPackagesEvent = new EventEmitter();

  packageCategories: any[] = [];
  periods: any[] = [];
  products: any[] = [];
  addons: any[] = [];
  multiplier: number = 0;
  price: number = 0;
  initPrice: number = 0;
  finalPrice: number = 0;
  discount: number = 0;
  packagePeriods: any[] = [];
  packageProducts: any[] = [];
  packageAddons: any[] = [];

  isLoading: boolean = false;

  toggleModal(): void {
    this.modalEvent.emit({ mode: "addItem" });
  }

  get packageCategoriesOptions() {
    return this.packageCategories.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  }

  get periodsOptions() {
    return this.periods.map((item) => ({
      label: `${item.length} ${item.name}(s)`,
      value: item.id,
    }));
  }

  private fb = inject(FormBuilder);

  form = this.fb.nonNullable.group({
    name: ["", [Validators.required, Validators.minLength(3)]],
    price: [0, [Validators.required, Validators.min(0)]],
    expiryPeriod: [0, [Validators.required, Validators.min(0)]],
    isRecommended: [false, []],
    productPackageCategoryId: ["", [Validators.required]],
    selectedPeriodId: ["", [Validators.required]],
    multiplier: [
      { value: 0, disabled: true },
      [Validators.required, Validators.min(0)],
    ],
    initPrice: [
      { value: 0, disabled: true },
      [Validators.required, Validators.min(0)],
    ],
    discount: [0, [Validators.required, Validators.min(0)]],
    finalPrice: [
      { value: 0, disabled: true },
      [Validators.required, Validators.min(0)],
    ],
  }) as FormGroup<FormControlType<CreatePackageRequest>>;

  constructor(
    private router: Router,
    private packageCategoriesService: PackageCategoriesService,
    private addonsService: AddonsService,
    private productsService: ProductsService,
    private periodsService: PeriodsService,
    private productPackagesService: PackagesService,
  ) {}

  ngOnInit(): void {
    this.getPackageCategories();
    this.getProducts();
    this.getAddons();
    this.getPeriods();
    this.form.get("selectedPeriodId")?.valueChanges.subscribe(() => {
      this.onPeriodChange();
    });
  }

  getPackageCategories() {
    this.packageCategoriesService
      .getPackageCategories()
      .subscribe((response) => {
        this.packageCategories = response.data;
      });
  }

  getProducts() {
    this.productsService.getProducts().subscribe((response) => {
      this.products = response.data;
    });
  }

  getAddons() {
    this.addonsService.getAddons().subscribe((response) => {
      this.addons = response.data;
    });
  }

  getPeriods() {
    this.periodsService.getPeriods().subscribe((response) => {
      this.periods = response.data;
    });
  }

  onPeriodChange() {
    const selectedPeriod = this.periods.find(
      (period) => period.id === this.form.get("selectedPeriodId")?.value,
    );

    if (selectedPeriod) {
      const price = this.form.get("price")?.value || 0;
      const discount = this.form.get("discount")?.value || 0;

      const multiplier = selectedPeriod.multiplier;

      // Calculate initial price based on multiplier
      const initPrice = price * multiplier;

      // Calculate final price based on discount
      let finalPrice = initPrice;
      if (discount > 0) {
        finalPrice = initPrice - initPrice * (discount / 100);
      }

      // Update form controls
      this.form.patchValue({
        multiplier,
        initPrice,
        finalPrice,
      });
    }
  }

  onPriceChange() {
    this.onPeriodChange(); // Recalculate when the price changes
  }

  onDiscountChange() {
    this.onPeriodChange(); // Recalculate when the discount changes
  }

  onAdd() {
    // Safely get the selected period ID from the form
    const selectedPeriodId = this.form.get("selectedPeriodId")?.value;

    // Check for existing period
    const exists = this.packagePeriods.some(
      (item) => item.id === selectedPeriodId,
    );

    if (exists) {
      Swal.fire({
        icon: "warning",
        title: "Duplicate Period",
        text: "You cannot add the same period twice!",
      });
      return; // Exit the method to prevent further execution
    }

    // Safely find the selected period
    const selectedPeriod = this.periods.find(
      (period) => period.id === selectedPeriodId,
    );

    // Additional null check for selectedPeriod
    if (!selectedPeriod) {
      Swal.fire({
        icon: "error",
        title: "Invalid Selection",
        text: "Please select a valid period.",
      });
      return;
    }

    // Safely get form values with null coalescing
    const newPackagePeriod = {
      id: selectedPeriod.id,
      periodName: selectedPeriod.name,
      periodLength: selectedPeriod.length,
      discount: this.form.get("discount")?.value ?? 0,
      price: this.form.get("finalPrice")?.value ?? 0,
      isDefault: false,
    };

    this.packagePeriods.push(newPackagePeriod);
  }

  // Optional: Extract reset logic to a separate method
  private resetFormFields() {
    this.packagePeriods = [];
    this.packageProducts = [];
    this.packageAddons = [];
  }

  onRemove(index: number) {
    this.packagePeriods.splice(index, 1);
  }

  onAddProduct(product: any) {
    const exists = this.packageProducts.some((p) => p.id === product.id);
    if (!exists) {
      this.packageProducts.push(product);
    }
  }

  onRemoveProduct(product: any) {
    this.packageProducts = this.packageProducts.filter(
      (p) => p.id !== product.id,
    );
  }

  isProductAdded(product: any): boolean {
    return this.packageProducts.some((p) => p.id === product.id);
  }

  onAddAddon(addon: any) {
    const exists = this.packageAddons.some((a) => a.id === addon.id);
    if (!exists) {
      this.packageAddons.push(addon);
    }
  }

  onRemoveAddon(addon: any) {
    this.packageAddons = this.packageAddons.filter((a) => a.id !== addon.id);
  }

  isAddonAdded(addon: any): boolean {
    return this.packageAddons.some((a) => a.id === addon.id);
  }

  addProductPackage() {
    if (this.form.valid) {
      if (!this.packagePeriods.length) {
        Swal.fire({
          icon: "error",
          title: "Invalid Form",
          text: "Please add at least one period.",
        });
        return;
      }

      if (!this.packageProducts.length) {
        Swal.fire({
          icon: "error",
          title: "Invalid Form",
          text: "Please add at least one product.",
        });
        return;
      }

      if (!this.packagePeriods.some((period) => period.isDefault)) {
        Swal.fire({
          icon: "error",
          title: "Invalid Form",
          text: "Please select a default period.",
        });
        return;
      }
      const formValues = this.form.value as CreatePackageRequest;

      const payload: CreatePackagePayload = {
        name: formValues.name,
        price: formValues.price,
        expiryPeriod: formValues.expiryPeriod,
        isRecommended: formValues.isRecommended,
        productPackageCategoryId: formValues.productPackageCategoryId,
        products: this.packageProducts,
        addons: this.packageAddons,
        periods: this.packagePeriods,
      };

      this.isLoading = true;
      this.productPackagesService.createPackage(payload).subscribe({
        next: () => {
          this.form.reset();
          this.resetFormFields();
          this.loadPackagesEvent.emit();
          this.toggleModal();
          this.isLoading = false;

          Swal.fire({
            icon: "success",
            title: "Success",
            text: "Package has been created successfully.",
          });
        },
        error: (error) => {
          handleError(error);
          this.isLoading = false;
        },
      });
    } else {
      Swal.fire({
        icon: "error",
        title: "Invalid Form",
        text: "Please fix the form errors.",
      });
      markFormGroupTouched(this.form);
    }
  }

  onDefaultChange(selectedPeriod: any) {
    if (selectedPeriod.isDefault) {
      this.packagePeriods.forEach((period) => {
        if (period !== selectedPeriod) {
          period.isDefault = false;
        }
      });
    } else {
      const hasDefault = this.packagePeriods.some((period) => period.isDefault);
      if (!hasDefault) {
        selectedPeriod.isDefault = true;
      }
    }
  }
}
