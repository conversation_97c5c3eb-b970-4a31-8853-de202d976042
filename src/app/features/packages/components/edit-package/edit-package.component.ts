import { <PERSON><PERSON><PERSON><PERSON> } from "@/app/core/interfaces/common.interface";
import { handleError } from "@/app/core/utils/error-handler.util";
import { ProductsService } from "@/app/features/products/products.service";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";

import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { Router } from "@angular/router";
import Swal from "sweetalert2";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { FormFieldComponent } from "../../../../shared/ui/form-field/form-field.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { AddonsService } from "../../../addons/addons.service";
import { PackageCategoriesService } from "../../../package-categories/package-categories.service";
import { PeriodsService } from "../../../periods/periods.service";
import {
  Package,
  UpdatePackagePayload,
  UpdatePackageRequest,
} from "../../packages.model";
import { PackagesService } from "../../packages.service";

interface ModalEvent {
  mode: ModalKey;
  productPackage?: Package | null;
}

@Component({
  selector: "app-edit-product-package",
  standalone: true,
  imports: [
    FormsModule,
    TextComponent,
    ButtonComponent,
    ReactiveFormsModule,
    FormFieldComponent
],
  templateUrl: "./edit-package.component.html",
})
export class EditPackageComponent implements OnInit, OnChanges {
  @Input() packageToEdit?: Package | null;
  @Output() modalEvent = new EventEmitter<ModalEvent>();
  @Output() loadPackagesEvent = new EventEmitter<void>();

  packageCategories: any[] = [];
  periods: any[] = [];
  products: any[] = [];
  addons: any[] = [];

  packagePeriods: any[] = [];
  packageProducts: any[] = [];
  packageAddons: any[] = [];

  isLoading: boolean = false;
  mode: ModalKey = "addItem";

  private fb = inject(FormBuilder);

  form = this.fb.nonNullable.group({
    name: ["", [Validators.minLength(3)]],
    price: [0, [Validators.min(0)]],
    expiryPeriod: [0, [Validators.min(0)]],
    isRecommended: [false, []],
    productPackageCategoryId: ["", []],
    selectedPeriodId: ["", []],
    multiplier: [{ value: 0, disabled: true }, [Validators.min(0)]],
    initPrice: [{ value: 0, disabled: true }, [Validators.min(0)]],
    discount: [0, [Validators.min(0)]],
    finalPrice: [{ value: 0, disabled: true }, [Validators.min(0)]],
    status: [""],
    id: [""],
  }) as FormGroup<FormControlType<UpdatePackageRequest>>;

  constructor(
    private router: Router,
    private packageCategoriesService: PackageCategoriesService,
    private addonsService: AddonsService,
    private productsService: ProductsService,
    private periodsService: PeriodsService,
    private productPackagesService: PackagesService,
  ) {}

  ngOnInit(): void {
    this.loadInitialData();
    this.setupFormListeners();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes["packageToEdit"] && this.packageToEdit) {
      this.initializeEditMode();
    }
  }

  private loadInitialData(): void {
    this.getPackageCategories();
    this.getProducts();
    this.getAddons();
    this.getPeriods();
  }

  private setupFormListeners(): void {
    this.form.get("selectedPeriodId")?.valueChanges.subscribe(() => {
      this.onPeriodChange();
    });

    this.form.get("price")?.valueChanges.subscribe(() => {
      this.onPriceChange();
    });

    this.form.get("discount")?.valueChanges.subscribe(() => {
      this.onDiscountChange();
    });
  }

  private initializeEditMode(): void {
    this.mode = "editItem";
    this.resetForm();
    this.populateFormForEdit();
  }

  private resetForm(): void {
    this.form.reset({
      name: "",
      price: 0,
      expiryPeriod: 0,
      isRecommended: false,
      productPackageCategoryId: "",
      selectedPeriodId: "",
      multiplier: 0,
      initPrice: 0,
      discount: 0,
      finalPrice: 0,
    });

    this.packagePeriods = [];
    this.packageProducts = [];
    this.packageAddons = [];
  }

  get packageCategoriesOptions() {
    return this.packageCategories.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  }

  get periodsOptions() {
    return this.periods.map((item) => ({
      label: `${item.length} ${item.name}(s)`,
      value: item.id,
    }));
  }

  private populateFormForEdit(): void {
    if (!this.packageToEdit) return;

    const selectedPeriod = this.packageToEdit.periods.find(
      (period) => period.isDefault,
    );

    this.form.patchValue({
      name: this.packageToEdit.name,
      price: this.packageToEdit.price,
      expiryPeriod: this.packageToEdit.expiryPeriod,
      isRecommended: this.packageToEdit.isRecommended,
      productPackageCategoryId: this.packageToEdit.productPackageCategoryId,
      selectedPeriodId: selectedPeriod?.id,
      multiplier: selectedPeriod?.multiplier,
      initPrice: selectedPeriod?.price,
      discount: selectedPeriod?.discount,
      finalPrice: selectedPeriod?.price,
      status: selectedPeriod?.status,
    });

    this.packagePeriods = [...this.packageToEdit.periods];
    this.packageProducts = [...this.packageToEdit.products];
    this.packageAddons = [...this.packageToEdit.addons];
  }

  private getPackageCategories() {
    this.packageCategoriesService.getPackageCategories().subscribe({
      next: (response) => {
        this.packageCategories = response.data;
      },
      error: (error) => handleError(error),
    });
  }

  private getProducts() {
    this.productsService.getProducts().subscribe({
      next: (response) => {
        this.products = response.data;
      },
      error: (error) => handleError(error),
    });
  }

  private getAddons() {
    this.addonsService.getAddons().subscribe({
      next: (response) => {
        this.addons = response.data;
      },
      error: (error) => handleError(error),
    });
  }

  private getPeriods() {
    this.periodsService.getPeriods().subscribe({
      next: (response) => {
        this.periods = response.data;
      },
      error: (error) => handleError(error),
    });
  }

  onPeriodChange() {
    const selectedPeriodId = this.form.get("selectedPeriodId")?.value;
    const selectedPeriod = this.periods.find(
      (period) => period.id === selectedPeriodId,
    );

    if (!selectedPeriod) return;

    const price = this.form.get("price")?.value || 0;
    const discount = this.form.get("discount")?.value || 0;
    const multiplier = selectedPeriod.multiplier;

    const initPrice = price * multiplier;
    const finalPrice =
      discount > 0 ? initPrice - initPrice * (discount / 100) : initPrice;

    this.form.patchValue(
      {
        multiplier,
        initPrice,
        finalPrice,
      },
      { emitEvent: false },
    );
  }

  onPriceChange() {
    this.onPeriodChange();
  }

  onDiscountChange() {
    this.onPeriodChange();
  }

  onAdd() {
    const selectedPeriodId = this.form.get("selectedPeriodId")?.value;

    const exists = this.packagePeriods.some(
      (item) => item.id === selectedPeriodId,
    );

    if (exists) {
      Swal.fire({
        icon: "warning",
        title: "Duplicate Period",
        text: "You cannot add the same period twice!",
      });
      return;
    }

    const selectedPeriod = this.periods.find(
      (period) => period.id === selectedPeriodId,
    );

    if (!selectedPeriod) {
      Swal.fire({
        icon: "error",
        title: "Invalid Selection",
        text: "Please select a valid period.",
      });
      return;
    }

    const newPackagePeriod = {
      id: selectedPeriod.id,
      periodName: selectedPeriod.name,
      periodLength: selectedPeriod.length,
      discount: this.form.get("discount")?.value ?? 0,
      price: this.form.get("finalPrice")?.value ?? 0,
      isDefault: this.packagePeriods.length === 0, // First period is default
    };

    this.packagePeriods.push(newPackagePeriod);
    this.resetFormFields();
  }

  private resetFormFields() {
    this.form.patchValue({
      selectedPeriodId: "",
      multiplier: 0,
      initPrice: 0,
      discount: 0,
      finalPrice: 0,
    });
  }

  editProductPackage() {
    if (this.form.invalid) {
      markFormGroupTouched(this.form);
      return;
    }

    if (!this.packagePeriods.length) {
      Swal.fire({
        icon: "error",
        title: "Invalid Form",
        text: "Please add at least one period.",
      });
      return;
    }

    if (!this.packageProducts.length) {
      Swal.fire({
        icon: "error",
        title: "Invalid Form",
        text: "Please add at least one product.",
      });
      return;
    }

    if (!this.packagePeriods.some((period) => period.isDefault)) {
      Swal.fire({
        icon: "error",
        title: "Invalid Form",
        text: "Please select a default period.",
      });
      return;
    }

    const formValues = this.form.value as UpdatePackagePayload;
    const payload: UpdatePackagePayload = {
      id: this!.packageToEdit!.id,
      name: formValues.name,
      price: formValues.price,
      expiryPeriod: formValues.expiryPeriod,
      isRecommended: formValues.isRecommended,
      productPackageCategoryId: formValues.productPackageCategoryId,
      products: this.packageProducts,
      addons: this.packageAddons,
      periods: this.packagePeriods,
      status: formValues.status,
    };

    const serviceMethod =
      this.mode === "editItem"
        ? this.productPackagesService.updatePackage(payload)
        : this.productPackagesService.createPackage(payload);

    this.isLoading = true;
    serviceMethod.subscribe({
      next: () => {
        this.form.reset();
        this.loadPackagesEvent.emit();
        this.toggleModal();
        Swal.fire({
          icon: "success",
          title: "Success",
          text:
            this.mode === "editItem"
              ? "Package has been updated successfully."
              : "Package has been created successfully.",
        });
        this.isLoading = false;
      },
      error: (error) => {
        handleError(error);
        this.isLoading = false;
      },
    });
  }

  toggleModal(): void {
    this.modalEvent.emit({
      mode: this.mode,
      productPackage: this.packageToEdit,
    });
  }

  onDefaultChange(selectedPeriod: any) {
    this.packagePeriods.forEach((period) => {
      period.isDefault = period === selectedPeriod;
    });
  }

  onRemove(index: number) {
    this.packagePeriods.splice(index, 1);

    // Ensure at least one period remains default if possible
    if (
      !this.packagePeriods.some((p) => p.isDefault) &&
      this.packagePeriods.length > 0
    ) {
      this.packagePeriods[0].isDefault = true;
    }
  }

  onAddProduct(product: any) {
    if (!this.isProductAdded(product)) {
      this.packageProducts.push(product);
    }
  }

  onRemoveProduct(product: any) {
    this.packageProducts = this.packageProducts.filter(
      (p) => p.id !== product.id,
    );
  }

  isProductAdded(product: any): boolean {
    return this.packageProducts.some((p) => p.id === product.id);
  }

  onAddAddon(addon: any) {
    if (!this.isAddonAdded(addon)) {
      this.packageAddons.push(addon);
    }
  }

  onRemoveAddon(addon: any) {
    this.packageAddons = this.packageAddons.filter((a) => a.id !== addon.id);
  }

  isAddonAdded(addon: any): boolean {
    return this.packageAddons.some((a) => a.id === addon.id);
  }
}
