import { Component, inject, OnInit, signal } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { TableModule } from "primeng/table";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { ModalKey } from "@/app/core/interfaces/common.interface";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { Option } from "@/app/core/utils/map-to-options.util";
import { CurrencyPipe, DatePipe } from "@angular/common";
import { ActivatedRoute, Router } from "@angular/router";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { SpinnerComponent } from "../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import { AddProductPackageComponent } from "./components/add-product-package/add-product-package.component";
import { EditPackageComponent } from "./components/edit-package/edit-package.component";
import { ViewPackageComponent } from "./components/view-package/view-package.component";
import { Package } from "./packages.model";
import { PackagesService } from "./packages.service";

@Component({
  selector: "app-packages",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ModalComponent,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    SpinnerComponent,
    ViewPackageComponent,
    AddProductPackageComponent,
    EditPackageComponent,
    ButtonPermissionDirective,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    ButtonModule,
    CurrencyPipe,
    DatePipe,
  ],
  providers: [DatePipe],
  templateUrl: "./packages.component.html",
})
export class ProductPackagesComponent implements OnInit {
  private packagesService = inject(PackagesService);
  private readonly datePipe = inject(DatePipe);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;

  packages: Package[] = [];
  filteredPackages = signal<Package[]>([]);
  packageTables: any[] = [];
  selectedPackage: Package | null = null;
  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  get exportData(): any[] {
    return this.filteredPackages().map((pkg) => ({
      "Package Name": pkg.name,
      Category: pkg.productPackageCategoryName,
      Type: pkg.productCategoryType,
      "Monthly Price": pkg.price,
      Recommended: pkg.isRecommended ? "Yes" : "No",
      Status: pkg.status,
      "Expiry Period (Days)": pkg.expiryPeriod,
      "Number of Products": pkg.products?.length || 0,
      "Number of Addons": pkg.addons?.length || 0,
      "Number of Periods": pkg.periods?.length || 0,
      "Created Date": this.datePipe.transform(
        pkg.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": pkg.modifiedDate
        ? this.datePipe.transform(pkg.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": pkg.createdUserName ?? "-",
      "Modified By": pkg.modifiedUserName ?? "-",
    }));
  }

  get packageTableOptions(): Option[] {
    return this.packageTables.map((table) => ({
      value: table,
      label: table,
    }));
  }

  get exchangeOptions(): Option[] {
    return [
      { value: Exchange.VFEX, label: Exchange.VFEX },
      { value: Exchange.ZSE, label: Exchange.ZSE },
    ];
  }

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  isDataLoading = false;
  isPackageTablesLoading = false;

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadPackages();
    });
  }

  loadPackages(): void {
    this.isDataLoading = true;
    this.packagesService.getPackages().subscribe({
      next: (response) => {
        this.packages = response.data;

        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);

        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load packages");
        this.isDataLoading = false;
      },
    });
  }

  toggleModal(mode: ModalKey, productPackage?: Package | null): void {
    this.selectedPackage = productPackage || null;
    this.modals[mode] = !this.modals[mode];
  }

  toggleRow(pkg: Package): void {
    if (this.expandedRows[pkg.id]) {
      delete this.expandedRows[pkg.id];
    } else {
      this.expandedRows[pkg.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.packages;

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (pkg) => pkg?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (pkg) =>
          pkg.name.toLowerCase().includes(term) ||
          pkg.productPackageCategoryName.toLowerCase().includes(term) ||
          pkg.productCategoryType.toLowerCase().includes(term) ||
          pkg.price.toString().includes(term),
      );
    }

    this.filteredPackages.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }
}
