import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { Status } from "@/app/core/enums/status.enum";
import { BaseResponse } from "@/app/core/interfaces/common.interface";
import { Addon } from "../addons/addons.model";
import { Product } from "../products/products.model";

export interface Package extends BaseResponse {
  name: string;
  description: string;
  price: number;
  expiryPeriod: number;
  isRecommended: boolean;
  productPackageCategoryId: string;
  currencyId: string;
  currencyCode: string;
  productPackageCategoryName: string;
  productCategoryType: ProductCategoryType;
  products: Product[];
  addons: Addon[];
  periods: PackagePeriod[];
}

export interface CreatePackagePayload {
  name: string;
  price: number;
  expiryPeriod: number;
  isRecommended: boolean;
  productPackageCategoryId: string;
  products: Product[];
  addons: Addon[];
  periods: PackagePeriod[];
}

export interface UpdatePackagePayload {
  id: string;
  name: string;
  price: number;
  expiryPeriod: number;
  isRecommended: boolean;
  productPackageCategoryId: string;
  products: Product[];
  addons: Addon[];
  periods: PackagePeriod[];
  status: Status;
}

export interface CreatePackageRequest {
  name: string;
  price: number;
  expiryPeriod: number;
  isRecommended: boolean;
  productPackageCategoryId: string;
  selectedPeriodId: string;
  multiplier: number;
  initPrice: number;
  discount: number;
  finalPrice: number;
}

export interface UpdatePackageRequest {
  id: string;
  status: string;
  name: string;
  price: number;
  expiryPeriod: number;
  isRecommended: boolean;
  productPackageCategoryId: string;
  selectedPeriodId: string;
  multiplier: number;
  initPrice: number;
  discount: number;
  finalPrice: number;
}

export interface PackagePeriod extends BaseResponse {
  name: string;
  length: number;
  multiplier: number;
  discount: number;
  price: number;
  isDefault: boolean;
}
