
import { Component, inject } from "@angular/core";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import Swal from "sweetalert2";
import { IdleService } from "./idle.service";

@Component({
  selector: "app-idle-monitor",
  standalone: true,
  imports: [],
  template: ``,
})
export class IdleMonitorComponent {
  private idleService = inject(IdleService);

  constructor() {
    this.idleService
      .onIdle()
      .pipe(takeUntilDestroyed())
      .subscribe((isIdle) => {
        if (isIdle) {
          this.showIdleWarning();
        }
      });

    // Set idle time to 5 seconds
    this.idleService.setIdleTime(900);

    // Set warning time to 5 seconds
    // this.idleService.setWarningTime(15);
    this.idleService.setWarningTime(5);
  }

  private showIdleWarning(): void {
    Swal.fire({
      title: "Idle Warning",
      text: "You have been inactive for a while.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "I'm still here",
      cancelButtonText: "Log Out",
      allowOutsideClick: false,
    }).then((result) => {
      if (result.isConfirmed) {
        // Reset the idle timer if the user confirms they are still active
        this.idleService.setIdleTime(900);
      } else {
        // Close Swal before handling logout
        Swal.close();

        // Handle logout logic here
        this.logoutUser();
      }
    });
  }

  private logoutUser(): void {
    // Implement your logout logic here
    console.log("User logged out due to inactivity");

    // Example: this.authService.logout();
  }
}
