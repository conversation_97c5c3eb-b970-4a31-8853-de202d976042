import { APP_ROUTES } from "@/app/core/constants/routes.constant";
import { DestroyRef, Injectable, inject } from "@angular/core";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { Router } from "@angular/router";
import { Observable, Subject, fromEvent, merge } from "rxjs";
import { tap } from "rxjs/operators";
import Swal from "sweetalert2";
import { AuthService } from "../core/services/auth.service";

@Injectable({
  providedIn: "root",
})
export class IdleService {
  private readonly destroyRef = inject(DestroyRef);
  private idle$ = new Subject<boolean>();
  private idleTime = 15 * 60 * 1000; // 15 minutes
  private warningTime = 15000; // 15 seconds for warning
  private idleTimer: any;
  private warningTimer: any;
  private userActive = true;

  constructor(
    private authService: AuthService,
    private router: Router,
  ) {
    // Monitor user interactions
    const userActions$ = merge(
      fromEvent(document, "mousemove"),
      fromEvent(document, "keydown"),
      fromEvent(document, "mousedown"),
      fromEvent(document, "touchstart"),
      fromEvent(document, "scroll"),
    ).pipe(tap(() => this.resetTimer()));

    userActions$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe();

    this.startIdleTimer();
  }

  private startIdleTimer(): void {
    this.clearTimers();
    this.idleTimer = setTimeout(() => {
      this.userActive = false;
      this.idle$.next(true); // Notify that user is idle
      this.startWarningTimer();
    }, this.idleTime);
  }

  private startWarningTimer(): void {
    this.warningTimer = setTimeout(() => {
      this.logoutUser();
    }, this.warningTime);
  }

  private resetTimer(): void {
    if (!this.userActive) {
      this.idle$.next(false); // Notify that user is active
      this.userActive = true;
    }
    this.startIdleTimer();
  }

  private clearTimers(): void {
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
    }
    if (this.warningTimer) {
      clearTimeout(this.warningTimer);
    }
  }

  private logoutUser(): void {
    Swal.close();
    this.authService.logout();
    this.router.navigate([APP_ROUTES.login]);
  }

  onIdle(): Observable<boolean> {
    return this.idle$.asObservable();
  }

  setIdleTime(seconds: number): void {
    this.idleTime = seconds * 1000;
    this.resetTimer();
  }

  setWarningTime(seconds: number): void {
    this.warningTime = seconds * 1000;
  }
}
