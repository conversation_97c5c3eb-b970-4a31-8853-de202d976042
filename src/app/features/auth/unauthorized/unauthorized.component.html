<div
  class="flex items-center justify-center min-h-screen px-4 py-8 bg-gradient-to-br from-gray-100 to-gray-200"
>
  <div class="w-full max-w-md overflow-hidden bg-white shadow-2xl rounded-2xl">
    <div class="p-6 text-center text-white bg-danger">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="w-16 h-16 mx-auto mb-4"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
        />
      </svg>
      <h2 class="text-3xl font-bold">Access Denied</h2>
    </div>

    <div class="p-8 space-y-6 text-center">
      <p class="text-lg text-gray-600">
        You do not have the necessary permissions to access this page.
      </p>

      <div class="flex flex-col space-y-4">
        <app-button
          (click)="goToDashboard()"
          [block]="true"
          class="flex items-center gap-2"
          size="lg"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-6 h-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
            />
          </svg>
          <span>Return to Dashboard</span>
        </app-button>

        <app-button
          class="flex items-center gap-2"
          variant="secondary"
          (click)="contactSupport()"
          [block]="true"
          size="lg"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-6 h-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
            />
          </svg>

          <span>Contact Support</span>
        </app-button>
      </div>
    </div>
  </div>
</div>
