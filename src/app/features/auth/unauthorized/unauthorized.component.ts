import { APP_ROUTES } from "@/app/core/constants/routes";
import { Component } from "@angular/core";
import { Router } from "@angular/router";
import { ButtonComponent } from "../../../shared/ui/button/button.component";

@Component({
  selector: "app-unauthorized",
  templateUrl: "./unauthorized.component.html",
  standalone: true,
  imports: [ButtonComponent],
})
export class UnauthorizedComponent {
  constructor(private router: Router) {}

  goToDashboard() {
    this.router.navigate([APP_ROUTES.dashboard]);
  }

  contactSupport() {
    // Implement support contact logic
    // This could open a modal, redirect to support page, or trigger a support email
    window.open(
      "mailto:<EMAIL>?subject=Access%20Permission%20Request",
      "_blank",
    );
  }
}
