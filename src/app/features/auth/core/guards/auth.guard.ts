import { APP_ROUTES } from "@/app/core/constants/routes";
import { Injectable } from "@angular/core";
import {
  ActivatedRouteSnapshot,
  CanActivate,
  Router,
  RouterStateSnapshot,
} from "@angular/router";
import { AuthService } from "../services/auth.service";

@Injectable({
  providedIn: "root",
})
export class AuthGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): boolean {
    // First, check if the user is authenticated
    if (!this.authService.isAuthenticated()) {
      // Redirect to login with return URL
      this.router.navigate([APP_ROUTES.login], {
        queryParams: { returnUrl: state.url },
      });
      return false;
    }

    // Check if the current route is already the change password page
    const isChangePasswordRoute = state.url.includes(APP_ROUTES.changePassword);

    // Only redirect to change password if not already on change password page
    if (!this.authService.hasChangedPassword && !isChangePasswordRoute) {
      console.log("Redirecting to change password");
      this.router.navigate([APP_ROUTES.changePassword]);
      return false;
    }

    // If route has required roles, check role permissions
    const requiredRoles = route.data["roles"] as string[];

    // If roles are specified, perform the role check
    if (requiredRoles && !this.authService.hasRole(requiredRoles)) {
      // Redirect to unauthorized page if the user lacks the required role
      this.router.navigate([APP_ROUTES.unauthorized]);
      return false;
    }

    // If everything is fine, allow navigation
    return true;
  }
}
