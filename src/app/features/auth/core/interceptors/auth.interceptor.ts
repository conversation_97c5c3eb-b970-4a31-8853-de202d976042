import { APP_ROUTES } from "@/app/core/constants/routes.constant";
import {
  <PERSON>ttp<PERSON><PERSON>,
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
} from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Observable, of, throwError } from "rxjs";
import { catchError } from "rxjs/operators";
import Swal from "sweetalert2";
import { AuthService } from "../services/auth.service";

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    private authService: AuthService,
    private router: Router,
  ) {}

  intercept(
    req: HttpRequest<any>,
    next: HttpHand<PERSON>,
  ): Observable<HttpEvent<any>> {
    const token = this.authService.getToken();

    if (token) {
      if (this.authService.isTokenExpired()) {
        Swal.fire({
          icon: "warning",
          title: "Session Expired",
          text: "Your session has expired. Please log in again.",
          confirmButtonText: "Login",
          allowOutsideClick: false,
        }).then(() => {
          this.authService.logout();
          this.router.navigate([APP_ROUTES.login]);
        });

        // Return an empty observable to prevent further processing
        return of();
      }

      const cloned = req.clone({
        headers: req.headers.set("Authorization", `Bearer ${token}`),
      });

      return next.handle(cloned).pipe(
        catchError((error) => {
          // if (error.status === 401) {
          //   Swal.fire({
          //     icon: "warning",
          //     title: "Unauthorized",
          //     text: "Your session has expired. Please log in again.",
          //     confirmButtonText: "Login",
          //     allowOutsideClick: false,
          //   }).then(() => {
          //     this.authService.logout();
          //     this.router.navigate([APP_ROUTES.login]);
          //   });
          //   return of(); // Prevent error propagation
          // }
          return throwError(() => error);
        }),
      );
    } else {
      return next.handle(req);
    }
  }
}
