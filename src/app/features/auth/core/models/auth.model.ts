export interface LoginCredentials {
  username: string;
  password: string;
}

export interface ChangePasswordCredentials {
  password: string;
}

export interface RegisterCredentials extends LoginCredentials {
  name: string;
  confirmPassword: string;
}

export interface User {
  firstName: string;
  lastName: string;
  role: string;
  emailVerification: boolean;
  id: string;
  department: string;
  email: string;
  username: string;
  passwordChanged: boolean;
}

export interface AuthResponse {
  data: string;
}
