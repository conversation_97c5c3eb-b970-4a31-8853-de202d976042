import { environment } from "@/environments/environment.development";
import { HttpClient } from "@angular/common/http";
import { inject, Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { jwtDecode } from "jwt-decode";
import { BehaviorSubject, Observable, throwError } from "rxjs";
import { catchError, tap } from "rxjs/operators";
import {
  AuthResponse,
  ChangePasswordCredentials,
  LoginCredentials,
  User,
} from "../models/auth.model";

@Injectable({
  providedIn: "root",
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  private readonly INIT_BASE_URL = `${environment.apiBaseUrl}`;
  private readonly API_URL = `${environment.apiBaseUrl}/auth`;
  router = inject(Router);

  constructor(private http: HttpClient) {
    this.loadStoredUser();
  }

  get currentUser(): User | null {
    return this.currentUserSubject.value;
  }

  get hasChangedPassword(): boolean {
    return this.currentUser?.passwordChanged || false;
  }

  private loadStoredUser(): void {
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      this.currentUserSubject.next(JSON.parse(storedUser));
    }
  }

  login(credentials: LoginCredentials): Observable<AuthResponse> {
    return this.http
      .post<AuthResponse>(`${this.API_URL}/admin/login`, credentials)
      .pipe(
        tap((response) => this.handleAuthentication(response)),
        catchError((error) => throwError(() => error)),
      );
  }

  changePassword(credentials: ChangePasswordCredentials): Observable<any> {
    return this.http
      .put<AuthResponse>(
        `${this.INIT_BASE_URL}/admin/admin-users/change-password`,
        credentials,
      )
      .pipe(
        tap((response) => this.handleAuthentication(response)),
        catchError((error) => throwError(() => error)),
      );
  }

  logout(): void {
    localStorage.removeItem("user");
    localStorage.removeItem("token");
    this.currentUserSubject.next(null);
    this.router.navigate(["/"]);
  }

  isAuthenticated(): boolean {
    return !!this.currentUser;
  }

  private handleAuthentication(response: AuthResponse): void {
    const token = response.data;

    let decodedToken: User | null = null;
    try {
      decodedToken = jwtDecode<User>(token);
    } catch (error) {
      console.error("Invalid token:", error);
      return;
    }

    if (decodedToken) {
      const user: User = {
        id: decodedToken.id,
        firstName: decodedToken.firstName,
        lastName: decodedToken.lastName,
        department: decodedToken.department,
        email: decodedToken.email,
        username: decodedToken.username,
        passwordChanged: decodedToken.passwordChanged,
        role: decodedToken.role.replace(/^ROLE_/, ""),
        emailVerification: decodedToken.emailVerification,
      };
      localStorage.setItem("user", JSON.stringify(user));
      localStorage.setItem("token", token);
      this.currentUserSubject.next(user);
    }
  }

  hasRole(requiredRoles: string[]): boolean {
    if (!requiredRoles || requiredRoles.length === 0) {
      return false;
    }

    if (requiredRoles.includes("ALL")) {
      return true;
    }

    return requiredRoles.includes(this.currentUser?.role as string);
  }

  getToken(): string | null {
    return localStorage.getItem("token");
  }

  isTokenExpired(): boolean {
    const token = this.getToken();
    if (!token) {
      return true; // If no token, consider it expired
    }

    // Decode the token to get the expiration time
    const decodedToken: any = jwtDecode(token);
    const expirationTime = decodedToken.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();

    return currentTime >= expirationTime; // Return true if the token is expired
  }
}
