import { handleError } from "@/app/core/utils/error-handler.util";
import { FormFieldComponent } from "@/app/shared/ui/form-field/form-field.component";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { Component, inject } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { Router } from "@angular/router";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { AuthService } from "../../core/services/auth.service";

interface LoginForm {
  username: string;
  password: string;
}
@Component({
  selector: "app-login-form",
  standalone: true,
  imports: [FormFieldComponent, ReactiveFormsModule, ButtonComponent],
  templateUrl: "./login-form.component.html",
})
export class LoginFormComponent {
  isLoading = false;

  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);

  loginForm = this.fb.nonNullable.group({
    username: ["", [Validators.required, Validators.email]],
    password: ["", [Validators.required]],
  }) as FormGroup<FormControlType<LoginForm>>;

  onSubmit() {
    if (this.loginForm.valid) {
      this.isLoading = true;

      this.authService.login(this.loginForm.value as LoginForm).subscribe({
        next: () => {
          this.router.navigate(["/admin/dashboard"]);
          window.location.reload();
        },
        error: (error) => {
          handleError(error);
          this.isLoading = false;
        },
      });
    } else {
      markFormGroupTouched(this.loginForm);
    }
  }
}
