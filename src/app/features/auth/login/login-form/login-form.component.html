<form
  [formGroup]="loginForm"
  (ngSubmit)="onSubmit()"
  class="flex flex-col space-y-6"
>
  <div class="flex flex-col gap-3">
    <!-- Email Input -->
    <app-form-field
      label="Email"
      type="email"
      [control]="loginForm.controls.username"
      [required]="true"
      placeholder="<EMAIL>"
      autocomplete="email"
    />

    <!-- Password Input -->
    <app-form-field
      label="Password"
      type="password"
      [control]="loginForm.controls.password"
      [required]="true"
      [minLength]="8"
    />
  </div>

  <!-- Submit Button -->
  <app-button type="submit" [loading]="isLoading" [block]="true">
    login
  </app-button>
</form>
