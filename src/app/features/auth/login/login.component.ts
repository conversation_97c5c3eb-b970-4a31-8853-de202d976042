import { APP_ROUTES } from "@/app/core/constants/routes.constant";

import { Component } from "@angular/core";
import { RouterModule } from "@angular/router";
import { LoginFormComponent } from "./login-form/login-form.component";
import { LogoComponent } from "../../../shared/logo/logo.component";

@Component({
  selector: "app-login",
  templateUrl: "./login.component.html",
  standalone: true,
  imports: [LoginFormComponent, RouterModule, LogoComponent],
})
export class LoginComponent {
  routes = APP_ROUTES;
  constructor() {}
}
