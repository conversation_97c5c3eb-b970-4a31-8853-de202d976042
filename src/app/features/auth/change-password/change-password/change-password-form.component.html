<form
  [formGroup]="form"
  (ngSubmit)="onSubmit()"
  class="flex flex-col space-y-6"
>
  <div class="flex flex-col gap-3">
    <!-- Password Input -->
    <app-form-field
      label="New Password"
      type="password"
      [control]="form.controls.newPassword"
      [required]="true"
      [minLength]="12"
      [customErrorMessages]="passwordErrors"
    />

    <!-- Confirm Password Input -->
    <app-form-field
      label="Confirm New Password"
      type="password"
      [control]="form.controls.confirmPassword"
      [required]="true"
      [minLength]="12"
      [customErrorMessages]="passwordErrors"
    />
  </div>

  <!-- Submit Button -->
  <app-button type="submit" [loading]="isLoading" [block]="true">
    Change Password
  </app-button>
</form>
