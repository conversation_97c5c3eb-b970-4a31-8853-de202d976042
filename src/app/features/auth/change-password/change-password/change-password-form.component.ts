
import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>ni<PERSON>, inject } from "@angular/core";
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from "@angular/forms";
import { Router, RouterModule } from "@angular/router";
import { Subject } from "rxjs";
import { distinctUntilChanged, takeUntil } from "rxjs/operators";

import { APP_ROUTES } from "@/app/core/constants/routes.constant";
import { handleError } from "@/app/core/utils/error-handler.util";
import { AuthService } from "../../core/services/auth.service";

import { ButtonComponent } from "@/app/shared/ui/button/button.component";
import { FormFieldComponent } from "@/app/shared/ui/form-field/form-field.component";

import { PasswordValidators } from "@/app/core/validators/password.validator";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import Swal from "sweetalert2";
import { ChangePasswordCredentials } from "../../core/models/auth.model";

interface ChangePasswordForm {
  newPassword: string;
  confirmPassword: string;
}

@Component({
  selector: "app-change-password-form",
  standalone: true,
  imports: [
    ReactiveFormsModule,
    RouterModule,
    FormFieldComponent,
    ButtonComponent
],
  templateUrl: "./change-password-form.component.html",
})
export class ChangePasswordFormComponent implements OnInit, OnDestroy {
  // Dependency Injection
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);
  passwordChanged = this.authService.hasChangedPassword;

  // Component state
  isLoading = false;
  error: string | null = null;
  routes = APP_ROUTES;

  // Form configuration
  form!: FormGroup<FormControlType<ChangePasswordForm>>;

  // RxJS unsubscribe management
  private destroy$ = new Subject<void>();

  // Password validation error messages
  readonly passwordErrors: Record<string, string> = {
    required: "Password is required",
    minlength: "Password must be at least 12 characters long",
    maxlength: "Password cannot be longer than 32 characters",
    lowercase: "Password must contain at least one lowercase letter",
    uppercase: "Password must contain at least one uppercase letter",
    digit: "Password must contain at least one number",
    specialChar:
      'Password must contain at least one special character (!@#$%^&*(),.?":{}|<>)',
    noSpaces: "Password cannot contain spaces",
    passwordMismatch: "Passwords do not match",
  };

  ngOnInit(): void {
    this.initForm();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForm(): void {
    this.form = this.fb.nonNullable.group({
      newPassword: [
        "",
        [
          Validators.required,
          PasswordValidators.patternValidator(),
          Validators.minLength(12),
          Validators.maxLength(32),
        ],
      ],
      confirmPassword: ["", [Validators.required, this.passwordMatchValidator]],
    });

    // Watch for changes to trigger cross-field validation
    this.form
      .get("newPassword")
      ?.valueChanges.pipe(takeUntil(this.destroy$), distinctUntilChanged())
      .subscribe(() => {
        this.form.get("confirmPassword")?.updateValueAndValidity();
      });
  }

  // Custom validator to check password match
  private passwordMatchValidator = (
    control: AbstractControl,
  ): ValidationErrors | null => {
    const form = control.parent;
    if (!form) return null;

    const newPassword = form.get("newPassword");
    const confirmPassword = control;

    if (newPassword && confirmPassword) {
      const passwordsMatch = newPassword.value === confirmPassword.value;
      return passwordsMatch ? null : { passwordMismatch: true };
    }

    return null;
  };

  onSubmit(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    const { newPassword } = this.form.getRawValue();

    this.changePassword(newPassword);
  }

  private changePassword(newPassword: string): void {
    this.isLoading = true;
    this.error = null;

    const payload: ChangePasswordCredentials = {
      password: newPassword,
    };

    this.authService
      .changePassword(payload)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          Swal.fire("Success", "Password reset was successful", "success").then(
            () => {
              this.router.navigate([APP_ROUTES.dashboard]);
            },
          );
        },
        error: (error) => {
          handleError(error);
          this.isLoading = false;
        },
      });
  }
}
