import { APP_ROUTES } from "@/app/core/constants/routes.constant";

import { Component } from "@angular/core";
import { RouterModule } from "@angular/router";
import { CardComponent } from "../../../shared/ui/card/card.component";
import { ChangePasswordFormComponent } from "./change-password/change-password-form.component";

@Component({
  selector: "app-change-password",
  templateUrl: "./change-password.component.html",
  standalone: true,
  imports: [
    RouterModule,
    ChangePasswordFormComponent,
    CardComponent
],
})
export class ChangePasswordComponent {
  routes = APP_ROUTES;
  constructor() {}
}
