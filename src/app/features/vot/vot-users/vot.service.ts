import { Status } from "@/app/core/enums/status.enum";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { UpdateVotUserRequest } from "./vot.model";

@Injectable({
  providedIn: "root",
})
export class VotService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getVotUsers(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/vot-users`);
  }

  public updateVotUser(votUserData: UpdateVotUserRequest): Observable<any> {
    return this.http.put<any>(`${this.apiServerUrl}/vot-users`, votUserData);
  }

  public getAtsVotUsers(status: Status): Observable<any> {
    const params = new HttpParams().set("status", status);
    return this.http.get<any>(`${this.apiServerUrl}/vot-users/ats`, { params });
  }
}
