<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #end>
      <app-button
        size="sm"
        class="!gap-2"
        variant="secondary"
        (click)="
          reportExportService.exportToExcel(exportData, 'vot-users.xlsx')
        "
      >
        <i class="text-sm pi pi-upload"></i>
        Export
      </app-button>
    </ng-template>
  </p-toolbar>

  @if (isDataLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading VOT users...</span>
      </div>
    </div>
  } @else {
    <p-table
      [value]="filteredVotUsers()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
    >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold">Manage VOT Users</app-text>
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />
            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search users..."
            />
          </p-iconfield>
        </div>
      </ng-template>

      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>
          <th pSortableColumn="firstName">
            <div class="flex items-center justify-between gap-2">
              First Name
              <p-sortIcon field="firstName" />
              <p-columnFilter
                type="text"
                field="firstName"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="lastName">
            <div class="flex items-center justify-between gap-2">
              Last Name
              <p-sortIcon field="lastName" />
              <p-columnFilter
                type="text"
                field="lastName"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="email">
            <div class="flex items-center justify-between gap-2">
              Email
              <p-sortIcon field="email" />
              <p-columnFilter
                type="text"
                field="email"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="packageSubscriptionName">
            <div class="flex items-center justify-between gap-2">
              Subscription
              <p-sortIcon field="packageSubscriptionName" />
              <p-columnFilter
                type="text"
                field="packageSubscriptionName"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th>Actions</th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-user let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(user)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ user.firstName }}</td>
          <td>{{ user.lastName }}</td>
          <td>{{ user.email }}</td>
          <td>{{ user.packageSubscriptionName }}</td>
          <td>{{ user.status }}</td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button size="icon" (click)="toggleModal('viewItem', user)">
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', user)"
                >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="rowexpansion" let-user>
        <tr>
          <td colspan="7">
            <div class="p-4 bg-gray-50 rounded-md">
              <div
                class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
              >
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Address
                  </h5>
                  <p class="text-gray-800">{{ user.address }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Mobile
                  </h5>
                  <p class="text-gray-800">{{ user.mobile }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">City</h5>
                  <p class="text-gray-800">{{ user.city }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Country
                  </h5>
                  <p class="text-gray-800">{{ user.country }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    User Type
                  </h5>
                  <p class="text-gray-800">{{ user.userType }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Subscriber Name
                  </h5>
                  <p class="text-gray-800">
                    {{ user.subscriberName }} {{ user.subscriberSurname }}
                  </p>
                </div>

                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ user.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                      user.modifiedDate
                        ? (user.modifiedDate | date: "yyyy-MM-dd HH:mm:ss")
                        : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ user.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ user.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  (click)="toggleModal('editItem', user)"
                >
                  Edit
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="7" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No users found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- View User Modal -->
<app-modal [isVisible]="modals.viewItem" [config]="{ title: 'View User' }">
  <div class="grid grid-cols-2 gap-4">
    <div>
      <label class="form-label">First Name</label>
      <input
        type="text"
        [value]="selectedUser?.firstName"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Last Name</label>
      <input
        type="text"
        [value]="selectedUser?.lastName"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Email</label>
      <input
        type="text"
        [value]="selectedUser?.email"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Mobile</label>
      <input
        type="text"
        [value]="selectedUser?.mobile"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Address</label>
      <input
        type="text"
        [value]="selectedUser?.address"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">City</label>
      <input
        type="text"
        [value]="selectedUser?.city"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Country</label>
      <input
        type="text"
        [value]="selectedUser?.country"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">User Type</label>
      <input
        type="text"
        [value]="selectedUser?.userType"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Package Subscription</label>
      <input
        type="text"
        [value]="selectedUser?.packageSubscriptionName"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Status</label>
      <input
        type="text"
        [value]="selectedUser?.status"
        class="form-control"
        disabled
      />
    </div>
  </div>

  <div class="flex justify-end mt-4">
    <app-button (click)="toggleModal('viewItem')">Close</app-button>
  </div>
</app-modal>

<!-- Edit User Modal -->
<app-modal [isVisible]="modals.editItem" [config]="{ title: 'Edit User' }">
  <form [formGroup]="editVotuserForm" (ngSubmit)="handleVOTUserSubmit()">
    <div class="grid grid-cols-2 gap-4">
      <app-form-field
        label="First Name"
        type="text"
        [control]="editVotuserForm.get('firstName')!"
        [required]="true"
      />
      <app-form-field
        label="Last Name"
        type="text"
        [control]="editVotuserForm.get('lastName')!"
        [required]="true"
      />
      <app-form-field
        label="Email"
        type="email"
        [control]="editVotuserForm.get('email')!"
        [required]="true"
      />
      <app-form-field
        label="Mobile"
        type="text"
        [control]="editVotuserForm.get('mobile')!"
        [required]="true"
      />
      <app-form-field
        label="Address"
        type="text"
        [control]="editVotuserForm.get('address')!"
      />
      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
        [control]="editVotuserForm.get('status')!"
        [required]="true"
      />
    </div>

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="toggleModal('editItem')" variant="outline"
        >Cancel</app-button
      >
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<p-toast />
