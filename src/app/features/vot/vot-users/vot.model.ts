import { BaseResponse } from "@/app/core/interfaces/common.interface";

export interface Vot extends BaseResponse {
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  address: string;
  cityId: string;
  city: string;
  country: string;
  countryId: string;
  userType: string;
  packageSubscriptionId: string;
  productId: string;
  packageSubscriptionName: string;
  subscriberName: string;
  subscriberSurname: string;
}

export interface UpdateVotUserRequest {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  address: string;
  cityId: string;
  city: string;
  country: string;
  countryId: string;
  userType: string;
  packageSubscriptionId: string;
  productId: string;
  status: string;
  packageSubscriptionName: string;
}

export interface AtsVotUsers extends BaseResponse {
  id: string;
  userName: string;
  completeName: string;
  email: string;
  mobile: string;
  userId: string;
  lastLoginDateTime: Date;
  userTypeId: string;
  creationDate: Date;
}
