// vot-users.component.ts
import { DatePipe } from "@angular/common";
import { Component, inject, signal } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { ButtonComponent } from "../../../shared/ui/button/button.component";
import { CardComponent } from "../../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../../shared/ui/modal/modal.component";
import { TextComponent } from "../../../shared/ui/text/text.component";
import { UpdateVotUserRequest, Vot } from "./vot.model";
import { VotService } from "./vot.service";

@Component({
  selector: "app-vot-users",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    ModalComponent,
    FormFieldComponent,
    ButtonPermissionDirective,
    DatePipe,
  ],
  templateUrl: "./vot-users.component.html",
  providers: [DatePipe],
})
export class VotUsersComponent {
  private readonly votService = inject(VotService);
  private readonly fb = inject(FormBuilder);
  private readonly datePipe = inject(DatePipe);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;

  filteredVotUsers = signal<Vot[]>([]);
  votUsers = signal<Vot[]>([]);
  selectedUser: Vot | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  modals: Record<"addItem" | "editItem" | "viewItem", boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  isDataLoading = false;

  get exportData(): any[] {
    return this.filteredVotUsers().map((user) => ({
      "First Name": user.firstName,
      "Last Name": user.lastName,
      Email: user.email,
      Mobile: user.mobile,
      Address: user.address,
      City: user.city,
      Country: user.country,
      "User Type": user.userType,
      "Package Subscription": user.packageSubscriptionName,
      "Subscriber Name": `${user.subscriberName} ${user.subscriberSurname}`,
      Status: user.status,
      "Created Date": this.datePipe.transform(
        user.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": user.modifiedDate
        ? this.datePipe.transform(user.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": user.createdUserName ?? "-",
      "Modified By": user.modifiedUserName ?? "-",
    }));
  }

  editVotuserForm = this.fb.nonNullable.group({
    id: [""],
    firstName: ["", [Validators.required]],
    lastName: ["", [Validators.required]],
    email: ["", [Validators.required, Validators.email]],
    mobile: ["", [Validators.required]],
    address: [""],
    cityId: [""],
    city: [""],
    country: [""],
    countryId: [""],
    userType: [""],
    packageSubscriptionId: [""],
    productId: [""],
    packageSubscriptionName: [""],
    status: ["", Validators.required],
  }) as FormGroup<FormControlType<UpdateVotUserRequest>>;

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.isDataLoading = true;
    this.votService.getVotUsers().subscribe({
      next: (response) => {
        this.votUsers.set(response.data);
        this.filteredVotUsers.set(response.data);
        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load VOT users");
        this.isDataLoading = false;
      },
    });
  }

  onSearch(term: string): void {
    if (!term) {
      this.filteredVotUsers.set(this.votUsers());
      return;
    }

    const filtered = this.votUsers().filter(
      (user) =>
        user.firstName.toLowerCase().includes(term.toLowerCase()) ||
        user.lastName.toLowerCase().includes(term.toLowerCase()) ||
        user.subscriberName.toLowerCase().includes(term.toLowerCase()) ||
        user.subscriberSurname.toLowerCase().includes(term.toLowerCase()) ||
        user.email.toLowerCase().includes(term.toLowerCase()) ||
        user.packageSubscriptionName.toLowerCase().includes(term.toLowerCase()),
    );
    this.filteredVotUsers.set(filtered);
  }

  toggleRow(user: Vot): void {
    if (this.expandedRows[user.id]) {
      delete this.expandedRows[user.id];
    } else {
      this.expandedRows[user.id] = true;
    }
    this.expandedRows = { ...this.expandedRows };
  }

  toggleModal(mode: "addItem" | "editItem" | "viewItem", user?: Vot): void {
    this.selectedUser = user || null;
    if (mode === "editItem" && user) {
      this.editVotuserForm.patchValue({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        mobile: user.mobile,
        address: user.address,
        cityId: user.cityId,
        city: user.city,
        country: user.country,
        countryId: user.countryId,
        userType: user.userType,
        packageSubscriptionId: user.packageSubscriptionId,
        productId: user.productId,
        packageSubscriptionName: user.packageSubscriptionName,
        status: user.status,
      });
    }
    this.modals[mode] = !this.modals[mode];
  }

  handleVOTUserSubmit(): void {
    if (this.editVotuserForm.invalid) {
      markFormGroupTouched(this.editVotuserForm);
      return;
    }

    this.votService
      .updateVotUser(this.editVotuserForm.value as UpdateVotUserRequest)
      .subscribe({
        next: () => {
          showToast({
            message: `User has been updated successfully`,
            type: "success",
          });
          this.loadData();
          this.toggleModal("editItem");
          this.editVotuserForm.reset();
        },
        error: (error) => {
          handleError(error, `Failed to update user: ${error.message}`);
        },
      });
  }
}
