import { DatePipe } from "@angular/common";
import { Component, inject, OnInit, signal } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { ButtonModule } from "primeng/button";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";

import { Status } from "@/app/core/enums/status.enum";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { AtsVotUsers } from "@/app/features/vot/vot-users/vot.model";
import { VotService } from "@/app/features/vot/vot-users/vot.service";
import { ButtonGroupComponent } from "@/app/shared/ui/button/button-group/button-group.component";
import { ButtonComponent } from "@/app/shared/ui/button/button.component";
import { CardComponent } from "@/app/shared/ui/card/card.component";
import { TextComponent } from "@/app/shared/ui/text/text.component";

@Component({
  selector: "app-ats-vot-users",
  standalone: true,
  imports: [
    FormsModule,
    TableModule,
    ToastModule,
    ToolbarModule,
    ButtonModule,
    IconFieldModule,
    InputIconModule,
    InputTextModule,
    RippleModule,
    ButtonGroupComponent,
    ButtonComponent,
    CardComponent,
    TextComponent,
    DatePipe,
  ],
  templateUrl: "./ats-vot-users.component.html",
  providers: [DatePipe],
})
export class AtsVotUsersComponent implements OnInit {
  private readonly votService = inject(VotService);
  private readonly datePipe = inject(DatePipe);
  protected readonly reportExportService = inject(ReportExportService);

  // Enums for template usage
  Status = Status;

  // Component state
  status: Status = Status.ACTIVE;
  isDataLoading = false;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  // Data signals
  users = signal<AtsVotUsers[]>([]);
  filteredUsers = signal<AtsVotUsers[]>([]);

  // Export data getter for Excel export
  get exportData(): any[] {
    return this.filteredUsers().map((user) => ({
      Username: user.userName,
      Email: user.email,
      "Full Name": user.completeName,
      "User ID": user.userId,
      Mobile: user.mobile,
      "Creation Date": this.datePipe.transform(
        user.creationDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Last Login": this.datePipe.transform(
        user.lastLoginDateTime,
        "yyyy-MM-dd HH:mm:ss",
      ),
      Status: user.status,
      "Created Date": this.datePipe.transform(
        user.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": user.modifiedDate
        ? this.datePipe.transform(user.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": user.createdUserName ?? "-",
      "Modified By": user.modifiedUserName ?? "-",
    }));
  }

  ngOnInit(): void {
    this.loadData();
  }

  getTableTitle(): string {
    return `${this.status === Status.ACTIVE ? "Active" : "Inactive"} ATS VOT Users`;
  }

  setStatus(status: Status): void {
    this.status = status;
    this.loadData();
    // Reset search when changing status
    this.searchTerm = "";
  }

  toggleRow(user: AtsVotUsers): void {
    if (this.expandedRows[user.userId]) {
      delete this.expandedRows[user.userId];
    } else {
      this.expandedRows[user.userId] = true;
    }
    // Create a new object to trigger change detection
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term);
  }

  applyFilters(searchTerm: string | null = null): void {
    let filtered = this.users();

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (user) =>
          (user.userName && user.userName.toLowerCase().includes(term)) ||
          (user.email && user.email.toLowerCase().includes(term)) ||
          (user.completeName &&
            user.completeName.toLowerCase().includes(term)) ||
          (user.status && user.status.toLowerCase().includes(term)) ||
          (user.mobile && user.mobile.toLowerCase().includes(term)),
      );
    }

    this.filteredUsers.set(filtered);
  }

  loadData(): void {
    this.isDataLoading = true;
    this.votService.getAtsVotUsers(this.status).subscribe({
      next: (response) => {
        const mappedUsers = response.data.map((user: AtsVotUsers) => ({
          ...user,
          creationDate: this.convertToDate(user.creationDate),
          lastLoginDateTime: this.convertToDate(user.lastLoginDateTime),
        }));
        this.users.set(mappedUsers);

        // Apply any existing filters after loading data
        this.applyFilters(this.searchTerm);
        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load users");
        this.isDataLoading = false;
      },
    });
  }

  convertToDate(date: any): Date | null {
    if (date === null || date === undefined) {
      return null;
    }
    return typeof date === "number" ? new Date(date * 1000) : new Date(date);
  }
}
