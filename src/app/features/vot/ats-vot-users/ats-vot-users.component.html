<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter buttons -->
      <app-button-group>
        <app-button
          (click)="setStatus(Status.ACTIVE)"
          [variant]="status === Status.ACTIVE ? 'primary' : 'outline-primary'"
        >
          Active
        </app-button>
        <app-button
          (click)="setStatus(Status.INACTIVE)"
          [variant]="status === Status.INACTIVE ? 'primary' : 'outline-primary'"
        >
          Inactive
        </app-button>
      </app-button-group>
    </ng-template>

    <ng-template #end>
      <app-button
        size="sm"
        class="!gap-2"
        variant="secondary"
        (click)="
          reportExportService.exportToExcel(exportData, 'vot-users.xlsx')
        "
      >
        <i class="text-sm pi pi-upload"></i>
        Export
      </app-button>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isDataLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading users...</span>
      </div>
    </div>
  } @else {
    <!-- Users Table -->
    <p-table
      [value]="filteredUsers()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="userId"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
    >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold">{{ getTableTitle() }}</app-text>
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />

            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search users..."
            />
          </p-iconfield>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>

          <th pSortableColumn="creationDate">
            <div class="flex items-center justify-between gap-2">
              Creation Date
              <p-sortIcon field="creationDate" />
              <p-columnFilter
                type="date"
                field="creationDate"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="userName">
            <div class="flex items-center justify-between gap-2">
              Username
              <p-sortIcon field="userName" />
              <p-columnFilter
                type="text"
                field="userName"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="email">
            <div class="flex items-center justify-between gap-2">
              Email
              <p-sortIcon field="email" />
              <p-columnFilter
                type="text"
                field="email"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="userId">
            <div class="flex items-center justify-between gap-2">
              User ID
              <p-sortIcon field="userId" />
              <p-columnFilter
                type="text"
                field="userId"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="lastLoginDateTime">
            <div class="flex items-center justify-between gap-2">
              Last Login
              <p-sortIcon field="lastLoginDateTime" />
              <p-columnFilter
                type="date"
                field="lastLoginDateTime"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-user let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(user)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ user.creationDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ user.userName }}</td>
          <td>{{ user.email }}</td>
          <td>{{ user.userId }}</td>
          <td>{{ user.lastLoginDateTime | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ user.status }}</td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-user>
        <tr>
          <td colspan="7">
            <div class="p-4 bg-gray-50 rounded-md">
              <div
                class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
              >
                <!-- User Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Full Name
                  </h5>
                  <p class="text-gray-800">{{ user.completeName }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Mobile
                  </h5>
                  <p class="text-gray-800">{{ user.mobile }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">{{ user.status }}</p>
                </div>

                <!-- Audit Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Creation Date
                  </h5>
                  <p class="text-gray-800">
                    {{ user.creationDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ user.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                      user.modifiedDate
                        ? (user.modifiedDate | date: "yyyy-MM-dd HH:mm:ss")
                        : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ user.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ user.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="7" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No users found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<p-toast />
