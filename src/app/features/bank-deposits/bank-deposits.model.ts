import { Status } from "@/app/core/enums/status.enum";
import { BaseResponse } from "@/app/core/interfaces/common.interface";

export interface BankDepositTransaction extends BaseResponse {
  id: string;
  clientBankId: string;
  transactionId: string;
  status: Status;
  amount: number;
  refNo: string;
  comment: string;
  popId: string;
  filePath: string;
  originalFilename: string;
  clientFirstName?: string;
  clientLastName?: string;
  accountHolderName?: string;
  bankName?: string;
  bankAccountNumber?: string;
  bankAccountName?: string;
  bankBranchName?: string;
  createdDate: string;
  modifiedDate: string;
  createdUserName: string;
  modifiedUserName: string;
}

export interface ApproveBankDepositRequest {
  transactionId: string;
  status: Status;
  comment?: string;
}

export interface BankPOP {
  id: string;
  transactionId: string;
  filePath: string;
  originalFilename: string;
  fileType: string;
  fileSize: number;
  status: Status;
  createdDate: string;
  modifiedDate: string;
  createdUserName: string;
  modifiedUserName: string;
}

export interface ServerSideTableRequest {
  page: number;
  size: number;
  sortField?: string;
  sortOrder?: "asc" | "desc";
  filters?: Record<string, any>;
  globalFilter?: string;
  statusFilter: Status | null;
}

export interface ServerSideTableResponse<T> {
  data: {
    data: T[];
    totalRecords: number;
    page: number;
    size: number;
    totalPages: number;
    first: number;
    last: number;
  };
}
