import { Status } from "@/app/core/enums/status.enum";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { catchError, Observable, throwError } from "rxjs";
import { environment } from "src/environments/environment.development";
import {
  ApproveBankDepositRequest,
  BankDepositTransaction,
  ServerSideTableRequest,
  ServerSideTableResponse,
} from "./bank-deposits.model";

@Injectable({
  providedIn: "root",
})
export class BankDepositsService {
  private apiServerUrl = environment.apiBaseUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get transactions with server-side pagination, sorting and filtering
   * @param request The table request parameters
   * @returns Observable of paginated bank deposit transactions
   */
  public getTransactionsServerSide(
    request: ServerSideTableRequest,
  ): Observable<ServerSideTableResponse<BankDepositTransaction>> {
    // Create initial params with pagination
    let params = new HttpParams()
      .set("page", request.page.toString())
      .set("size", request.size.toString());

    // Add sort parameters if provided
    if (request.sortField) {
      params = params.set(
        "sort",
        `${request.sortField},${request.sortOrder || "asc"}`,
      );
    }

    // Add filter parameters - using correct format for backend
    if (request.filters) {
      Object.entries(request.filters).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== "") {
          // Format changed to match backend expectations
          params = params.set(`filter[${key}]`, value.toString());
        }
      });
    }

    // Add search parameter if provided
    if (request.globalFilter) {
      params = params.set("search", request.globalFilter);
    }

    if (
      request.statusFilter === Status.INITIATED ||
      request.statusFilter === Status.PENDING ||
      request.statusFilter === Status.APPROVED ||
      request.statusFilter === Status.COMPLETED ||
      request.statusFilter === Status.CANCELLED ||
      request.statusFilter === Status.DECLINED
    ) {
      params = params.set("statusFilter", request.statusFilter);
    }

    return this.http
      .get<
        ServerSideTableResponse<BankDepositTransaction>
      >(`${this.apiServerUrl}/admin/bank-deposits`, { params })
      .pipe(
        catchError((error) => {
          console.error("Error fetching bank deposit transactions:", error);
          return throwError(
            () =>
              new Error("Failed to load transactions. Please try again later."),
          );
        }),
      );
  }
  /**
   * Get all bank deposit transactions
   * @returns Observable of bank deposit transactions
   */
  public getTransactions(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/admin/bank-deposits/all`);
  }

  /**
   * Get all pending bank deposit transactions
   * @returns Observable of bank deposit transactions
   */
  public getPendingTransactions(): Observable<any> {
    return this.http.get<any>(
      `${this.apiServerUrl}/admin/bank-deposits/pending`,
    );
  }

  /**
   * Get a specific bank deposit transaction by ID
   * @param transactionId The ID of the transaction
   * @returns Observable of bank deposit transaction
   */
  public getTransaction(transactionId: string): Observable<any> {
    return this.http.get<any>(
      `${this.apiServerUrl}/admin/bank-deposits/${transactionId}`,
    );
  }

  /**
   * Approve or reject a bank deposit transaction
   * @param request Approval request containing transaction ID and status
   * @returns Observable of updated bank deposit transaction
   */
  public approveTransaction(
    request: ApproveBankDepositRequest,
  ): Observable<any> {
    return this.http.put<any>(
      `${this.apiServerUrl}/admin/bank-deposits/approve`,
      request,
    );
  }

  /**
   * Download the file programmatically with authentication
   * @param transactionId The transaction ID
   * @returns Observable of the file as blob
   */
  public downloadFile(transactionId: string): Observable<Blob> {
    return this.http
      .get(`${this.apiServerUrl}/admin/bank-deposits/${transactionId}/file`, {
        responseType: "blob",
      })
      .pipe(
        catchError((error) => {
          console.error("Error downloading file:", error);
          throw error;
        }),
      );
  }

  /**
   * Get a direct URL to view the file (for use in img tags)
   * @param transactionId The transaction ID
   * @returns URL string to the file
   */
  public getDirectViewUrl(transactionId: string): string {
    const timestamp = new Date().getTime();
    return `${this.apiServerUrl}/admin/bank-deposits/${transactionId}/file?t=${timestamp}`;
  }

  /**
   * Get file URL for downloading or viewing
   * @param transactionId The transaction ID
   * @returns The proxied URL to the file
   */
  public getProxiedFileUrl(transactionId: string): string {
    const timestamp = new Date().getTime();
    return `${this.apiServerUrl}/admin/bank-deposits/${transactionId}/file?t=${timestamp}`;
  }

  /**
   * Determine if a file type is viewable in browser
   * @param fileType The MIME type of the file
   * @returns Boolean indicating if the file is viewable
   */
  public isViewableFileType(fileType: string | null): boolean {
    if (!fileType) return false;

    const viewableTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/bmp",
      "image/svg+xml",
      "application/pdf",
      "text/plain",
      "text/html",
    ];

    return viewableTypes.includes(fileType.toLowerCase());
  }

  /**
   * Get proper icon for a file type
   * @param fileType The MIME type of the file
   * @returns Icon class for the file type
   */
  public getFileIcon(fileType: string | null): string {
    if (!fileType) return "pi pi-file";

    fileType = fileType.toLowerCase();

    if (fileType.startsWith("image/")) return "pi pi-image";
    if (fileType === "application/pdf") return "pi pi-file-pdf";
    if (fileType.includes("excel") || fileType.includes("spreadsheet"))
      return "pi pi-file-excel";
    if (fileType.includes("word") || fileType.includes("document"))
      return "pi pi-file-word";
    if (fileType.includes("powerpoint") || fileType.includes("presentation"))
      return "pi pi-file-powerpoint";
    if (fileType.includes("zip") || fileType.includes("compressed"))
      return "pi pi-file-archive";

    return "pi pi-file";
  }
}
