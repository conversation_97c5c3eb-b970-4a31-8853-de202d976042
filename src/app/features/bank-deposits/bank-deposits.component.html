<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter dropdown -->
      <div class="mr-3 flex items-center">
        <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
        <div class="relative flex items-center">
          <i class="pi pi-filter absolute left-2 text-gray-500"></i>
          <select
            [ngModel]="tableState().statusFilter"
            (ngModelChange)="updateStatusFilter($event)"
            class="pl-8 pr-4 py-2 border rounded-lg text-sm"
          >
            <option [value]="null">All Statuses</option>
            <option [value]="Status.INITIATED">Initiated</option>
            <option [value]="Status.PENDING">Pending</option>
            <option [value]="Status.APPROVED">Approved</option>
            <option [value]="Status.COMPLETED">Completed</option>
            <option [value]="Status.CANCELLED">Cancelled</option>
            <option [value]="Status.DECLINED">Declined</option>
          </select>
        </div>
      </div>
    </ng-template>

    <ng-template #end>
      <app-button
        size="sm"
        class="!gap-2"
        variant="secondary"
        (click)="
          reportExportService.exportToExcel(exportData(), 'bank-deposits.xlsx')
        "
      >
        <i class="text-sm pi pi-upload"></i>
        Export
      </app-button>
    </ng-template>
  </p-toolbar>

  <p-table
    [value]="bankDeposits()"
    [lazy]="true"
    [paginator]="true"
    [rows]="tableState().size"
    [first]="tableState().page * tableState().size"
    [totalRecords]="totalRecords()"
    [rowsPerPageOptions]="[10, 25, 50]"
    [loading]="isDataLoading()"
    [showCurrentPageReport]="true"
    [expandedRowKeys]="expandedRows()"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    (onLazyLoad)="onLazyLoad($event)"
    [sortField]="tableState().sortField"
    [sortOrder]="tableState().sortOrder === 'asc' ? 1 : -1"
    dataKey="id"
    styleClass="p-datatable-elegant"
    [globalFilterFields]="[
      'refNo',
      'comment',
      'clientFirstName',
      'clientLastName',
      'originalFilename',
      'bankName',
      'bankAccountNumber',
      'accountHolderName',
    ]"
    [autoLayout]="true"
    [lazyLoadOnInit]="false"
  >
    <ng-template pTemplate="caption">
      <div class="flex flex-wrap items-center justify-between gap-2">
        <app-text variant="title/semibold">Bank Deposit Transactions</app-text>
        <p-iconfield>
          <p-inputicon styleClass="pi pi-search" />
          <input
            id="depositsSearchInput"
            pSize="small"
            pInputText
            type="text"
            [ngModel]="searchTerm()"
            (ngModelChange)="onSearch($event)"
            placeholder="Search bank deposits..."
            #depositsSearchInput
            [attr.data-testid]="'depositsSearchInput'"
          />
        </p-iconfield>
      </div>
    </ng-template>

    <!-- Table Header -->
    <ng-template pTemplate="header">
      <tr class="bg-gray-50 text-nowrap">
        <th style="width: 4rem"></th>

        <th pSortableColumn="createdDate">
          <div class="flex items-center gap-2">
            Created Date
            <p-sortIcon field="createdDate" />
          </div>
        </th>

        <th pSortableColumn="refNo">
          <div class="flex items-center justify-between gap-2">
            Reference No.
            <p-sortIcon field="refNo" />
            <p-columnFilter
              type="text"
              field="refNo"
              display="menu"
              class="ml-auto"
            />
          </div>
        </th>

        <th pSortableColumn="clientFirstName">
          <div class="flex items-center justify-between gap-2">
            Client
            <p-sortIcon field="clientFirstName" />
            <p-columnFilter
              type="text"
              field="clientFirstName"
              display="menu"
              class="ml-auto"
            />
          </div>
        </th>

        <th pSortableColumn="amount">
          <div class="flex items-center justify-between gap-2">
            Amount
            <p-sortIcon field="amount" />
            <p-columnFilter
              type="numeric"
              field="amount"
              display="menu"
              class="ml-auto"
            />
          </div>
        </th>

        <th>
          <div class="flex items-center justify-between gap-2">Bank</div>
        </th>

        <th pSortableColumn="comment">
          <div class="flex items-center justify-between gap-2">
            Comment
            <p-sortIcon field="comment" />
            <p-columnFilter
              type="text"
              field="comment"
              display="menu"
              class="ml-auto"
            />
          </div>
        </th>

        <th pSortableColumn="status">
          <div class="flex items-center justify-between gap-2">
            Status
            <p-sortIcon field="status" />
            <p-columnFilter
              type="text"
              field="status"
              display="menu"
              class="ml-auto"
            />
          </div>
        </th>

        <th>Proof of Payment</th>

        <th>Actions</th>
      </tr>
    </ng-template>

    <!-- Table Body -->
    <ng-template pTemplate="body" let-deposit let-expanded="expanded">
      <tr>
        <td>
          <button
            type="button"
            pButton
            pRipple
            [icon]="
              expanded
                ? 'pi pi-chevron-down text-xs'
                : 'pi pi-chevron-right text-xs'
            "
            (click)="toggleRow(deposit)"
            class="p-button-text p-button-rounded p-button-plain"
            [class.expanded]="expanded"
          ></button>
        </td>
        <td>{{ deposit.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
        <td>{{ deposit.refNo }}</td>
        <td>
          {{ deposit.clientFirstName || "N/A" }}
          {{ deposit.clientLastName ? deposit.clientLastName : "" }}
        </td>
        <td>{{ deposit.amount | currency }}</td>
        <td>{{ deposit.bankName || "N/A" }}</td>
        <td>{{ deposit.comment }}</td>
        <td>
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            [ngClass]="{
              'bg-amber-100 text-amber-800': deposit.status === Status.PENDING,
              'bg-blue-100 text-blue-800': deposit.status === Status.APPROVED,
              'bg-emerald-100 text-emerald-800':
                deposit.status === Status.COMPLETED,
              'bg-zinc-100 text-zinc-800': deposit.status === Status.DRAFT,
              'bg-stone-100 text-stone-800': deposit.status === Status.INACTIVE,
              'bg-red-100 text-red-800': deposit.status === Status.DECLINED,
              'bg-orange-100 text-orange-800':
                deposit.status === Status.CANCELLED,
            }"
          >
            {{ deposit.status }}
          </span>
        </td>
        <td>
          @if (deposit.filePath) {
            <app-button
              variant="secondary"
              size="sm"
              (click)="toggleModal('viewPOP', deposit)"
            >
              View POP
            </app-button>
          }
          @if (!deposit.filePath) {
            <span>No file uploaded</span>
          }
        </td>
        <td>
          <div class="flex gap-2">
            <ng-container *appButtonPermission="'view'">
              <app-button
                variant="secondary"
                size="icon"
                (click)="toggleModal('viewItem', deposit)"
              >
                <fa-icon [icon]="eyeIcon"></fa-icon>
              </app-button>
            </ng-container>
            @if (
              deposit.status === Status.PENDING ||
              deposit.status === Status.APPROVED
            ) {
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('approveItem', deposit)"
                >
                  <fa-icon [icon]="checkIcon"></fa-icon>
                </app-button>
              </ng-container>
            }
          </div>
        </td>
      </tr>
    </ng-template>

    <!-- Expanded Row Template -->
    <ng-template pTemplate="rowexpansion" let-deposit>
      <tr>
        <td colspan="10">
          <div class="p-4 bg-gray-50 rounded-md">
            <div
              class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
            >
              <!-- Basic Information -->
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Reference No.
                </h5>
                <p class="text-gray-800">{{ deposit.refNo }}</p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">Status</h5>
                <p class="text-gray-800">{{ deposit.status }}</p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">Amount</h5>
                <p class="text-gray-800">{{ deposit.amount | currency }}</p>
              </div>

              <!-- Client Information -->
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">Client</h5>
                <p class="text-gray-800">
                  {{ deposit.clientFirstName || "N/A" }}
                  {{ deposit.clientLastName ? deposit.clientLastName : "" }}
                </p>
              </div>

              <!-- Bank Information -->
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Bank Name
                </h5>
                <p class="text-gray-800">{{ deposit.bankName || "N/A" }}</p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Bank Branch
                </h5>
                <p class="text-gray-800">
                  {{ deposit.bankBranchName || "N/A" }}
                </p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Account Number
                </h5>
                <p class="text-gray-800">
                  {{ deposit.bankAccountNumber || "N/A" }}
                </p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Account Holder
                </h5>
                <p class="text-gray-800">
                  {{ deposit.accountHolderName || "N/A" }}
                </p>
              </div>

              <!-- Comment if available -->
              @if (deposit.comment) {
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Comment
                  </h5>
                  <p class="text-gray-800">{{ deposit.comment }}</p>
                </div>
              }

              <!-- Audit -->
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Created Date
                </h5>
                <p class="text-gray-800">
                  {{ deposit.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                </p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Modified Date
                </h5>
                <p class="text-gray-800">
                  {{
                    deposit.modifiedDate
                      ? (deposit.modifiedDate | date: "yyyy-MM-dd HH:mm:ss")
                      : "-"
                  }}
                </p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Created By
                </h5>
                <p class="text-gray-800">
                  {{ deposit.createdUserName || "-" }}
                </p>
              </div>
              <div>
                <h5 class="text-sm font-semibold text-gray-600 mb-1">
                  Modified By
                </h5>
                <p class="text-gray-800">
                  {{ deposit.modifiedUserName || "-" }}
                </p>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="mt-4 flex justify-end gap-2">
              @if (deposit.filePath) {
                <app-button
                  size="sm"
                  variant="secondary"
                  (click)="toggleModal('viewPOP', deposit)"
                >
                  View Proof of Payment
                </app-button>
              }

              @if (deposit.status === Status.PENDING) {
                <app-button
                  size="sm"
                  variant="success"
                  (click)="toggleModal('approveItem', deposit)"
                >
                  Approve/Reject
                </app-button>
              }
            </div>
          </div>
        </td>
      </tr>
    </ng-template>

    <!-- Empty State -->
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="10" class="p-8 text-center">
          <div class="flex flex-col items-center gap-4">
            <i class="text-4xl text-gray-400 pi pi-inbox"></i>
            <span class="text-gray-600"
              >No bank deposit transactions found</span
            >
          </div>
        </td>
      </tr>
    </ng-template>
  </p-table>
</app-card>

<!-- View Transaction Modal -->
<app-modal
  [isVisible]="modals().viewItem"
  [config]="{ title: 'View Bank Deposit Transaction' }"
>
  @if (selectedTransaction()) {
    <div class="flex flex-col gap-4">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="form-label">Reference Number</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.refNo }}
          </p>
        </div>
        <div>
          <label class="form-label">Status</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.status }}
          </p>
        </div>
        <div>
          <label class="form-label">Amount</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.amount | currency }}
          </p>
        </div>
        <div>
          <label class="form-label">Client</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.clientFirstName || "N/A" }}
            {{
              selectedTransaction()?.clientLastName
                ? selectedTransaction()?.clientLastName
                : ""
            }}
          </p>
        </div>

        <!-- Bank information section -->
        <div>
          <label class="form-label">Bank Name</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.bankName || "N/A" }}
          </p>
        </div>
        <div>
          <label class="form-label">Bank Branch</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.bankBranchName || "N/A" }}
          </p>
        </div>
        <div>
          <label class="form-label">Account Number</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.bankAccountNumber || "N/A" }}
          </p>
        </div>
        <div>
          <label class="form-label">Account Holder</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.accountHolderName || "N/A" }}
          </p>
        </div>

        <div class="col-span-2">
          <label class="form-label">Comment</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.comment }}
          </p>
        </div>
        <div>
          <label class="form-label">Created Date</label>
          <p class="form-control bg-gray-100">
            {{
              selectedTransaction()?.createdDate | date: "yyyy-MM-dd HH:mm:ss"
            }}
          </p>
        </div>
        <div>
          <label class="form-label">Last Modified</label>
          <p class="form-control bg-gray-100">
            {{
              selectedTransaction()?.modifiedDate
                ? (selectedTransaction()?.modifiedDate
                  | date: "yyyy-MM-dd HH:mm:ss")
                : "-"
            }}
          </p>
        </div>
        <div>
          <label class="form-label">Created By</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.createdUserName || "-" }}
          </p>
        </div>
        <div>
          <label class="form-label">Modified By</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.modifiedUserName || "-" }}
          </p>
        </div>
      </div>
      @if (selectedTransaction()?.filePath) {
        <div class="flex items-center justify-center gap-3 mt-4">
          <app-button
            (click)="toggleModal('viewPOP', selectedTransaction() ?? undefined)"
            variant="secondary"
          >
            <i class="pi pi-eye mr-2"></i>
            View Proof of Payment
          </app-button>
        </div>
      }
      <div class="flex justify-end mt-4">
        <app-button (click)="toggleModal('viewItem')" variant="outline">
          Close
        </app-button>
      </div>
    </div>
  }
</app-modal>

<!-- Approve Transaction Modal -->
<app-modal
  [isVisible]="modals().approveItem"
  [config]="{ title: 'Approve Bank Deposit Transaction' }"
>
  @if (selectedTransaction()) {
    <form [formGroup]="approveDepositForm" (ngSubmit)="handleApproveDeposit()">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="form-label">Reference Number</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.refNo }}
          </p>
        </div>
        <div>
          <label class="form-label">Amount</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.amount | currency }}
          </p>
        </div>
        <div>
          <label class="form-label">Client</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.clientFirstName || "N/A" }}
            {{
              selectedTransaction()?.clientLastName
                ? selectedTransaction()?.clientLastName
                : ""
            }}
          </p>
        </div>
        <div>
          <label class="form-label">Current Status</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.status }}
          </p>
        </div>

        <!-- Bank information in approval modal -->
        <div>
          <label class="form-label">Bank Name</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.bankName || "N/A" }}
          </p>
        </div>
        <div>
          <label class="form-label">Account Number</label>
          <p class="form-control bg-gray-100">
            {{ selectedTransaction()?.bankAccountNumber || "N/A" }}
          </p>
        </div>

        <app-form-field
          class="col-span-2"
          label="Action"
          type="select"
          [options]="[
            {
              value:
                selectedTransaction()?.status === Status.APPROVED
                  ? 'COMPLETED'
                  : 'APPROVED',
              label:
                selectedTransaction()?.status === Status.APPROVED
                  ? 'Credit Client'
                  : 'Approve Deposit',
            },
            { value: 'DECLINED', label: 'Reject Deposit' },
          ]"
          [control]="approveDepositForm.get('status')!"
          [required]="true"
        />
        <app-form-field
          class="col-span-2"
          label="Comment (Optional)"
          type="textarea"
          [control]="approveDepositForm.get('comment')!"
          [required]="false"
        />
      </div>
      <div class="flex justify-end gap-3 mt-4">
        <app-button (click)="toggleModal('approveItem')" variant="outline">
          Cancel
        </app-button>
        <app-button
          type="submit"
          [variant]="
            approveDepositForm.get('status')?.value === 'COMPLETED' ||
            approveDepositForm.get('status')?.value === 'APPROVED'
              ? 'primary'
              : 'danger'
          "
        >
          {{
            approveDepositForm.get("status")?.value === "APPROVED"
              ? "Approve"
              : approveDepositForm.get("status")?.value === "COMPLETED"
                ? "Credit Client"
                : "Reject"
          }}
        </app-button>
      </div>
    </form>
  }
</app-modal>

<!-- View Proof of Payment Modal -->
<app-modal
  [isVisible]="modals().viewPOP"
  [config]="{ title: 'Proof of Payment' }"
>
  <div class="flex flex-col items-center">
    <!-- Loading indicator -->
    @if (!currentPOPFileType()) {
      <div class="flex items-center justify-center py-8">
        <div class="flex flex-col items-center gap-4">
          <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
          <span class="text-gray-600">Loading file information...</span>
        </div>
      </div>
    }

    <!-- Image Preview with Error Handling -->
    @if (
      isCurrentPOPViewable() &&
      currentPOPUrl() &&
      (currentPOPFileType()?.startsWith("image/") || false)
    ) {
      <div class="mb-4 max-h-96 overflow-auto w-full">
        @if (!imageLoadError()) {
          <img
            [src]="currentPOPUrl() || '' | safe: 'resourceUrl'"
            alt="Proof of Payment"
            class="max-w-full h-auto mx-auto border rounded"
            (error)="onImageError()"
          />
        } @else {
          <div class="text-center py-8 px-4 bg-gray-100 rounded-lg">
            <i class="pi pi-image text-4xl text-gray-400 mb-3 block"></i>
            <p class="text-gray-700 mb-2">Unable to display this image</p>
            <p class="text-gray-500 text-sm">
              The image may be in an unsupported format or inaccessible.
            </p>
          </div>
        }
      </div>
    }

    <!-- PDF Preview -->
    @if (
      isCurrentPOPViewable() &&
      currentPOPUrl() &&
      currentPOPFileType() === "application/pdf"
    ) {
      <div class="mb-4 w-full">
        <div class="bg-gray-100 py-3 px-4 rounded text-center mb-2">
          <p>
            PDF documents cannot be embedded directly. Please use the download
            button below.
          </p>
        </div>
        <div class="flex justify-center">
          <i class="pi pi-file-pdf text-5xl text-red-500"></i>
        </div>
      </div>
    }

    <!-- Non-viewable file type info -->
    @if (!isCurrentPOPViewable() && currentPOPFileType()) {
      <div class="mb-4 text-center p-6">
        <i
          [class]="getFileIcon(currentPOPFileType())"
          class="text-5xl mb-4 text-gray-700"
        ></i>
        <p class="mb-4">
          This file type ({{ currentPOPFileType() }}) cannot be previewed
          directly.
        </p>
        <p class="text-gray-600">
          File:
          {{ selectedTransaction()?.originalFilename || "proof-of-payment" }}
        </p>
      </div>
    }

    <!-- Action buttons -->
    <div class="flex justify-end w-full mt-4 gap-2">
      @if (currentPOPFileType()) {
        <app-button (click)="downloadPOP()" variant="secondary">
          <i class="pi pi-download mr-2"></i>
          Download
        </app-button>
      }
      <app-button (click)="toggleModal('viewPOP')" variant="outline">
        Close
      </app-button>
    </div>
  </div>
</app-modal>

<p-toast />
