import { CommonModule, DatePipe } from "@angular/common";
import {
  Component,
  computed,
  inject,
  On<PERSON><PERSON><PERSON>,
  OnInit,
  signal,
} from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router, RouterModule } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import {
  faCheck,
  faEdit,
  faEye,
  faTimes,
} from "@fortawesome/free-solid-svg-icons";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableLazyLoadEvent, TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from "rxjs";
import Swal from "sweetalert2";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Status } from "@/app/core/enums/status.enum";
import { SafePipe } from "@/app/core/pipes/safe.pipe";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";

import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { TextComponent } from "../../shared/ui/text/text.component";

import {
  ApproveBankDepositRequest,
  BankDepositTransaction,
  ServerSideTableRequest,
  ServerSideTableResponse,
} from "./bank-deposits.model";
import { BankDepositsService } from "./bank-deposits.service";

type ModalKey = "viewItem" | "approveItem" | "viewPOP";

@Component({
  selector: "app-bank-deposits",
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    FontAwesomeModule,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    DatePipe,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    ModalComponent,
    FormFieldComponent,
    ButtonPermissionDirective,
    SafePipe,
  ],
  templateUrl: "./bank-deposits.component.html",
  providers: [DatePipe],
})
export class BankDepositsComponent implements OnInit, OnDestroy {
  // Dependency Injection using inject()
  private readonly bankDepositsService = inject(BankDepositsService);
  private readonly fb = inject(FormBuilder);
  private readonly datePipe = inject(DatePipe);
  protected readonly reportExportService = inject(ReportExportService);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);

  // Constants
  readonly Status = Status;
  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;
  readonly checkIcon = faCheck;
  readonly timesIcon = faTimes;

  // UI state signals
  imageLoadError = signal<boolean>(false);
  isDataLoading = signal<boolean>(false);

  // Data signals
  bankDeposits = signal<BankDepositTransaction[]>([]);
  totalRecords = signal<number>(0);
  selectedTransaction = signal<BankDepositTransaction | null>(null);
  currentPOPUrl = signal<string | null>(null);
  currentPOPFileType = signal<string | null>(null);
  isCurrentPOPViewable = signal<boolean>(false);
  expandedRows = signal<Record<string, boolean>>({});

  // Search functionality
  searchTerm = signal<string>("");

  // Modal states - using signal for reactive UI updates
  modals = signal<Record<ModalKey, boolean>>({
    viewItem: false,
    approveItem: false,
    viewPOP: false,
  });

  // Server-side table state
  tableState = signal<ServerSideTableRequest>({
    page: 0,
    size: 10,
    sortField: "createdDate",
    sortOrder: "desc",
    filters: {},
    globalFilter: "",
    statusFilter: null,
  });

  // Computed properties for exports
  exportData = computed(() =>
    this.bankDeposits().map((deposit) => ({
      "Reference Number": deposit.refNo,
      Status: deposit.status,
      Amount: deposit.amount,
      Comment: deposit.comment,
      "Client First Name": deposit.clientFirstName || "",
      "Client Last Name": deposit.clientLastName || "",
      "Bank Name": deposit.bankName || "",
      "Bank Branch": deposit.bankBranchName || "",
      "Account Number": deposit.bankAccountNumber || "",
      "Account Holder": deposit.accountHolderName || "",
      "Created Date": this.datePipe.transform(
        deposit.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": deposit.modifiedDate
        ? this.datePipe.transform(deposit.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": deposit.createdUserName || "-",
      "Modified By": deposit.modifiedUserName || "-",
    })),
  );

  // Reactive forms
  approveDepositForm = this.fb.nonNullable.group({
    transactionId: ["", [Validators.required]],
    status: ["", [Validators.required]],
    comment: [""],
  }) as FormGroup<FormControlType<ApproveBankDepositRequest>>;

  // RxJS cleanup
  private searchTermSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    // Set up debounced search - must be before URL parameter reading
    this.setupSearchDebounce();

    // Read and react to URL parameters
    this.subscribeToRouteParams();
  }

  private subscribeToRouteParams(): void {
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe((params) => {
        // Flag to determine if we need to load data after state updates
        let shouldLoadData = true;

        // If no status in URL, set default status to PENDING and update URL
        // This will trigger another route param update, so skip loading data
        if (!params["status"]) {
          shouldLoadData = false;
          this.updateUrlParams({
            status: Status.PENDING,
            page: "0",
            // Preserve search term if it exists
            ...(params["search"] && { search: params["search"] }),
          });
          return; // The subscription will trigger again with updated params
        }

        // Get search term from URL (will be undefined if not present)
        const urlSearchTerm = params["search"] || "";

        // Always sync component's searchTerm with URL parameter
        // This ensures URL is the source of truth
        this.searchTerm.set(urlSearchTerm);

        // Update table state from URL parameters
        this.tableState.update((state) => ({
          ...state,
          page: Number(params["page"]) || 0,
          size: Number(params["size"]) || 10,
          sortField: params["sortField"] || "createdDate",
          sortOrder: (params["sortOrder"] as "asc" | "desc") || "desc",
          globalFilter: urlSearchTerm, // Sync global filter with URL search param
          statusFilter: (params["status"] as Status) || Status.PENDING,
        }));

        // Load data if needed
        if (shouldLoadData) {
          this.loadServerSideData();
        }
      });
  }

  // Modified onSearch method to handle empty string case
  onSearch(term: string): void {
    // Don't update if it's the same (prevents unnecessary navigation)
    if (term === this.searchTerm()) return;

    // Update local state immediately for UI responsiveness
    this.searchTerm.set(term);

    // Trigger the debounced search
    this.searchTermSubject.next(term);
  }

  // Modified setupSearchDebounce method with explicit empty string handling
  private setupSearchDebounce(): void {
    this.searchTermSubject
      .pipe(takeUntil(this.destroy$), debounceTime(400), distinctUntilChanged())
      .subscribe((term) => {
        // Store active element reference before navigation
        const activeElement = document.activeElement as HTMLElement;
        const isSearchFocused = activeElement?.id === "search-input";

        // Create updates object with explicit null for empty terms
        const updates: Record<string, string | null> = {
          page: "0", // Reset to first page on new search
        };

        // Empty string check with trim to catch whitespace-only inputs
        if (!term || term.trim() === "") {
          updates["search"] = null; // Explicitly set to null to remove param
        } else {
          updates["search"] = term; // Non-empty term
        }

        // Apply the updates
        this.updateUrlParams(updates);

        // Restore focus after a short delay
        if (isSearchFocused) {
          setTimeout(() => {
            document.getElementById("search-input")?.focus();
          }, 100);
        }
      });
  }

  // Modified updateUrlParams to be more explicit with null handling
  private updateUrlParams(updates: Record<string, string | null>): void {
    const currentParams = { ...this.route.snapshot.queryParams };

    // Process each update
    Object.entries(updates).forEach(([key, value]) => {
      if (value === null) {
        // Explicitly remove parameters with null values
        delete currentParams[key];
      } else if (typeof value === "string" && value.trim() === "") {
        // Also remove parameters with empty or whitespace-only strings
        delete currentParams[key];
      } else {
        // Otherwise set the parameter
        currentParams[key] = value;
      }
    });

    // Navigate with updated params
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: currentParams,
      replaceUrl: true, // Replace URL to avoid history stack issues
    });
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.destroy$.next();
    this.destroy$.complete();

    // Clean up any blob URLs to prevent memory leaks
    if (this.currentPOPUrl() && this.currentPOPUrl()?.startsWith("blob:")) {
      URL.revokeObjectURL(this.currentPOPUrl()!);
    }
  }

  loadServerSideData(): void {
    if (this.isDataLoading()) {
      return;
    }

    this.isDataLoading.set(true);

    this.bankDepositsService
      .getTransactionsServerSide(this.tableState())
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: ServerSideTableResponse<BankDepositTransaction>) => {
          this.bankDeposits.set(response.data?.data || []);
          this.totalRecords.set(response.data?.totalRecords || 0);
          this.isDataLoading.set(false);
        },
        error: (error) => {
          this.bankDeposits.set([]);
          handleError(error, "Failed to load bank deposit transactions");
          this.isDataLoading.set(false);
        },
      });
  }

  updateStatusFilter(status: Status | null): void {
    const updates: Record<string, string | null> = {
      page: "0", // Reset to first page on filter change
      status: status || null, // This will remove status param when null
    };

    this.updateUrlParams(updates);
  }

  onLazyLoad(event: TableLazyLoadEvent): void {
    // Check if we're already loading
    if (this.isDataLoading()) {
      return;
    }

    const page =
      event.first !== undefined
        ? Math.floor(event.first / (event.rows ?? 10))
        : 0;

    const updates: Record<string, string> = {
      page: page.toString(),
      size: (event.rows ?? 10).toString(),
    };

    if (event.sortField) {
      updates["sortField"] = event.sortField as string;
      updates["sortOrder"] = event.sortOrder === 1 ? "asc" : "desc";
    }

    if (event.filters) {
      Object.entries(event.filters).forEach(([key, filterMeta]) => {
        if (Array.isArray(filterMeta)) {
          const values = filterMeta
            .map((meta) => meta.value)
            .filter((value) => value !== null && value !== undefined);
          if (values.length > 0) {
            updates[`filter_${key}`] = values.join(",");
          }
        } else if (
          typeof filterMeta === "object" &&
          filterMeta !== null &&
          "value" in filterMeta
        ) {
          const value = filterMeta.value;
          if (value !== null && value !== undefined && value !== "") {
            updates[`filter_${key}`] = String(value);
          }
        }
      });
    }

    this.updateUrlParams(updates);
  }

  toggleRow(transaction: BankDepositTransaction): void {
    const currentExpandedRows = this.expandedRows();

    this.expandedRows.update((rows) => {
      const newRows = { ...rows };

      if (newRows[transaction.id]) {
        delete newRows[transaction.id];
      } else {
        newRows[transaction.id] = true;
      }

      return newRows;
    });
  }

  handleApproveDeposit(): void {
    if (this.approveDepositForm.invalid) {
      markFormGroupTouched(this.approveDepositForm);
      return;
    }

    const formValue = this.approveDepositForm
      .value as ApproveBankDepositRequest;
    const isApproving =
      formValue.status === Status.COMPLETED ||
      formValue.status === Status.APPROVED;
    let actionText = isApproving ? "approve" : "reject";

    // Determine the specific action text based on the status
    if (formValue.status === Status.COMPLETED) {
      actionText = "credit";
    } else if (formValue.status === Status.APPROVED) {
      actionText = "approve";
    }

    this.showConfirmationDialog(actionText, formValue);
  }

  private showConfirmationDialog(
    actionText: string,
    formValue: ApproveBankDepositRequest,
  ): void {
    Swal.fire({
      title: `Are you sure?`,
      text: `You want to ${actionText} this bank deposit transaction?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: `Yes, ${actionText} it!`,
    }).then((result) => {
      if (result.isConfirmed) {
        this.submitApproval(actionText, formValue);
      }
    });
  }

  private submitApproval(
    actionText: string,
    formValue: ApproveBankDepositRequest,
  ): void {
    this.bankDepositsService
      .approveTransaction(formValue)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          showToast({
            message: `Bank deposit transaction has been ${actionText}ed successfully`,
            type: "success",
          });
          this.loadServerSideData();
          this.toggleModal("approveItem");
          this.approveDepositForm.reset();
        },
        error: (error) => {
          handleError(error);
        },
      });
  }

  toggleModal(mode: ModalKey, transaction?: BankDepositTransaction): void {
    this.selectedTransaction.set(transaction || null);

    // Reset error state when opening modal
    if (mode === "viewPOP") {
      this.imageLoadError.set(false);
    }

    if (mode === "approveItem" && transaction) {
      // Set the appropriate default status based on the current transaction status
      const defaultStatus =
        transaction.status === Status.APPROVED
          ? Status.COMPLETED
          : Status.APPROVED;

      this.approveDepositForm.patchValue({
        transactionId: transaction.transactionId,
        status: defaultStatus,
        comment: "",
      });
    } else if (mode === "viewPOP" && transaction && transaction.filePath) {
      // Reset file viewing states
      this.currentPOPUrl.set(null);
      this.currentPOPFileType.set(null);
      this.isCurrentPOPViewable.set(false);

      // Create a blob URL to better handle the image/file
      this.loadProofOfPayment(transaction.transactionId);
    }

    this.modals.update((currentModals) => ({
      ...currentModals,
      [mode]: !currentModals[mode],
    }));
  }

  // Handle image load errors
  onImageError(): void {
    this.imageLoadError.set(true);
    console.error("Image failed to load:", this.currentPOPUrl());
  }

  // Load the file with better error handling
  loadProofOfPayment(transactionId: string): void {
    // Get the file type from filename first
    const selectedTransaction = this.selectedTransaction();
    if (selectedTransaction) {
      const fileType = this.guessFileTypeFromPath(selectedTransaction.filePath);
      this.currentPOPFileType.set(fileType);
      this.isCurrentPOPViewable.set(
        this.bankDepositsService.isViewableFileType(fileType),
      );
    }

    // Use the download method to get the blob and create a local URL
    this.bankDepositsService
      .downloadFile(transactionId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (blob) => {
          this.handleFileDownloadSuccess(blob);
        },
        error: (error) => {
          this.handleFileDownloadError(error, transactionId);
        },
      });
  }

  private handleFileDownloadSuccess(blob: Blob): void {
    // Clean up previous blob URL if exists
    const currentUrl = this.currentPOPUrl();
    if (currentUrl && currentUrl.startsWith("blob:")) {
      URL.revokeObjectURL(currentUrl);
    }

    // Create a blob URL for viewing in the browser
    this.currentPOPUrl.set(URL.createObjectURL(blob));

    // Try to get the correct MIME type from the blob
    const currentFileType = this.currentPOPFileType();
    const fileType = blob.type || currentFileType;
    this.currentPOPFileType.set(fileType);
    this.isCurrentPOPViewable.set(
      this.bankDepositsService.isViewableFileType(fileType),
    );

    // Reset error state
    this.imageLoadError.set(false);
  }

  private handleFileDownloadError(error: any, transactionId: string): void {
    console.error("Error loading file:", error);
    this.imageLoadError.set(true);

    // Fallback to direct URL method
    this.currentPOPUrl.set(
      this.bankDepositsService.getProxiedFileUrl(transactionId),
    );

    showToast({
      message: "Error loading file. You may still try downloading it.",
      type: "error",
    });
  }

  downloadPOP(): void {
    const selectedTransaction = this.selectedTransaction();
    if (!selectedTransaction) return;

    const filename = selectedTransaction.originalFilename || "proof-of-payment";

    // Show a loading toast
    showToast({
      message: `Preparing download...`,
      type: "info",
    });

    // If we already have a blob URL, we can download directly
    const currentUrl = this.currentPOPUrl();
    if (currentUrl && currentUrl.startsWith("blob:")) {
      this.downloadFromBlobUrl(currentUrl, filename);
      return;
    }

    // Otherwise use the service method for a fresh download
    this.downloadFreshFile(selectedTransaction.transactionId, filename);
  }

  private downloadFromBlobUrl(url: string, filename: string): void {
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showToast({
      message: `Downloaded ${filename} successfully`,
      type: "success",
    });
  }

  private downloadFreshFile(transactionId: string, filename: string): void {
    this.bankDepositsService
      .downloadFile(transactionId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          showToast({
            message: `Downloaded ${filename} successfully`,
            type: "success",
          });
        },
        error: (error) => {
          console.error("Download failed:", error);
          showToast({
            message: `Failed to download file. Please try again.`,
            type: "error",
          });
        },
      });
  }

  // Helper to guess file type from path
  guessFileTypeFromPath(filePath: string): string {
    if (!filePath) return "application/octet-stream";

    const extension = filePath.split(".").pop()?.toLowerCase();

    switch (extension) {
      case "jpg":
      case "jpeg":
        return "image/jpeg";
      case "png":
        return "image/png";
      case "pdf":
        return "application/pdf";
      case "txt":
        return "text/plain";
      default:
        return "application/octet-stream";
    }
  }

  // Get file icon
  getFileIcon(fileType: string | null): string {
    return this.bankDepositsService.getFileIcon(fileType);
  }
}
