import { DatePipe } from "@angular/common";
import { Component, inject, OnInit, signal } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { TableModule } from "primeng/table";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { handleError } from "@/app/core/utils/error-handler.util";
import { Option } from "@/app/core/utils/map-to-options.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { onlyLettersAndHyphensValidator } from "@/app/core/validators/custom.validators";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import {
  ClientType,
  CreateClientTypeRequest,
  UpdateClientTypeRequest,
} from "./client-types.model";
import { ClientTypesService } from "./client-types.service";

// PrimeNG imports
import { ReportExportService } from "@/app/core/services/report-export-service";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";

type ModalKey = "addItem" | "editItem" | "viewItem";

@Component({
  selector: "app-clientTypes",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ModalComponent,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    FormFieldComponent,
    ButtonPermissionDirective,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    DatePipe,
  ],
  templateUrl: "./client-types.component.html",
  providers: [DatePipe],
})
export class ClientTypesComponent implements OnInit {
  private clientTypesService = inject(ClientTypesService);
  private fb = inject(FormBuilder);
  private datePipe = inject(DatePipe);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;

  // Use signals for reactive state management
  filteredClientTypes = signal<ClientType[]>([]);
  clientTypes = signal<ClientType[]>([]);

  selectedClientType: ClientType | null = null;
  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  get exportData(): any[] {
    return this.filteredClientTypes().map((type) => ({
      Type: type.type,
      Status: type.status,
      "Created Date": this.datePipe.transform(
        type.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": type.modifiedDate
        ? this.datePipe.transform(type.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": type.createdUserName ?? "-",
      "Modified By": type.modifiedUserName ?? "-",
    }));
  }

  get clientTypeTableOptions(): Option[] {
    return [];
  }

  get exchangeOptions(): Option[] {
    return [
      { value: Exchange.VFEX, label: Exchange.VFEX },
      { value: Exchange.ZSE, label: Exchange.ZSE },
    ];
  }

  addClientTypeForm = this.fb.nonNullable.group({
    type: ["", [Validators.required, onlyLettersAndHyphensValidator()]],
  }) as FormGroup<FormControlType<CreateClientTypeRequest>>;

  editClientTypeForm = this.fb.nonNullable.group({
    id: ["", [Validators.required]],
    type: ["", [Validators.required, onlyLettersAndHyphensValidator()]],
    status: ["", [Validators.required]],
  }) as FormGroup<FormControlType<UpdateClientTypeRequest>>;

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  isDataLoading = false;
  isClientTypeTablesLoading = false;

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadData();
    });
  }

  toggleRow(clientType: ClientType): void {
    if (this.expandedRows[clientType.id]) {
      delete this.expandedRows[clientType.id];
    } else {
      this.expandedRows[clientType.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.clientTypes();

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (type) => type?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (type: any) =>
          type.type.toLowerCase().includes(term) ||
          type.status.toLowerCase().includes(term) ||
          (type.id && type.id.toLowerCase().includes(term)),
      );
    }

    this.filteredClientTypes.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  loadData(): void {
    this.isDataLoading = true;
    this.clientTypesService.getClientTypes().subscribe({
      next: (response) => {
        this.clientTypes.set(response.data);

        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);

        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load client types");
        this.isDataLoading = false;
      },
    });
  }

  toggleModal(mode: ModalKey, clientType?: ClientType): void {
    this.selectedClientType = clientType || null;

    if (mode === "editItem" && clientType) {
      this.editClientTypeForm.patchValue({
        id: clientType.id,
        type: clientType.type,
        status: clientType.status,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  handleClientTypeSubmit(mode: "add" | "update"): void {
    const form =
      mode === "add" ? this.addClientTypeForm : this.editClientTypeForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.clientTypesService.createClientType(
            form.value as CreateClientTypeRequest,
          )
        : this.clientTypesService.updateClientType(
            form.value as UpdateClientTypeRequest,
          );

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Client type has been added successfully`
              : `Client type has been updated successfully`,
          type: "success",
        });
        this.loadData();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(error, `Failed to ${mode} client type: ${error.message}`);
      },
    });
  }
}
