import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import {
  CreateClientTypeRequest,
  UpdateClientTypeRequest,
} from "./client-types.model";

@Injectable({
  providedIn: "root",
})
export class ClientTypesService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getClientTypes(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/public/client-types/admin`);
  }

  public createClientType(
    clientTypeData: CreateClientTypeRequest,
  ): Observable<any> {
    return this.http.post<any>(
      `${this.apiServerUrl}/public/client-types`,
      clientTypeData,
    );
  }

  public updateClientType(
    clientTypeData: UpdateClientTypeRequest,
  ): Observable<any> {
    return this.http.put<any>(
      `${this.apiServerUrl}/public/client-types`,
      clientTypeData,
    );
  }
}
