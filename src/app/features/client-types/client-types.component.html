<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter dropdown -->
      <div class="mr-3 flex items-center">
        <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
        <div class="relative flex items-center">
          <i class="pi pi-filter absolute left-2 text-gray-500"></i>
          <select
            [ngModel]="statusFilter"
            (ngModelChange)="updateStatusFilter($event)"
            class="pl-8 pr-4 py-2 border rounded-lg text-sm"
            >
            <option [value]="null">All Statuses</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
        </div>
      </div>
    </ng-template>

    <ng-template #end>
      <div class="flex gap-2">
        <ng-container *appButtonPermission="'create'">
          <app-button size="sm" class="!gap-2" (click)="toggleModal('addItem')">
            <i class="text-sm pi pi-plus"></i>
            Create
          </app-button>
        </ng-container>
        <app-button
          size="sm"
          class="!gap-2"
          variant="secondary"
          (click)="
            reportExportService.exportToExcel(exportData, 'client-types.xlsx')
          "
          >
          <i class="text-sm pi pi-upload"></i>
          Export
        </app-button>
      </div>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isDataLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading client types...</span>
      </div>
    </div>
  } @else {
    <!-- Client Types Table -->
    <p-table
      [value]="filteredClientTypes()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
      >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold">Manage Client Types</app-text>
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />
            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search client types..."
              />
          </p-iconfield>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>
          <th pSortableColumn="createdDate">
            <div class="flex items-center gap-2">
              Created Date
              <p-sortIcon field="createdDate" />
            </div>
          </th>
          <th pSortableColumn="type">
            <div class="flex items-center justify-between gap-2">
              Type
              <p-sortIcon field="type" />
              <p-columnFilter
                type="text"
                field="type"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>
          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>
          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-clientType let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(clientType)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ clientType.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ clientType.type }}</td>
          <td>{{ clientType.status }}</td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button
                  size="icon"
                  (click)="toggleModal('viewItem', clientType)"
                  >
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', clientType)"
                  >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-clientType>
        <tr>
          <td colspan="5">
            <div class="p-4 bg-gray-50 rounded-md">
              <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                <!-- Basic Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">Type</h5>
                  <p class="text-gray-800">{{ clientType.type }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">
                    {{ clientType.status }}
                  </p>
                </div>
                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ clientType.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                    clientType.modifiedDate
                    ? (clientType.modifiedDate
                    | date: "yyyy-MM-dd HH:mm:ss")
                    : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ clientType.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ clientType.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Description if available -->
              @if (clientType.description) {
                <div class="mt-4">
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Description
                  </h5>
                  <p class="text-gray-800 whitespace-pre-line">
                    {{ clientType.description }}
                  </p>
                </div>
              }

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  (click)="toggleModal('editItem', clientType)"
                  *appButtonPermission="'edit'"
                  >
                  Edit
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="5" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No client types found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- Add ClientType Modal -->
<app-modal [isVisible]="modals.addItem" [config]="{ title: 'Add Client Type' }">
  <form
    [formGroup]="addClientTypeForm"
    (ngSubmit)="handleClientTypeSubmit('add')"
    class="flex flex-col gap-6"
    >
    <app-form-field
      label="Type"
      type="text"
      [control]="addClientTypeForm.controls.type"
      [required]="true"
      />

    <div class="flex items-center justify-end gap-3">
      <app-button (click)="toggleModal('addItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- Edit ClientType Modal -->
<app-modal
  [isVisible]="modals.editItem"
  [config]="{ title: 'Edit Client Type' }"
  >
  <form
    [formGroup]="editClientTypeForm"
    (ngSubmit)="handleClientTypeSubmit('update')"
    >
    <div class="flex flex-col gap-3">
      <app-form-field
        label="Type"
        type="text"
        [control]="editClientTypeForm.controls.type!"
        [required]="true"
        />

      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
        [control]="editClientTypeForm.get('status')!"
        [required]="true"
        />
    </div>

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="toggleModal('editItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- View ClientType Modal -->
<app-modal
  [isVisible]="modals.viewItem"
  [config]="{ title: 'View Client Type' }"
  >
  <div class="flex flex-col gap-3">
    <div>
      <label class="form-label">Type</label>
      <input
        type="text"
        [value]="selectedClientType?.type"
        class="form-control"
        disabled
        />
    </div>

    <div>
      <label class="form-label">Status</label>
      <input
        type="text"
        [value]="selectedClientType?.status"
        class="form-control"
        disabled
        />
    </div>

    <div class="flex justify-end">
      <app-button (click)="toggleModal('viewItem')">Close</app-button>
    </div>
  </div>
</app-modal>

<p-toast />
