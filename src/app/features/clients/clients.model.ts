import { Status } from "@/app/core/enums/status.enum";
import { BaseResponse } from "@/app/core/interfaces/common.interface";
import { ClientType } from "../client-types/client-types.model";

export interface Client extends BaseResponse {
  address: string;
  title: string;
  passportNo: string;
  nationalId: string;
  gender: string;
  dob: string;
  mobileToken: string;
  foreignLocal: string;
  clientType: ClientType;
  companyDetail?: CompanyDetail;
  emailVerification: true;
  lastName: string;
  firstName: string;
  email: string;
  city: {
    name: string;
    id: string;
  };
  emailToken: string;
  mobile: string;
}

export interface CreateClientRequest {
  title: string;
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  cityId: string;
  clientTypeId: string;
  nationalId: string;
  mobile: string;
  passportNo: string;
  address: string;
  dob: string;
  foreignLocal: string;
  gender: string;
  // Company details (optional)
  companyName?: string;
  companyRegNo?: string;
  sectorId?: string;
  companyEmail?: string;
  companyPhone?: string;
  companyTypeId?: string;
  companyAddress?: string;
  companyCityId?: string;
}

export interface UpdateClientRequest {
  id: string;
  title: string;
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  cityId: string;
  clientTypeId: string;
  nationalId: string;
  mobile: string;
  passportNo: string;
  address: string;
  dob: string;
  foreignLocal: string;
  gender: string;

  // Company details (optional)
  companyName?: string;
  companyRegNo?: string;
  sectorId?: string;
  companyEmail?: string;
  companyPhone?: string;
  companyTypeId?: string;
  companyAddress?: string;
  companyCityId?: string;

  status: Status;
}

export interface CompanyDetail {
  name: string;
  id: string;
  address: string;
  phone: string;
  regNo: string;
  companyType: CompanyType;
  email: string;
  city: {
    name: string;
    id: string;
  };
  sector: {
    id: string;
    sector: string;
  };
}

export interface CompanyType {
  id: string;
  type: string;
  status: Status;
  createdDate: string;
  modifiedDate: string;
  createdBy: string;
  modifiedBy: string;
  systemField: string;
}
