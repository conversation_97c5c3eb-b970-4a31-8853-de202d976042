import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { CreateClientRequest, UpdateClientRequest } from "./clients.model";

@Injectable({
  providedIn: "root",
})
export class ClientsService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getClients(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/admin/clients`);
  }

  public createClient(clientData: CreateClientRequest): Observable<any> {
    return this.http.post<any>(
      `${this.apiServerUrl}/admin/clients`,
      clientData,
    );
  }

  public updateClient(clientData: UpdateClientRequest): Observable<any> {
    return this.http.put<any>(
      `${this.apiServerUrl}/admin/clients/${clientData.id}`,
      clientData,
    );
  }
}
