<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter dropdown -->
      <div class="mr-3 flex items-center">
        <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
        <div class="relative flex items-center">
          <i class="pi pi-filter absolute left-2 text-gray-500"></i>
          <select
            [ngModel]="statusFilter"
            (ngModelChange)="updateStatusFilter($event)"
            class="pl-8 pr-4 py-2 border rounded-lg text-sm"
            >
            <option [value]="null">All Statuses</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
        </div>
      </div>
    </ng-template>

    <ng-template #end>
      <app-button
        size="sm"
        class="!gap-2"
        variant="secondary"
        (click)="reportExportService.exportToExcel(exportData, 'clients.xlsx')"
        >
        <i class="text-sm pi pi-upload"></i>
        Export
      </app-button>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isDataLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading clients...</span>
      </div>
    </div>
  } @else {
    <!-- Clients Table -->
    <p-table
      [value]="filteredClients()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
      >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold">Manage Clients</app-text>
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />

            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search clients..."
              />
          </p-iconfield>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>
          <th pSortableColumn="createdDate">
            <div class="flex items-center gap-2">
              Created Date
              <p-sortIcon field="createdDate" />
            </div>
          </th>

          <th pSortableColumn="firstName">
            <div class="flex items-center justify-between gap-2">
              Name
              <p-sortIcon field="firstName" />
              <p-columnFilter
                type="text"
                field="firstName"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>

          <th pSortableColumn="title">Title</th>
          <th pSortableColumn="email">Email</th>
          <th pSortableColumn="city.name">City</th>
          <th pSortableColumn="clientType.type">Client Type</th>
          <th pSortableColumn="nationalId">National ID</th>

          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>

          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-client let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(client)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ client.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ client.firstName }} {{ client.lastName }}</td>
          <td>{{ client.title }}</td>
          <td>{{ client.email }}</td>
          <td>{{ client.city.name }}</td>
          <td>{{ client.clientType.type }}</td>
          <td>{{ client.nationalId }}</td>
          <td>{{ client.status }}</td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button
                  size="icon"
                  (click)="toggleModal('viewItem', client)"
                  >
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', client)"
                  >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-client>
        <tr>
          <td colspan="12">
            <div class="p-4 bg-gray-50 rounded-md">
              @if (client.clientType.type === "Corporate") {
                <h5 class="text-lg font-bold text-gray-600 mb-3">
                  Contact Person Details
                </h5>
              }
              <div
                class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
                >
                <!-- Basic Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">Name</h5>
                  <p class="text-gray-800">
                    {{ client.firstName }} {{ client.lastName }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Email
                  </h5>
                  <p class="text-gray-800">{{ client.email }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">
                    {{ client.status }}
                  </p>
                </div>

                <!-- Personal Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Mobile
                  </h5>
                  <p class="text-gray-800">{{ client.mobile }}</p>
                </div>

                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Address
                  </h5>
                  <p class="text-gray-800">{{ client.address }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    National ID
                  </h5>
                  <p class="text-gray-800">{{ client.nationalId }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Gender
                  </h5>
                  <p class="text-gray-800">{{ client.gender }}</p>
                </div>

                <!-- Additional Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Date of Birth
                  </h5>
                  <p class="text-gray-800">{{ client.dob }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Passport No
                  </h5>
                  <p class="text-gray-800">{{ client.passportNo }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Foreign/Local
                  </h5>
                  <p class="text-gray-800">{{ client.foreignLocal }}</p>
                </div>

                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ client.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                    client.modifiedDate
                    ? (client.modifiedDate | date: "yyyy-MM-dd HH:mm:ss")
                    : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ client.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ client.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Company Details if Corporate Client -->
              @if (client.clientType.type === 'Corporate') {
                <div class="mt-4">
                  <h5 class="text-lg font-bold text-gray-600 mb-3">
                    Company Details
                  </h5>
                  <div
                    class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
                    >
                    <div>
                      <h5 class="text-sm font-semibold text-gray-600 mb-1">
                        Company Name
                      </h5>
                      <p class="text-gray-800">
                        {{ client.companyDetail?.name || "N/A" }}
                      </p>
                    </div>
                    <div>
                      <h5 class="text-sm font-semibold text-gray-600 mb-1">
                        Company Reg No
                      </h5>
                      <p class="text-gray-800">
                        {{ client.companyDetail?.regNo || "N/A" }}
                      </p>
                    </div>
                    <div>
                      <h5 class="text-sm font-semibold text-gray-600 mb-1">
                        Company Email
                      </h5>
                      <p class="text-gray-800">
                        {{ client.companyDetail?.email || "N/A" }}
                      </p>
                    </div>
                    <div>
                      <h5 class="text-sm font-semibold text-gray-600 mb-1">
                        Company Phone
                      </h5>
                      <p class="text-gray-800">
                        {{ client.companyDetail?.phone || "N/A" }}
                      </p>
                    </div>
                    <div>
                      <h5 class="text-sm font-semibold text-gray-600 mb-1">
                        Company Address
                      </h5>
                      <p class="text-gray-800">
                        {{ client.companyDetail?.address || "N/A" }}
                      </p>
                    </div>
                    <div>
                      <h5 class="text-sm font-semibold text-gray-600 mb-1">
                        Sector
                      </h5>
                      <p class="text-gray-800">
                        {{ client.companyDetail?.sector?.sector || "N/A" }}
                      </p>
                    </div>
                    <div>
                      <h5 class="text-sm font-semibold text-gray-600 mb-1">
                        Company Type
                      </h5>
                      <p class="text-gray-800">
                        {{ client.companyDetail?.companyType?.type || "N/A" }}
                      </p>
                    </div>
                    <div>
                      <h5 class="text-sm font-semibold text-gray-600 mb-1">
                        Company City
                      </h5>
                      <p class="text-gray-800">
                        {{ client.companyDetail?.city?.name || "N/A" }}
                      </p>
                    </div>
                  </div>
                </div>
              }

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  (click)="toggleModal('editItem', client)"
                  >
                  Edit
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="12" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No clients found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- Add Client Modal -->
<app-modal [isVisible]="modals.addItem" [config]="{ title: 'Add Client' }">
  <form
    [formGroup]="addClientForm"
    (ngSubmit)="handleClientSubmit('add')"
    class="flex flex-col gap-6"
    >
    <div class="grid gap-3 md:grid-cols-2">
      <app-form-field
        label="Title"
        type="text"
        [control]="addClientForm.controls.title"
        [required]="true"
        />
      <app-form-field
        label="First Name"
        type="text"
        [control]="addClientForm.controls.firstName"
        [required]="true"
        />
      <app-form-field
        label="Last Name"
        type="text"
        [control]="addClientForm.controls.lastName"
        [required]="true"
        />
      <app-form-field
        label="Email"
        type="email"
        [control]="addClientForm.controls.email"
        [required]="true"
        />
      <app-form-field
        label="Password"
        type="password"
        [control]="addClientForm.controls.password"
        [required]="true"
        />
      <!-- <app-form-field
      label="City"
      type="select"
      [options]="cities"
      [control]="addClientForm.controls.cityId"
      [required]="true"
      />
    <app-form-field
      label="Client Type"
      type="select"
      [options]="clientTypes"
      [control]="addClientForm.controls.clientTypeId"
      [required]="true"
      /> -->
      <app-form-field
        label="National ID"
        type="text"
        [control]="addClientForm.controls.nationalId"
        [required]="true"
        />
      <app-form-field
        label="Mobile"
        type="text"
        [control]="addClientForm.controls.mobile"
        [required]="true"
        />
      <app-form-field
        label="Passport No"
        type="text"
        [control]="addClientForm.controls.passportNo"
        [required]="true"
        />
      <app-form-field
        label="Address"
        type="text"
        [control]="addClientForm.controls.address"
        [required]="true"
        />
      <app-form-field
        label="Date of Birth"
        type="text"
        [control]="addClientForm.controls.dob"
        [required]="true"
        />
      <app-form-field
        label="Foreign Local"
        type="text"
        [control]="addClientForm.controls.foreignLocal"
        [required]="true"
        />
      <app-form-field
        label="Gender"
        type="text"
        [control]="addClientForm.controls.gender"
        [required]="true"
        />
    </div>

    <div class="flex items-center justify-end gap-3">
      <app-button (click)="toggleModal('addItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- Edit Client Modal -->
<app-modal [isVisible]="modals.editItem" [config]="{ title: 'Edit Client' }">
  <form [formGroup]="editClientForm" (ngSubmit)="handleClientSubmit('update')">
    <div class="grid gap-3 md:grid-cols-2">
      <app-form-field
        label="Title"
        type="text"
        [control]="editClientForm.controls.title!"
        [required]="true"
        />
      <app-form-field
        label="First Name"
        type="text"
        [control]="editClientForm.controls.firstName!"
        [required]="true"
        />
      <app-form-field
        label="Last Name"
        type="text"
        [control]="editClientForm.controls.lastName!"
        [required]="true"
        />
      <app-form-field
        label="Email"
        type="email"
        [control]="editClientForm.controls.email!"
        [required]="true"
        />
      <app-form-field
        label="National ID"
        type="text"
        [control]="editClientForm.controls.nationalId!"
        [required]="true"
        />
      <app-form-field
        label="Mobile"
        type="text"
        [control]="editClientForm.controls.mobile!"
        [required]="true"
        />
      <app-form-field
        label="Passport No"
        type="text"
        [control]="editClientForm.controls.passportNo!"
        [required]="true"
        />
      <app-form-field
        label="Address"
        type="text"
        [control]="editClientForm.controls.address!"
        [required]="true"
        />
      <app-form-field
        label="Date of Birth"
        type="text"
        [control]="editClientForm.controls.dob!"
        [required]="true"
        />
      <app-form-field
        label="Foreign Local"
        type="text"
        [control]="editClientForm.controls.foreignLocal!"
        [required]="true"
        />
      <app-form-field
        label="Gender"
        type="text"
        [control]="editClientForm.controls.gender!"
        [required]="true"
        />
      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
        [control]="editClientForm.get('status')!"
        [required]="true"
        />
    </div>

    <!-- Company details section (conditionally displayed) -->
    @if (isCorporateClient) {
      <div class="mt-4">
        <h3 class="font-semibold text-lg mb-3">Company Details</h3>
        <div class="grid gap-3 md:grid-cols-2">
          <app-form-field
            label="Company Name"
            type="text"
            [control]="editClientForm.controls.companyName!"
            />
          <app-form-field
            label="Company Reg No"
            type="text"
            [control]="editClientForm.controls.companyRegNo!"
            />
          <app-form-field
            label="Company Email"
            type="text"
            [control]="editClientForm.controls.companyEmail!"
            />
          <app-form-field
            label="Company Phone"
            type="text"
            [control]="editClientForm.controls.companyPhone!"
            />
          <app-form-field
            label="Company Address"
            type="text"
            [control]="editClientForm.controls.companyAddress!"
            />
        </div>
      </div>
    }

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="toggleModal('editItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- View Client Modal -->
<app-modal [isVisible]="modals.viewItem" [config]="{ title: 'View Client' }">
  <div class="grid gap-4 md:grid-cols-2">
    <div>
      <label class="form-label">Title</label>
      <input
        type="text"
        [value]="selectedClient?.title"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">First Name</label>
      <input
        type="text"
        [value]="selectedClient?.firstName"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Last Name</label>
      <input
        type="text"
        [value]="selectedClient?.lastName"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Email</label>
      <input
        type="text"
        [value]="selectedClient?.email"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">City</label>
      <input
        type="text"
        [value]="selectedClient?.city?.name"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Client Type</label>
      <input
        type="text"
        [value]="selectedClient?.clientType?.type"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">National ID</label>
      <input
        type="text"
        [value]="selectedClient?.nationalId"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Mobile</label>
      <input
        type="text"
        [value]="selectedClient?.mobile"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Passport No</label>
      <input
        type="text"
        [value]="selectedClient?.passportNo"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Address</label>
      <input
        type="text"
        [value]="selectedClient?.address"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Date of Birth</label>
      <input
        type="text"
        [value]="selectedClient?.dob"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Foreign Local</label>
      <input
        type="text"
        [value]="selectedClient?.foreignLocal"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Gender</label>
      <input
        type="text"
        [value]="selectedClient?.gender"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Status</label>
      <input
        type="text"
        [value]="selectedClient?.status"
        class="form-control"
        disabled
        />
    </div>
  </div>

  <!-- Company details if available and client is corporate -->
  @if (selectedClient?.clientType?.type === 'Corporate') {
    <div class="mt-4">
      <h3 class="font-semibold text-lg mb-3">Company Details</h3>
      <div class="grid gap-4 md:grid-cols-2">
        <div>
          <label class="form-label">Company Name</label>
          <input
            type="text"
            [value]="selectedClient?.companyDetail?.name"
            class="form-control"
            disabled
            />
        </div>
        <div>
          <label class="form-label">Company Reg No</label>
          <input
            type="text"
            [value]="selectedClient?.companyDetail?.regNo"
            class="form-control"
            disabled
            />
        </div>
        <div>
          <label class="form-label">Sector</label>
          <input
            type="text"
            [value]="selectedClient?.companyDetail?.sector?.sector"
            class="form-control"
            disabled
            />
        </div>
        <div>
          <label class="form-label">Company Email</label>
          <input
            type="text"
            [value]="selectedClient?.companyDetail?.email"
            class="form-control"
            disabled
            />
        </div>
        <div>
          <label class="form-label">Company Phone</label>
          <input
            type="text"
            [value]="selectedClient?.companyDetail?.phone"
            class="form-control"
            disabled
            />
        </div>
        <div>
          <label class="form-label">Company Type</label>
          <input
            type="text"
            [value]="selectedClient?.companyDetail?.companyType?.type"
            class="form-control"
            disabled
            />
        </div>
        <div>
          <label class="form-label">Company Address</label>
          <input
            type="text"
            [value]="selectedClient?.companyDetail?.address"
            class="form-control"
            disabled
            />
        </div>
        <div>
          <label class="form-label">Company City</label>
          <input
            type="text"
            [value]="selectedClient?.companyDetail?.city?.name"
            class="form-control"
            disabled
            />
        </div>
      </div>
    </div>
  }

  <div class="flex justify-end mt-4">
    <app-button (click)="toggleModal('viewItem')">Close</app-button>
  </div>
</app-modal>

<p-toast />
