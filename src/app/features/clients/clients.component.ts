import { DatePipe } from "@angular/common";
import { Component, inject, OnInit, signal } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { TableModule } from "primeng/table";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import {
  Client,
  CreateClientRequest,
  UpdateClientRequest,
} from "./clients.model";
import { ClientsService } from "./clients.service";

type ModalKey = "addItem" | "editItem" | "viewItem";

@Component({
  selector: "app-clients",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ModalComponent,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    FormFieldComponent,
    ButtonPermissionDirective,
    DatePipe,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule
],
  templateUrl: "./clients.component.html",
  providers: [DatePipe],
})
export class ClientsComponent implements OnInit {
  private readonly clientsService = inject(ClientsService);
  private readonly fb = inject(FormBuilder);
  private readonly datePipe = inject(DatePipe);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;

  // Signal for clients data
  clients = signal<Client[]>([]);
  filteredClients = signal<Client[]>([]);
  selectedClient: Client | null = null;
  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  isDataLoading = false;

  addClientForm = this.fb.nonNullable.group({
    title: ["", [Validators.required, Validators.minLength(2)]],
    firstName: ["", [Validators.required, Validators.minLength(2)]],
    lastName: ["", [Validators.required, Validators.minLength(2)]],
    email: ["", [Validators.required, Validators.email]],
    password: ["", [Validators.required, Validators.minLength(8)]],
    cityId: ["", [Validators.required]],
    clientTypeId: ["", [Validators.required]],
    nationalId: ["", [Validators.required, Validators.minLength(2)]],
    mobile: ["", [Validators.required, Validators.minLength(10)]],
    passportNo: ["", [Validators.required, Validators.minLength(2)]],
    address: ["", [Validators.required, Validators.minLength(2)]],
    dob: ["", [Validators.required, Validators.minLength(2)]],
    foreignLocal: ["", [Validators.required, Validators.minLength(2)]],
    gender: ["", [Validators.required, Validators.minLength(2)]],
    companyName: ["", [Validators.minLength(2)]],
    companyRegNo: ["", [Validators.minLength(2)]],
    sectorId: [""],
    companyEmail: ["", [Validators.minLength(2)]],
    companyPhone: ["", [Validators.minLength(2)]],
    companyTypeId: ["", [Validators.required]],
    companyAddress: ["", [Validators.minLength(2)]],
    companyCityId: ["", [Validators.required]],
  }) as FormGroup<FormControlType<CreateClientRequest>>;

  editClientForm = this.fb.nonNullable.group({
    id: ["", []],
    title: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    firstName: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    lastName: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    email: [{ value: "", disabled: true }, [Validators.email]],
    password: [{ value: "", disabled: true }, [Validators.minLength(8)]],
    cityId: [{ value: "", disabled: true }, []],
    clientTypeId: [{ value: "", disabled: true }, []],
    nationalId: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    mobile: [{ value: "", disabled: true }, [Validators.minLength(10)]],
    passportNo: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    address: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    dob: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    foreignLocal: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    gender: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    companyName: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    companyRegNo: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    sectorId: [{ value: "", disabled: true }],
    companyEmail: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    companyPhone: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    companyTypeId: [{ value: "", disabled: true }, []],
    companyAddress: [{ value: "", disabled: true }, [Validators.minLength(2)]],
    companyCityId: [{ value: "", disabled: true }, []],
    status: [""], // Only this field remains enabled
  }) as FormGroup<FormControlType<UpdateClientRequest>>;

  get isCorporateClient(): boolean {
    return this.selectedClient?.clientType?.type === "Corporate";
  }

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadClients();
    });
  }

  toggleRow(client: Client): void {
    if (this.expandedRows[client.id]) {
      delete this.expandedRows[client.id];
    } else {
      this.expandedRows[client.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.searchTerm = term;
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.clients();

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (client) =>
          client?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (client) =>
          client.firstName.toLowerCase().includes(term) ||
          client.lastName.toLowerCase().includes(term) ||
          client.email.toLowerCase().includes(term) ||
          client.nationalId.toLowerCase().includes(term) ||
          client.mobile.toLowerCase().includes(term) ||
          client.city.name.toLowerCase().includes(term) ||
          client.clientType.type.toLowerCase().includes(term),
      );
    }

    this.filteredClients.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  loadClients(): void {
    this.isDataLoading = true;
    this.clientsService.getClients().subscribe({
      next: (response) => {
        this.clients.set(response.data);

        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);

        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load clients");
        this.isDataLoading = false;
      },
    });
  }

  toggleModal(mode: ModalKey, client?: Client): void {
    this.selectedClient = client || null;

    if (mode === "editItem" && client) {
      this.editClientForm.patchValue({
        id: client.id,
        title: client.title,
        firstName: client.firstName,
        lastName: client.lastName,
        email: client.email,
        cityId: client.city.id,
        clientTypeId: client.clientType.id,
        nationalId: client.nationalId,
        mobile: client.mobile,
        passportNo: client.passportNo,
        address: client.address,
        dob: client.dob,
        foreignLocal: client.foreignLocal,
        gender: client.gender,
        companyName: client.companyDetail?.name,
        companyRegNo: client.companyDetail?.regNo,
        sectorId: client.companyDetail?.sector?.id,
        companyEmail: client.companyDetail?.email,
        companyPhone: client.companyDetail?.phone,
        companyTypeId: client.companyDetail?.companyType?.id,
        companyAddress: client.companyDetail?.address,
        companyCityId: client.companyDetail?.city?.id,
        status: client.status,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  handleClientSubmit(mode: "add" | "update"): void {
    const form = mode === "add" ? this.addClientForm : this.editClientForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.clientsService.createClient(form.value as CreateClientRequest)
        : this.clientsService.updateClient(form.value as UpdateClientRequest);

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Client has been added successfully`
              : `Client has been updated successfully`,
          type: "success",
        });
        this.loadClients();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(error, `Failed to ${mode} client: ${error.message}`);
      },
    });
  }

  get exportData(): any[] {
    return this.filteredClients().map((client) => ({
      Title: client.title,
      "First Name": client.firstName,
      "Last Name": client.lastName,
      Email: client.email,
      "National ID": client.nationalId,
      Mobile: client.mobile,
      "Passport No": client.passportNo,
      Gender: client.gender,
      "Date of Birth": client.dob,
      Address: client.address,
      City: client.city.name,
      "Client Type": client.clientType.type,
      Status: client.status,
      "Company Name": client.companyDetail?.name || "N/A",
      "Company Reg No": client.companyDetail?.regNo || "N/A",
      "Company Email": client.companyDetail?.email || "N/A",
      "Created Date": this.datePipe.transform(
        client.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": client.modifiedDate
        ? this.datePipe.transform(client.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": client.createdUserName ?? "-",
      "Modified By": client.modifiedUserName ?? "-",
    }));
  }
}
