import { BaseResponse } from "@/app/core/interfaces/common.interface";

export interface PackageExemption extends BaseResponse {
  groupName: string;
  packageName: string;
  productPackageId: string;
  groupId: string;
}

export interface CreatePackageExemptionRequest {
  productPackageId: string;
  groupId: string;
}

export interface UpdatePackageExemptionRequest {
  id: string;
  groupName: string;
  packageName: string;
  status: string;
  productPackageId: string;
  groupId: string;
}
