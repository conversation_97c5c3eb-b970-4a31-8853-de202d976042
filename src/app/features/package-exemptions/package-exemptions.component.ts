import { DatePipe } from "@angular/common";
import { Component, inject, OnInit, signal } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye, faShield } from "@fortawesome/free-solid-svg-icons";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { ModalKey } from "@/app/core/interfaces/common.interface";
import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { onlyLettersAndHyphensValidator } from "@/app/core/validators/custom.validators";
import { ButtonComponent } from "@/app/shared/ui/button/button.component";
import { CardComponent } from "@/app/shared/ui/card/card.component";
import { FormFieldComponent } from "@/app/shared/ui/form-field/form-field.component";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { ModalComponent } from "@/app/shared/ui/modal/modal.component";
import { SpinnerComponent } from "@/app/shared/ui/spinner/spinner.component";
import { TextComponent } from "@/app/shared/ui/text/text.component";

import { ReportExportService } from "@/app/core/services/report-export-service";
import { ClientGroup } from "../client-groups/client-groups.model";
import { Group } from "../group/group.model";
import { GroupService } from "../group/group.service";
import { Package } from "../packages/packages.model";
import { PackagesService } from "../packages/packages.service";
import {
  CreatePackageExemptionRequest,
  PackageExemption,
  UpdatePackageExemptionRequest,
} from "./package-exemptions.model";
import { PackageExemptionsService } from "./package-exemptions.service";

@Component({
  selector: "app-package-exemptions",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    SpinnerComponent,
    DatePipe,
    ModalComponent,
    FormFieldComponent,
    ButtonPermissionDirective,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
  ],
  templateUrl: "./package-exemptions.component.html",
  providers: [DatePipe],
})
export class PackageExemptionsComponent implements OnInit {
  private packageExemptionsService = inject(PackageExemptionsService);
  private groupService = inject(GroupService);
  private packageService = inject(PackagesService);
  private fb = inject(FormBuilder);
  private datePipe = inject(DatePipe);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;
  readonly passwordIcon = faShield;

  packageExemptions: PackageExemption[] = [];
  filteredPackageExemptions = signal<PackageExemption[]>([]);

  groups: Group[] = [];
  clientGroups: ClientGroup[] = [];
  selectedPackageExemptions: PackageExemption | null = null;
  packages: Package[] = [];

  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  isProductPackageExemptionsLoading = false;
  isGroupsLoading = false;
  isDataLoading = false;

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadData();
    });
  }

  loadData(): void {
    this.loadPackageExemptions();
    this.loadGroups();
    this.loadPackages();
  }

  get groupsOptions() {
    return this.groups.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  }

  get packagesOptions() {
    return this.packages.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  }

  // Data for Excel export
  get exportData(): any[] {
    return this.filteredPackageExemptions().map((exemption) => ({
      "Group Name": exemption.groupName,
      "Package Name": exemption.packageName,
      Status: exemption.status,
      "Created Date": this.datePipe.transform(
        exemption.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": exemption.modifiedDate
        ? this.datePipe.transform(exemption.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": exemption.createdUserName ?? "-",
      "Modified By": exemption.modifiedUserName ?? "-",
    }));
  }

  toggleRow(exemption: PackageExemption): void {
    if (this.expandedRows[exemption.id]) {
      delete this.expandedRows[exemption.id];
    } else {
      this.expandedRows[exemption.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.packageExemptions;

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (exemption) =>
          exemption?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (exemption: any) =>
          exemption.groupName.toLowerCase().includes(term) ||
          exemption.packageName.toLowerCase().includes(term) ||
          exemption.status.toLowerCase().includes(term) ||
          (exemption.createdDate &&
            exemption.createdDate.toString().includes(term)),
      );
    }

    this.filteredPackageExemptions.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  loadPackageExemptions() {
    this.isProductPackageExemptionsLoading = true;
    this.packageExemptionsService.getProductPackageExemptions().subscribe({
      next: (response) => {
        this.packageExemptions = response.data;
        this.filteredPackageExemptions.set(this.packageExemptions);
        this.applyFilters(this.searchTerm, this.statusFilter);
        this.isProductPackageExemptionsLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load package exemptions");
        this.isProductPackageExemptionsLoading = false;
      },
    });
  }

  loadPackages(): void {
    this.isDataLoading = true;
    this.packageService.getPackages().subscribe({
      next: (response) => {
        this.packages = response.data;
        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load packages");
        this.isDataLoading = false;
      },
    });
  }

  loadGroups() {
    this.isGroupsLoading = true;
    this.groupService.getGroups().subscribe({
      next: (response) => {
        this.groups = response.data;
        this.isGroupsLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load groups");
        this.isGroupsLoading = false;
      },
    });
  }

  toggleModal(mode: ModalKey, packageExemption?: PackageExemption): void {
    this.selectedPackageExemptions = packageExemption || null;

    if (mode === "editItem" && packageExemption) {
      this.editPackageExemptionForm.patchValue({
        id: packageExemption.id,
        groupName: packageExemption.groupName,
        productPackageId: packageExemption.productPackageId,
        groupId: packageExemption.groupId,
        packageName: packageExemption.packageName,
        status: packageExemption.status,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  addPackageExemptionForm = this.fb.nonNullable.group({
    productPackageId: ["", [Validators.required, Validators.minLength(2)]],
    groupId: ["", [Validators.required, Validators.minLength(2)]],
  }) as unknown as FormGroup<FormControlType<CreatePackageExemptionRequest>>;

  editPackageExemptionForm = this.fb.nonNullable.group({
    id: [""],
    groupId: [""],
    productPackageId: [""],
    groupName: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    packageName: [""],
    status: ["", Validators.required],
  }) as unknown as FormGroup<FormControlType<UpdatePackageExemptionRequest>>;

  handleGroupSubmit(mode: "add" | "update"): void {
    const form =
      mode === "add"
        ? this.addPackageExemptionForm
        : this.editPackageExemptionForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.packageExemptionsService.createProductPackageExemption(
            form.value as CreatePackageExemptionRequest,
          )
        : this.packageExemptionsService.updateProductPackageExemption(
            form.value as UpdatePackageExemptionRequest,
          );

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Package Exemption has been added successfully`
              : `Package Exemption has been updated successfully`,
          type: "success",
        });
        this.loadPackageExemptions();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(error, `Failed to ${mode} package exemption`);
      },
    });
  }
}
