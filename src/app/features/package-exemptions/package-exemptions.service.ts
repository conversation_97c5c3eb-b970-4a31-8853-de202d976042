import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import {
  CreatePackageExemptionRequest,
  UpdatePackageExemptionRequest,
} from "./package-exemptions.model";

@Injectable({
  providedIn: "root",
})
export class PackageExemptionsService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getProductPackageExemptions(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/product-package-exemption`);
  }

  public getActiveProductPackageExemptions(): Observable<any> {
    return this.http.get<any>(
      `${this.apiServerUrl}/product-package-exemption/active`,
    );
  }

  public createProductPackageExemption(
    packageExemption: CreatePackageExemptionRequest,
  ): Observable<any> {
    return this.http.post<any>(
      `${this.apiServerUrl}/product-package-exemption`,
      packageExemption,
    );
  }

  public updateProductPackageExemption(
    packageExemption: UpdatePackageExemptionRequest,
  ): Observable<any> {
    return this.http.put<any>(
      `${this.apiServerUrl}/product-package-exemption`,
      packageExemption,
    );
  }
}
