<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter dropdown -->
      <div class="mr-3 flex items-center">
        <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
        <div class="relative flex items-center">
          <i class="pi pi-filter absolute left-2 text-gray-500"></i>
          <select
            [ngModel]="statusFilter"
            (ngModelChange)="updateStatusFilter($event)"
            class="pl-8 pr-4 py-2 border rounded-lg text-sm"
          >
            <option [value]="null">All Statuses</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
        </div>
      </div>
    </ng-template>

    <ng-template #end>
      <div class="flex items-center gap-2">
        <ng-container *appButtonPermission="'create'">
          <app-button size="sm" (click)="toggleModal('addItem')">
            <i class="text-sm pi pi-plus"></i>
            Create
          </app-button>
        </ng-container>
        <app-button
          size="sm"
          class="!gap-2"
          variant="secondary"
          (click)="
            reportExportService.exportToExcel(
              exportData,
              'package-exemptions.xlsx'
            )
          "
        >
          <i class="text-sm pi pi-upload"></i>
          Export
        </app-button>
      </div>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isProductPackageExemptionsLoading || isGroupsLoading || isDataLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading package exemptions...</span>
      </div>
    </div>
  } @else {
    <!-- Package Exemptions Table -->
    <p-table
      [value]="filteredPackageExemptions()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
    >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold"
            >Product Package Exempted Groups</app-text
          >
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />
            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search exemptions..."
            />
          </p-iconfield>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>
          <th pSortableColumn="createdDate">
            <div class="flex items-center gap-2">
              Created Date
              <p-sortIcon field="createdDate" />
            </div>
          </th>
          <th pSortableColumn="groupName">
            <div class="flex items-center justify-between gap-2">
              Group
              <p-sortIcon field="groupName" />
              <p-columnFilter
                type="text"
                field="groupName"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="packageName">
            <div class="flex items-center justify-between gap-2">
              Package
              <p-sortIcon field="packageName" />
              <p-columnFilter
                type="text"
                field="packageName"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template
        pTemplate="body"
        let-packageExemption
        let-expanded="expanded"
      >
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(packageExemption)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>
            {{ packageExemption.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
          </td>
          <td>{{ packageExemption.groupName }}</td>
          <td>{{ packageExemption.packageName }}</td>
          <td>{{ packageExemption.status }}</td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button
                  size="icon"
                  (click)="toggleModal('viewItem', packageExemption)"
                >
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', packageExemption)"
                >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-packageExemption>
        <tr>
          <td colspan="6">
            <div class="p-4 bg-gray-50 rounded-md">
              <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                <!-- Basic Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                      packageExemption.createdDate | date: "yyyy-MM-dd HH:mm:ss"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Group Name
                  </h5>
                  <p class="text-gray-800">{{ packageExemption.groupName }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Package Name
                  </h5>
                  <p class="text-gray-800">
                    {{ packageExemption.packageName }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">{{ packageExemption.status }}</p>
                </div>
                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                      packageExemption.createdDate | date: "yyyy-MM-dd HH:mm:ss"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                      packageExemption.modifiedDate
                        ? (packageExemption.modifiedDate
                          | date: "yyyy-MM-dd HH:mm:ss")
                        : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ packageExemption.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ packageExemption.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  (click)="toggleModal('editItem', packageExemption)"
                >
                  Edit
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No package exemptions found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- View Package Exemption Modal -->
<app-modal
  [isVisible]="modals.viewItem"
  [config]="{ title: 'View Package Exemption' }"
>
  <div class="flex flex-col gap-3">
    <div>
      <label class="form-label">Created Date</label>
      <input
        type="text"
        [value]="
          selectedPackageExemptions?.createdDate | date: 'yyyy-MM-dd HH:mm:ss'
        "
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Group Name</label>
      <input
        type="text"
        [value]="selectedPackageExemptions?.groupName"
        class="form-control"
        disabled
      />
    </div>
    <div>
      <label class="form-label">Package Name</label>
      <input
        type="text"
        [value]="selectedPackageExemptions?.packageName"
        class="form-control"
        disabled
      />
    </div>

    <div>
      <label class="form-label">Status</label>
      <input
        type="text"
        [value]="selectedPackageExemptions?.status"
        class="form-control"
        disabled
      />
    </div>

    <div class="flex justify-end">
      <app-button (click)="toggleModal('viewItem')">Close</app-button>
    </div>
  </div>
</app-modal>

<!-- Add Package Exemption Modal -->
<app-modal
  [isVisible]="modals.addItem"
  [config]="{ title: 'Exempt Group From Package' }"
>
  <form
    [formGroup]="addPackageExemptionForm"
    (ngSubmit)="handleGroupSubmit('add')"
    class="flex flex-col gap-6"
  >
    <div class="grid gap-3 md:grid-cols-2">
      <app-form-field
        label="Group"
        placeholder="Select Group"
        type="select"
        [options]="groupsOptions"
        [control]="addPackageExemptionForm.controls.groupId"
        [required]="true"
      />
      <app-form-field
        label="Package"
        placeholder="Select Package"
        type="select"
        [options]="packagesOptions"
        [control]="addPackageExemptionForm.controls.productPackageId"
        [required]="true"
      />
    </div>

    <div class="flex items-center justify-end gap-3">
      <app-button (click)="toggleModal('addItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- Edit Package Exemption Modal -->
<app-modal
  [isVisible]="modals.editItem"
  [config]="{ title: 'Edit Package Exemption' }"
>
  <form
    [formGroup]="editPackageExemptionForm"
    (ngSubmit)="handleGroupSubmit('update')"
  >
    <div class="flex flex-col gap-3">
      <div>
        <label class="form-label">Created Date</label>
        <input
          type="text"
          [value]="
            selectedPackageExemptions?.createdDate | date: 'yyyy-MM-dd HH:mm:ss'
          "
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Group Name</label>
        <input
          type="text"
          [value]="selectedPackageExemptions?.groupName"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Package Name</label>
        <input
          type="text"
          [value]="selectedPackageExemptions?.packageName"
          class="form-control"
          disabled
        />
      </div>
      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
        [control]="editPackageExemptionForm.get('status')!"
        [required]="true"
      />
    </div>

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="toggleModal('editItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<p-toast />
