import { Exchange } from "@/app/core/enums/exchange.enum";
import { Status } from "@/app/core/enums/status.enum";
import { BaseResponse } from "@/app/core/interfaces/common.interface";

export interface Product extends BaseResponse {
  name: string;
  description: string;
  linkTable: string;
  exchangeId: number;
  exchangeName: Exchange;
  numberOfUsers: number;
}

export interface CreateProductRequest {
  name: string;
  description: string;
  linkTable: string;
  exchangeName: Exchange;
  numberOfUsers: number;
}

export interface UpdateProductRequest {
  id: string;
  name?: string;
  description?: string;
  linkTable?: string;
  exchangeName?: Exchange;
  status: Status;
  numberOfUsers: number;
}
