import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { CreateProductRequest, UpdateProductRequest } from "./products.model";

@Injectable({
  providedIn: "root",
})
export class ProductsService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getProducts(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/products`);
  }

  public getProductTables(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/products/tables`);
  }

  public createProduct(productData: CreateProductRequest): Observable<any> {
    return this.http.post<any>(`${this.apiServerUrl}/products`, productData);
  }

  public updateProduct(productData: UpdateProductRequest): Observable<any> {
    return this.http.put<any>(`${this.apiServerUrl}/products`, productData);
  }
}
