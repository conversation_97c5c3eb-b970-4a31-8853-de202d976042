import { DatePipe } from "@angular/common";
import { Component, inject, OnInit } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { Option } from "@/app/core/utils/map-to-options.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import {
  CreateProductRequest,
  Product,
  UpdateProductRequest,
} from "./products.model";
import { ProductsService } from "./products.service";

type ModalKey = "addItem" | "editItem" | "viewItem";

@Component({
  selector: "app-products",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ModalComponent,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    FormFieldComponent,
    ButtonPermissionDirective,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    DatePipe,
  ],
  templateUrl: "./products.component.html",
  providers: [DatePipe],
})
export class ProductsComponent implements OnInit {
  private productsService = inject(ProductsService);
  private fb = inject(FormBuilder);
  private readonly datePipe = inject(DatePipe);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;
  showNumberOfUsers = false;

  products: Product[] = [];
  filteredProducts: Product[] = [];
  productTables: any[] = [];
  selectedProduct: Product | null = null;
  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  get exportData(): any[] {
    return this.filteredProducts.map((product) => ({
      "Product Name": product.name,
      Description: product.description,
      "Link Table": product.linkTable,
      Exchange: product.exchangeName,
      "Number of Users": product.numberOfUsers,
      Status: product.status,
      "Created Date": this.datePipe.transform(
        product.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": product.modifiedDate
        ? this.datePipe.transform(product.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": product.createdUserName ?? "-",
      "Modified By": product.modifiedUserName ?? "-",
    }));
  }

  get productTableOptions(): Option[] {
    return this.productTables.map((table) => ({
      value: table,
      label: table,
    }));
  }

  get exchangeOptions(): Option[] {
    return [
      { value: Exchange.VFEX, label: Exchange.VFEX },
      { value: Exchange.ZSE, label: Exchange.ZSE },
    ];
  }

  addProductForm = this.fb.nonNullable.group({
    name: [
      "",
      [Validators.required, Validators.minLength(2), Validators.maxLength(50)],
    ],
    description: ["", [Validators.minLength(2), Validators.maxLength(100)]],
    linkTable: ["", [Validators.required, Validators.minLength(2)]],
    exchangeName: [
      Exchange.VFEX,
      [Validators.required, Validators.minLength(2), Validators.maxLength(50)],
    ],
    numberOfUsers: [1, [Validators.min(1)]],
  }) as FormGroup<FormControlType<CreateProductRequest>>;

  editProductForm = this.fb.nonNullable.group({
    id: [""],
    name: [
      "",
      [Validators.required, Validators.minLength(2), Validators.maxLength(50)],
    ],
    description: ["", [Validators.minLength(2), Validators.maxLength(100)]],
    linkTable: ["", [Validators.required, Validators.minLength(2)]],
    exchangeName: [
      Exchange.VFEX,
      [Validators.required, Validators.minLength(2), Validators.maxLength(50)],
    ],
    status: ["", Validators.required],
    numberOfUsers: [0, [Validators.required]],
  }) as FormGroup<FormControlType<UpdateProductRequest>>;

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  isDataLoading = false;
  isProductTablesLoading = false;

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadData();
    });
    this.getProductTables();
  }

  loadData(): void {
    this.isDataLoading = true;
    this.productsService.getProducts().subscribe({
      next: (response) => {
        this.products = response.data;
        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);
        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load products");
        this.isDataLoading = false;
      },
    });
  }

  toggleRow(product: Product): void {
    if (this.expandedRows[product.id]) {
      delete this.expandedRows[product.id];
    } else {
      this.expandedRows[product.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.products;

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (product) =>
          product?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (product: any) =>
          product.name.toLowerCase().includes(term) ||
          product.description?.toLowerCase()?.includes(term) ||
          "" ||
          product.linkTable.toLowerCase().includes(term) ||
          product.exchangeName.toLowerCase().includes(term) ||
          product.status.toLowerCase().includes(term) ||
          String(product.numberOfUsers).includes(term),
      );
    }

    this.filteredProducts = filtered;
  }

  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  toggleModal(mode: ModalKey, product?: Product): void {
    this.selectedProduct = product || null;

    if (mode === "editItem" && product) {
      this.editProductForm.patchValue({
        id: product.id,
        name: product.name,
        description: product.description,
        linkTable: product.linkTable,
        exchangeName: product.exchangeName,
        numberOfUsers: product.numberOfUsers,
        status: product.status,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  handleProductSubmit(mode: "add" | "update"): void {
    const form = mode === "add" ? this.addProductForm : this.editProductForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.productsService.createProduct(form.value as CreateProductRequest)
        : this.productsService.updateProduct(
            form.value as UpdateProductRequest,
          );

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Product has been added successfully`
              : `Product has been updated successfully`,
          type: "success",
        });
        this.loadData();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(error, `Failed to ${mode} product: ${error.message}`);
      },
    });
  }

  getProductTables() {
    this.isProductTablesLoading = true;
    this.productsService.getProductTables().subscribe({
      next: (response) => {
        this.productTables = response.data;
        this.isProductTablesLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load product tables");
        this.isProductTablesLoading = false;
      },
    });
  }
}
