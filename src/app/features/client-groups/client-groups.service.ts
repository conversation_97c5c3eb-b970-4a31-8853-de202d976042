import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { CreateGroupRequest, UpdateGroupRequest } from "../group/group.model";

@Injectable({
  providedIn: "root",
})
export class ClientGroupService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getClientGroups(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/client-groups`);
  }

  public getActiveGroups(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/groups/active`);
  }

  public createClientGroup(clientGroup: CreateGroupRequest): Observable<any> {
    return this.http.post<any>(
      `${this.apiServerUrl}/client-groups`,
      clientGroup,
    );
  }

  public updateClientGroup(clientGroup: UpdateGroupRequest): Observable<any> {
    return this.http.put<any>(
      `${this.apiServerUrl}/client-groups`,
      clientGroup,
    );
  }

  public checkClient(clientId: string): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/client-groups/client-id`, {
      params: { clientId },
    });
  }
}
