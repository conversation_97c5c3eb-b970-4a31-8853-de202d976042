import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/app/core/interfaces/common.interface";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { ButtonComponent } from "@/app/shared/ui/button/button.component";
import { CardComponent } from "@/app/shared/ui/card/card.component";
import { FormFieldComponent } from "@/app/shared/ui/form-field/form-field.component";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { ModalComponent } from "@/app/shared/ui/modal/modal.component";
import { TextComponent } from "@/app/shared/ui/text/text.component";
import { CommonModule, DatePipe } from "@angular/common";
import { Component, inject, OnInit, signal } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye, faShield } from "@fortawesome/free-solid-svg-icons";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { DropdownModule } from "primeng/dropdown";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import Swal from "sweetalert2";
import { Client } from "../clients/clients.model";
import { ClientsService } from "../clients/clients.service";
import {
  CreateGroupRequest,
  Group,
  UpdateGroupRequest,
} from "../group/group.model";
import { GroupService } from "../group/group.service";
import {
  ClientGroup,
  CreateClientGroupRequest,
  CreateClientGroupResponse,
  UpdateClientGroupRequest,
} from "./client-groups.model";
import { ClientGroupService } from "./client-groups.service";

@Component({
  selector: "app-client-groups",
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    DatePipe,
    ModalComponent,
    FormFieldComponent,
    ButtonPermissionDirective,
    DropdownModule,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
  ],
  templateUrl: "./client-groups.component.html",
  providers: [DatePipe],
})
export class ClientGroupsComponent implements OnInit {
  private clientGroupService = inject(ClientGroupService);
  private groupService = inject(GroupService);
  private clientsService = inject(ClientsService);
  private fb = inject(FormBuilder);
  private datePipe = inject(DatePipe);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;
  readonly passwordIcon = faShield;

  filteredClientGroups = signal<ClientGroup[]>([]);
  clientGroups = signal<ClientGroup[]>([]);

  groups: Group[] = [];
  selectedGroup: Group | null = null;
  selectedClientGroup: ClientGroup | null = null;
  clients: Client[] = [];

  isClientGroupsLoading = false;
  isGroupsLoading = false;
  isDataLoading = false;

  searchTerm = "";
  statusFilter: string | null = null;
  expandedRows: { [key: string]: boolean } = {};

  // For Excel export
  get exportData(): any[] {
    return this.filteredClientGroups().map((group) => ({
      "Group Name": group.group.name,
      "Client First Name": group.client.firstName,
      "Client Last Name": group.client.lastName,
      Email: group.client.email,
      Status: group.status,
      "Created Date": this.datePipe.transform(
        group.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": group.modifiedDate
        ? this.datePipe.transform(group.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": group.createdUserName ?? "-",
      "Modified By": group.modifiedUserName ?? "-",
    }));
  }

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadClientGroups();
      this.loadGroups();
      this.loadClients();
    });
  }

  toggleRow(clientGroup: ClientGroup): void {
    if (this.expandedRows[clientGroup.id]) {
      delete this.expandedRows[clientGroup.id];
    } else {
      this.expandedRows[clientGroup.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.clientGroups();

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (group) => group?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (group: any) =>
          group.groupName.toLowerCase().includes(term) ||
          group.firstName.toLowerCase().includes(term) ||
          group.lastName.toLowerCase().includes(term) ||
          group.email.toLowerCase().includes(term) ||
          group.status.toLowerCase().includes(term),
      );
    }

    this.filteredClientGroups.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  get groupsOptions() {
    return this.groups.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  }

  get clientOptions() {
    return this.clients.map((item) => ({
      label: `${item.firstName} ${item.lastName} (${item.email})`,
      value: item.id,
    }));
  }

  loadClientGroups() {
    this.isClientGroupsLoading = true;
    this.clientGroupService.getClientGroups().subscribe({
      next: (response) => {
        this.clientGroups.set(response.data);
        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);
        this.isClientGroupsLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load client groups");
        this.isClientGroupsLoading = false;
      },
    });
  }

  loadClients(): void {
    this.isDataLoading = true;
    this.clientsService.getClients().subscribe({
      next: (response) => {
        this.clients = response.data;
        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load clients");
        this.isDataLoading = false;
      },
    });
  }

  loadGroups() {
    this.isGroupsLoading = true;
    this.groupService.getGroups().subscribe({
      next: (response) => {
        this.groups = response.data;
        this.isGroupsLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load client groups");
        this.isGroupsLoading = false;
      },
    });
  }

  toggleModal(mode: ModalKey, clientGroup?: ClientGroup): void {
    this.selectedClientGroup = clientGroup || null;

    if (mode === "editItem" && clientGroup) {
      this.editClientGroupForm.patchValue({
        id: clientGroup.id,
        groupName: clientGroup.group.name,
        firstName: clientGroup.client.firstName,
        lastName: clientGroup.client.lastName,
        email: clientGroup.client.email,
        groupId: clientGroup.group.id,
        clientId: clientGroup.client.id,
        status: clientGroup.status,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  addClientGroupForm = this.fb.nonNullable.group({
    clientId: ["", [Validators.required, Validators.minLength(2)]],
    groupId: ["", [Validators.required, Validators.minLength(2)]],
  }) as unknown as FormGroup<FormControlType<CreateClientGroupRequest>>;

  editClientGroupForm = this.fb.nonNullable.group({
    id: [""],
    groupId: [""],
    clientId: [""],
    groupName: [""],
    firstName: [""],
    lastName: [""],
    email: [""],
    status: ["", Validators.required],
  }) as unknown as FormGroup<FormControlType<UpdateClientGroupRequest>>;

  checkClient() {
    const clientId = this.addClientGroupForm.get("clientId")?.value;

    if (!clientId) {
      showToast({
        message: `Client ID is required`,
        type: "warning",
      });
      return;
    }

    this.clientGroupService.checkClient(clientId).subscribe({
      next: (response) => {
        const clientGroups: CreateClientGroupResponse[] = response.data;

        if (clientGroups.length > 0) {
          const clientName = `${clientGroups[0]?.firstName} ${clientGroups[0]?.lastName}`;
          const groupNames = clientGroups
            .map((group: CreateClientGroupResponse) => group.groupName)
            .join(", ");

          Swal.fire({
            title: "Client already in group(s)",
            text: `The client "${clientName}" is already in the groups: ${groupNames}. Do you want to proceed?`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonText: "Proceed",
            cancelButtonText: "Cancel",
          }).then((result) => {
            if (result.isConfirmed) {
              this.handleGroupSubmit("add");
            } else {
              showToast({
                message: `Action cancelled`,
                type: "info",
              });
            }
          });
        } else {
          this.handleGroupSubmit("add");
        }
      },
      error: (error) => {
        handleError(error, `Failed to check client`);
      },
    });
  }

  handleGroupSubmit(mode: "add" | "update"): void {
    const form =
      mode === "add" ? this.addClientGroupForm : this.editClientGroupForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.clientGroupService.createClientGroup(
            form.value as CreateGroupRequest,
          )
        : this.clientGroupService.updateClientGroup(
            form.value as UpdateGroupRequest,
          );

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Client Group has been added successfully`
              : `Client Group has been updated successfully`,
          type: "success",
        });
        this.loadClientGroups();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(error, `Failed to ${mode} client group`);
      },
    });
  }
}
