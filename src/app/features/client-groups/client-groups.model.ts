import { BaseResponse } from "@/app/core/interfaces/common.interface";

export interface ClientGroup extends BaseResponse {
  group: Group;
  client: Client;
}

export interface CreateClientGroupResponse extends BaseResponse {
  firstName: string;
  lastName: string;
  email: string;
  groupId: string;
  groupName: string;
}

interface Group {
  id: string;
  name: string;
}

interface Client {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export interface CreateClientGroupRequest {
  clientId: string;
  groupId: string;
}

export interface UpdateClientGroupRequest {
  id: string;
  clientId: string;
  groupId: string;
  groupName: string;
  firstName: string;
  lastName: string;
  email: string;
  status: string;
}
