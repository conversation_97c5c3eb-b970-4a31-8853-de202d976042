<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter dropdown -->
      <div class="mr-3 flex items-center">
        <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
        <div class="relative flex items-center">
          <i class="pi pi-filter absolute left-2 text-gray-500"></i>
          <select
            [ngModel]="statusFilter"
            (ngModelChange)="updateStatusFilter($event)"
            class="pl-8 pr-4 py-2 border rounded-lg text-sm"
          >
            <option [value]="null">All Statuses</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
        </div>
      </div>
    </ng-template>

    <ng-template #end>
      <app-button
        size="sm"
        class="!gap-2"
        variant="secondary"
        (click)="
          reportExportService.exportToExcel(exportData, 'client-groups.xlsx')
        "
      >
        <i class="text-sm pi pi-upload"></i>
        Export
      </app-button>
      <ng-container *appButtonPermission="'create'">
        <app-button size="sm" class="ml-2" (click)="toggleModal('addItem')">
          <i class="text-sm pi pi-plus mr-2"></i>
          Create
        </app-button>
      </ng-container>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isDataLoading || isClientGroupsLoading || isGroupsLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading client groups...</span>
      </div>
    </div>
  } @else {
    <!-- Client Groups Table -->
    <p-table
      [value]="filteredClientGroups()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
    >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold">Manage Client Groups</app-text>
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />

            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search client groups..."
            />
          </p-iconfield>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>

          <th pSortableColumn="createdDate">
            <div class="flex items-center gap-2">
              Created Date
              <p-sortIcon field="createdDate" />
            </div>
          </th>

          <th pSortableColumn="groupName">
            <div class="flex items-center justify-between gap-2">
              Group
              <p-sortIcon field="groupName" />
              <p-columnFilter
                type="text"
                field="groupName"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="firstName">
            <div class="flex items-center justify-between gap-2">
              Client Name
              <p-sortIcon field="firstName" />
              <p-columnFilter
                type="text"
                field="firstName"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="lastName">
            <div class="flex items-center justify-between gap-2">
              Client Surname
              <p-sortIcon field="lastName" />
              <p-columnFilter
                type="text"
                field="lastName"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="email">
            <div class="flex items-center justify-between gap-2">
              Email
              <p-sortIcon field="email" />
              <p-columnFilter
                type="text"
                field="email"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-clientGroup let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(clientGroup)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ clientGroup.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ clientGroup.group.name }}</td>
          <td>{{ clientGroup.client.firstName }}</td>
          <td>{{ clientGroup.client.lastName }}</td>
          <td>{{ clientGroup.client.email }}</td>
          <td>
            <span
              [ngClass]="{
                'bg-green-100 text-green-800': clientGroup.status === 'ACTIVE',
                'bg-red-100 text-red-800': clientGroup.status === 'INACTIVE',
              }"
              class="px-2 py-1 text-xs font-semibold rounded-full"
            >
              {{ clientGroup.status }}
            </span>
          </td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button
                  size="icon"
                  (click)="toggleModal('viewItem', clientGroup)"
                >
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', clientGroup)"
                >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-clientGroup>
        <tr>
          <td colspan="8">
            <div class="p-4 bg-gray-50 rounded-md">
              <div
                class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
              >
                <!-- Group Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Group Name
                  </h5>
                  <p class="text-gray-800">{{ clientGroup.group.name }}</p>
                </div>

                <!-- Client Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Client Name
                  </h5>
                  <p class="text-gray-800">
                    {{ clientGroup.firstName }}
                    {{ clientGroup.client.lastName }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Client Email
                  </h5>
                  <p class="text-gray-800">{{ clientGroup.client.email }}</p>
                </div>

                <!-- Status Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">{{ clientGroup.status }}</p>
                </div>

                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ clientGroup.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                      clientGroup.modifiedDate
                        ? (clientGroup.modifiedDate
                          | date: "yyyy-MM-dd HH:mm:ss")
                        : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ clientGroup.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ clientGroup.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  (click)="toggleModal('editItem', clientGroup)"
                >
                  Edit
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No client groups found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- Add Client Group Modal -->
<app-modal
  [isVisible]="modals.addItem"
  [config]="{ title: 'Add Client to a Group' }"
>
  <form
    [formGroup]="addClientGroupForm"
    (ngSubmit)="checkClient()"
    class="flex flex-col gap-6"
  >
    <div class="grid gap-3 md:grid-cols-2">
      <app-form-field
        label="Group"
        placeholder="Select Group"
        type="select"
        [options]="groupsOptions"
        [control]="addClientGroupForm.controls.groupId"
        [required]="true"
      />
      <app-form-field
        label="Client"
        placeholder="Select Client"
        type="select"
        [options]="clientOptions"
        [control]="addClientGroupForm.controls.clientId"
        [required]="true"
      />
    </div>

    <div class="flex items-center justify-end gap-3">
      <app-button (click)="toggleModal('addItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- View Client Group Modal -->
<app-modal
  [isVisible]="modals.viewItem"
  [config]="{ title: 'View Client Group' }"
>
  <div class="flex flex-col gap-3">
    <div class="flex flex-col gap-3">
      <div>
        <label class="form-label">Created Date</label>
        <input
          type="text"
          [value]="
            selectedClientGroup?.createdDate | date: 'yyyy-MM-dd HH:mm:ss'
          "
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Group Name</label>
        <input
          type="text"
          [value]="selectedClientGroup?.group?.name"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Client Name</label>
        <input
          type="text"
          [value]="selectedClientGroup?.client?.firstName"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Client Surname</label>
        <input
          type="text"
          [value]="selectedClientGroup?.client?.lastName"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Client Email</label>
        <input
          type="text"
          [value]="selectedClientGroup?.client?.email"
          class="form-control"
          disabled
        />
      </div>

      <div>
        <label class="form-label">Status</label>
        <input
          type="text"
          [value]="selectedClientGroup?.status"
          class="form-control"
          disabled
        />
      </div>

      <div class="flex justify-end">
        <app-button (click)="toggleModal('viewItem')">Close</app-button>
      </div>
    </div>
  </div>
</app-modal>

<!-- Edit Client Group Modal -->
<app-modal
  [isVisible]="modals.editItem"
  [config]="{ title: 'Edit Client Group' }"
>
  <form
    [formGroup]="editClientGroupForm"
    (ngSubmit)="handleGroupSubmit('update')"
  >
    <div class="flex flex-col gap-3">
      <div>
        <label class="form-label">Created Date</label>
        <input
          type="text"
          [value]="
            selectedClientGroup?.createdDate | date: 'yyyy-MM-dd HH:mm:ss'
          "
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Group Name</label>
        <input
          type="text"
          [value]="selectedClientGroup?.group?.name"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Client Name</label>
        <input
          type="text"
          [value]="selectedClientGroup?.client?.firstName"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Client Surname</label>
        <input
          type="text"
          [value]="selectedClientGroup?.client?.lastName"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Client Email</label>
        <input
          type="text"
          [value]="selectedClientGroup?.client?.email"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <app-form-field
          label="Status"
          type="select"
          [options]="[
            { value: 'ACTIVE', label: 'Active' },
            { value: 'INACTIVE', label: 'Inactive' },
          ]"
          [control]="editClientGroupForm.get('status')!"
          [required]="true"
        />
      </div>
    </div>

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="toggleModal('editItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<p-toast />
