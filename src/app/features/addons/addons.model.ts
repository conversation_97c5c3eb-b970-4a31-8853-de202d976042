import { Exchange } from "@/app/core/enums/exchange.enum";
import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { Status } from "@/app/core/enums/status.enum";
import { BaseResponse } from "@/app/core/interfaces/common.interface";

export interface Addon extends BaseResponse {
  name: string;
  description: string;
  linkTable: string;
  exchangeId: number;
  exchangeName: Exchange;
  price: number;
  currencyId: string;
  currencyCode: string;
  categoryType: ProductCategoryType;
}

export interface CreateAddonRequest {
  name: string;
  description: string;
  linkTable: string;
  exchangeName: Exchange;
  price: number;
  currencyId: string;
  categoryType: ProductCategoryType;
}

export interface UpdateAddonRequest {
  id: string;
  name?: string;
  description?: string;
  linkTable?: string;
  categoryType?: ProductCategoryType;
  exchangeName?: Exchange;
  status: Status;
  price?: number;
  currencyId?: string;
}
