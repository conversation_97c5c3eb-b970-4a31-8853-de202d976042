import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { Option } from "@/app/core/utils/map-to-options.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { CurrencyPipe, DatePipe } from "@angular/common";
import { Component, inject, OnInit, signal } from "@angular/core";
import {
  Form<PERSON><PERSON>er,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import { CurrenciesService } from "../currency/currencies.service";
import { Currency } from "../currency/currency.model";
import { Addon, CreateAddonRequest, UpdateAddonRequest } from "./addons.model";
import { AddonsService } from "./addons.service";

type ModalKey = "addItem" | "editItem" | "viewItem";

@Component({
  selector: "app-addons",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ModalComponent,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    FormFieldComponent,
    ButtonPermissionDirective,
    DatePipe,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    CurrencyPipe,
  ],
  templateUrl: "./addons.component.html",
  providers: [DatePipe],
})
export class AddonsComponent implements OnInit {
  private addonsService = inject(AddonsService);
  private currenciesService = inject(CurrenciesService);
  private fb = inject(FormBuilder);
  private readonly datePipe = inject(DatePipe);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;

  filteredAddons = signal<Addon[]>([]);
  addons = signal<Addon[]>([]);
  addonTables: any[] = [];
  selectedAddon: Addon | null = null;
  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  currencies: Currency[] = [];

  get exportData(): any[] {
    return this.filteredAddons().map((addon) => ({
      "Addon Name": addon.name,
      Description: addon.description || "",
      "Link Table": addon.linkTable?.replaceAll("_", " ") || "",
      "Category Type": addon.categoryType,
      Exchange: addon.exchangeName,
      Price: addon.price,
      Currency: addon.currencyCode,
      Status: addon.status,
      "Created Date": this.datePipe.transform(
        addon.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": addon.modifiedDate
        ? this.datePipe.transform(addon.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": addon.createdUserName ?? "-",
      "Modified By": addon.modifiedUserName ?? "-",
    }));
  }

  get addonTableOptions(): Option[] {
    return this.addonTables.map((table) => ({
      value: table,
      label: table,
    }));
  }

  get currencyOptions(): Option[] {
    return this.currencies.map((currency) => ({
      value: currency.id,
      label: currency.name,
    }));
  }

  get exchangeOptions(): Option[] {
    return [
      { value: Exchange.VFEX, label: Exchange.VFEX },
      { value: Exchange.ZSE, label: Exchange.ZSE },
    ];
  }

  get categoryTypeOptions(): Option[] {
    return [
      { value: ProductCategoryType.LIVE, label: ProductCategoryType.LIVE },
      {
        value: ProductCategoryType.HISTORICAL,
        label: ProductCategoryType.HISTORICAL,
      },
      { value: ProductCategoryType.VOT, label: ProductCategoryType.VOT },
    ];
  }

  addAddonForm = this.fb.nonNullable.group({
    name: [
      "",
      [Validators.required, Validators.minLength(2), Validators.maxLength(50)],
    ],
    description: ["", [Validators.minLength(2), Validators.maxLength(100)]],
    linkTable: ["", [Validators.required, Validators.minLength(2)]],
    categoryType: ["", [Validators.required]],
    exchangeName: [
      Exchange.VFEX,
      [Validators.required, Validators.minLength(2), Validators.maxLength(50)],
    ],
    price: [0, [Validators.required, Validators.min(0)]],
    currencyId: ["", [Validators.required]],
  }) as FormGroup<FormControlType<CreateAddonRequest>>;

  editAddonForm = this.fb.nonNullable.group({
    id: [""],
    name: ["", [Validators.minLength(2), Validators.maxLength(50)]],
    description: ["", [Validators.minLength(2), Validators.maxLength(100)]],
    linkTable: ["", [Validators.minLength(2)]],
    categoryType: [""],
    exchangeName: [
      Exchange.VFEX,
      [Validators.minLength(2), Validators.maxLength(50)],
    ],
    status: [""],
    currencyId: [""],
    price: [0, [Validators.min(0)]],
  }) as FormGroup<FormControlType<UpdateAddonRequest>>;

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  isDataLoading = false;
  isAddonTablesLoading = false;
  isCurrenciesLoading = false;

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadData();
    });
    this.getAddonTables();
    this.getCurrencies();
  }

  loadData(): void {
    this.isDataLoading = true;
    this.addonsService.getAddons().subscribe({
      next: (response) => {
        this.addons.set(response.data);

        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);

        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load addons");
        this.isDataLoading = false;
      },
    });
  }

  toggleRow(addon: Addon): void {
    if (this.expandedRows[addon.id]) {
      delete this.expandedRows[addon.id];
    } else {
      this.expandedRows[addon.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.addons();

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (addon) => addon?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (addon) =>
          addon.name.toLowerCase().includes(term) ||
          (addon.description &&
            addon.description.toLowerCase().includes(term)) ||
          (addon.linkTable && addon.linkTable.toLowerCase().includes(term)) ||
          addon.exchangeName.toLowerCase().includes(term) ||
          addon.currencyCode.toLowerCase().includes(term) ||
          addon.price.toString().includes(term),
      );
    }

    this.filteredAddons.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  toggleModal(mode: ModalKey, addon?: Addon): void {
    this.selectedAddon = addon || null;

    if (mode === "editItem" && addon) {
      this.editAddonForm.patchValue({
        id: addon.id,
        name: addon.name,
        description: addon.description,
        linkTable: addon.linkTable,
        exchangeName: addon.exchangeName,
        currencyId: addon.currencyId,
        price: addon.price,
        status: addon.status,
        categoryType: addon.categoryType,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  handleAddonSubmit(mode: "add" | "update"): void {
    const form = mode === "add" ? this.addAddonForm : this.editAddonForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.addonsService.createAddon(form.value as CreateAddonRequest)
        : this.addonsService.updateAddon(form.value as UpdateAddonRequest);

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Addon has been added successfully`
              : `Addon has been updated successfully`,
          type: "success",
        });
        this.loadData();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(error, `Failed to ${mode} addon: ${error.message}`);
      },
    });
  }

  getAddonTables() {
    this.isAddonTablesLoading = true;
    this.addonsService.getAddonTables().subscribe({
      next: (response) => {
        this.addonTables = response.data;
        this.isAddonTablesLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load addon tables");
        this.isAddonTablesLoading = false;
      },
    });
  }

  getCurrencies() {
    this.isCurrenciesLoading = true;
    this.currenciesService.getCurrencies().subscribe({
      next: (response) => {
        this.currencies = response.data;
        this.isCurrenciesLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load currencies");
        this.isCurrenciesLoading = false;
      },
    });
  }
}
