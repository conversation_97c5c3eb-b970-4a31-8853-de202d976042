import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { map, Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { CreateAddonRequest, UpdateAddonRequest } from "./addons.model";

@Injectable({
  providedIn: "root",
})
export class AddonsService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getAddons(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/addons`).pipe(
      map((response) => {
        const historicalProducts = response.data.filter(
          (item: any) => item.categoryType !== ProductCategoryType.HISTORICAL,
        );
        return { ...response, data: historicalProducts };
      }),
    );
  }

  public getAddonTables(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/products/tables`);
  }

  public createAddon(addonData: CreateAddonRequest): Observable<any> {
    return this.http.post<any>(`${this.apiServerUrl}/addons`, addonData);
  }

  public updateAddon(addonData: UpdateAddonRequest): Observable<any> {
    return this.http.put<any>(`${this.apiServerUrl}/addons`, addonData);
  }
}
