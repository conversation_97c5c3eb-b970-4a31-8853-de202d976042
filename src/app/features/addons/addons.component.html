<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter dropdown -->
      <div class="mr-3 flex items-center">
        <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
        <div class="relative flex items-center">
          <i class="pi pi-filter absolute left-2 text-gray-500"></i>
          <select
            [ngModel]="statusFilter"
            (ngModelChange)="updateStatusFilter($event)"
            class="pl-8 pr-4 py-2 border rounded-lg text-sm"
            >
            <option [value]="null">All Statuses</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
        </div>
      </div>
    </ng-template>

    <ng-template #end>
      <div class="flex items-center gap-2">
        <app-button
          size="sm"
          class="!gap-2"
          variant="secondary"
          (click)="reportExportService.exportToExcel(exportData, 'addons.xlsx')"
          >
          <i class="text-sm pi pi-upload"></i>
          Export
        </app-button>
        <ng-container *appButtonPermission="'create'">
          <app-button size="sm" (click)="toggleModal('addItem')">
            <i class="text-sm pi pi-plus"></i>
            Create
          </app-button>
        </ng-container>
      </div>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isDataLoading || isAddonTablesLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading addons...</span>
      </div>
    </div>
  } @else {
    <!-- Addons Table -->
    <p-table
      [value]="filteredAddons()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
      >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold">Manage Addons</app-text>
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />
            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search addons..."
              />
          </p-iconfield>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>
          <th pSortableColumn="createdDate">
            <div class="flex items-center gap-2">
              Created Date
              <p-sortIcon field="createdDate" />
            </div>
          </th>
          <th pSortableColumn="name">
            <div class="flex items-center justify-between gap-2">
              Name
              <p-sortIcon field="name" />
              <p-columnFilter
                type="text"
                field="name"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>
          <th pSortableColumn="linkTable">
            <div class="flex items-center justify-between gap-2">
              Link Table
              <p-sortIcon field="linkTable" />
              <p-columnFilter
                type="text"
                field="linkTable"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>
          <th pSortableColumn="categoryType">
            <div class="flex items-center justify-between gap-2">
              Category Type
              <p-sortIcon field="categoryType" />
              <p-columnFilter
                type="text"
                field="categoryType"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>
          <th pSortableColumn="exchangeName">
            <div class="flex items-center justify-between gap-2">
              Exchange
              <p-sortIcon field="exchangeName" />
              <p-columnFilter
                type="text"
                field="exchangeName"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>
          <th pSortableColumn="price">
            <div class="flex items-center justify-between gap-2">
              Price
              <p-sortIcon field="price" />
              <p-columnFilter
                type="numeric"
                field="price"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>
          <th pSortableColumn="currencyCode">
            <div class="flex items-center justify-between gap-2">
              Currency
              <p-sortIcon field="currencyCode" />
              <p-columnFilter
                type="text"
                field="currencyCode"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>
          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>
          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-addon let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(addon)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ addon.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ addon.name }}</td>
          <td class="capitalize">
            {{ addon.linkTable?.replaceAll("_", " ") }}
          </td>
          <td>{{ addon.categoryType }}</td>
          <td>{{ addon.exchangeName }}</td>
          <td>{{ addon.price }}</td>
          <td>{{ addon.currencyCode }}</td>
          <td>{{ addon.status }}</td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button
                  size="icon"
                  (click)="toggleModal('viewItem', addon)"
                  >
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', addon)"
                  >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-addon>
        <tr>
          <td colspan="9">
            <div class="p-4 bg-gray-50 rounded-md">
              <div
                class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
                >
                <!-- Basic Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">Name</h5>
                  <p class="text-gray-800">{{ addon.name }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Link Table
                  </h5>
                  <p class="text-gray-800 capitalize">
                    {{ addon.linkTable?.replaceAll("_", " ") }}
                  </p>
                </div>
                @if (addon.categoryType) {
                  <div>
                    <h5 class="text-sm font-semibold text-gray-600 mb-1">
                      Category Type
                    </h5>
                    <p class="text-gray-800">
                      {{ addon.categoryType }}
                    </p>
                  </div>
                }
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">
                    {{ addon.status }}
                  </p>
                </div>

                <!-- Price & Currency Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Price
                  </h5>
                  <p class="text-gray-800">
                    {{ addon.price | currency }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Currency
                  </h5>
                  <p class="text-gray-800">
                    {{ addon.currencyCode }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Exchange
                  </h5>
                  <p class="text-gray-800">
                    {{ addon.exchangeName }}
                  </p>
                </div>

                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ addon.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                    addon.modifiedDate
                    ? (addon.modifiedDate | date: "yyyy-MM-dd HH:mm:ss")
                    : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ addon.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ addon.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Description if available -->
              @if (addon.description) {
                <div class="mt-4">
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Description
                  </h5>
                  <p class="text-gray-800 whitespace-pre-line">
                    {{ addon.description }}
                  </p>
                </div>
              }

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <ng-container *appButtonPermission="'edit'">
                  <app-button
                    size="sm"
                    variant="secondary"
                    icon="pi pi-pencil"
                    (click)="toggleModal('editItem', addon)"
                    >
                    Edit
                  </app-button>
                </ng-container>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="9" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No addons found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- Add Addon Modal -->
<app-modal [isVisible]="modals.addItem" [config]="{ title: 'Add Addon' }">
  <form
    [formGroup]="addAddonForm"
    (ngSubmit)="handleAddonSubmit('add')"
    class="flex flex-col gap-6"
    >
    <div class="grid gap-3 md:grid-cols-2">
      <app-form-field
        label="Name"
        type="text"
        [control]="addAddonForm.controls.name"
        [required]="true"
        />

      <app-form-field
        label="Link Table"
        placeholder="Link Table"
        type="select"
        [options]="addonTableOptions"
        [control]="addAddonForm.controls.linkTable"
        [required]="true"
        />

      <app-form-field
        label="Category Type"
        placeholder="Category Type"
        type="select"
        [options]="categoryTypeOptions"
        [control]="addAddonForm.controls.categoryType"
        [required]="true"
        />

      <app-form-field
        label="Price"
        type="number"
        [control]="addAddonForm.controls.price"
        [required]="true"
        min="0"
        />

      <app-form-field
        label="Currency"
        placeholder="Currency"
        type="select"
        [options]="currencyOptions"
        [control]="addAddonForm.controls.currencyId"
        [required]="true"
        />

      <app-form-field
        label="Exchange"
        placeholder="Exchange"
        type="select"
        [options]="exchangeOptions"
        [control]="addAddonForm.controls.exchangeName"
        [required]="true"
        />

      <app-form-field
        label="Description"
        placeholder="Description"
        type="textarea"
        [control]="addAddonForm.controls.description"
        />
    </div>

    <div class="flex items-center justify-end gap-3">
      <app-button (click)="toggleModal('addItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- Edit Addon Modal -->
<app-modal [isVisible]="modals.editItem" [config]="{ title: 'Edit Addon' }">
  <form [formGroup]="editAddonForm" (ngSubmit)="handleAddonSubmit('update')">
    <div class="grid md:grid-cols-2 gap-3">
      <app-form-field
        label="Name"
        type="text"
        [control]="editAddonForm.controls.name!"
        [required]="true"
        />

      <app-form-field
        label="Price"
        type="number"
        [control]="editAddonForm.controls.price!"
        [required]="true"
        min="0"
        />

      <app-form-field
        label="Currency"
        placeholder="Currency"
        type="select"
        [options]="currencyOptions"
        [control]="editAddonForm.controls.currencyId!"
        [required]="true"
        />

      <app-form-field
        label="Link Table"
        placeholder="Link Table"
        type="select"
        [options]="addonTableOptions"
        [control]="editAddonForm.controls.linkTable!"
        [required]="true"
        />

      <app-form-field
        label="Category Type"
        placeholder="Category Type"
        type="select"
        [options]="categoryTypeOptions"
        [control]="editAddonForm.controls.categoryType!"
        [required]="true"
        />

      <app-form-field
        label="Exchange"
        placeholder="Exchange"
        type="select"
        [options]="exchangeOptions"
        [control]="editAddonForm.controls.exchangeName!"
        [required]="true"
        />

      <app-form-field
        label="Description"
        placeholder="Description"
        type="textarea"
        [control]="editAddonForm.controls.description!"
        />

      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
        [control]="editAddonForm.get('status')!"
        [required]="true"
        />
    </div>

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="toggleModal('editItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- View Addon Modal -->
<app-modal [isVisible]="modals.viewItem" [config]="{ title: 'View Addon' }">
  <div class="flex flex-col gap-3">
    <div>
      <label class="form-label">Name</label>
      <input
        type="text"
        [value]="selectedAddon?.name"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Description</label>
      <input
        type="text"
        [value]="selectedAddon?.description"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Link Table</label>
      <input
        type="text"
        [value]="selectedAddon?.linkTable"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Exchange</label>
      <input
        type="text"
        [value]="selectedAddon?.exchangeName"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Price</label>
      <input
        type="text"
        [value]="selectedAddon?.price"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Currency</label>
      <input
        type="text"
        [value]="selectedAddon?.currencyCode"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Status</label>
      <input
        type="text"
        [value]="selectedAddon?.status"
        class="form-control"
        disabled
        />
    </div>

    <div class="flex justify-end">
      <app-button (click)="toggleModal('viewItem')">Close</app-button>
    </div>
  </div>
</app-modal>

<p-toast />
