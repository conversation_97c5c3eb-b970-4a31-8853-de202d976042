// hist-prices.component.ts
import { ClientProductType } from '@/app/core/enums/client-product-type.enum';
import { Exchange } from '@/app/core/enums/exchange.enum';
import { Market } from '@/app/core/enums/market.enum';
import { ExcelExportService } from '@/app/core/services/excel-export.service';
import { UtilityService } from '@/app/core/services/utility.service';
import { DatePipe, DecimalPipe } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  inject,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { finalize, Subject, takeUntil } from 'rxjs';
import { DashboardLayoutComponent } from '../../../shared/layouts/dashboard/dashboard-layout.component';
import { HistoricalPrice, HistoricalPricesResponse } from './hist-prices.model';
import { HistPricesService } from './hist-prices.service';

// PrimeNG imports
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { ChartModule } from 'primeng/chart';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { SplitButtonModule } from 'primeng/splitbutton';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { ButtonComponent } from '../../../shared/ui/button/button.component';
import { CardComponent } from '../../../shared/ui/card/card.component';
import { CustomSortIconComponent } from '../../../shared/ui/table/custom-sort-icon/custom-sort-icon.component';
import { TextComponent } from '../../../shared/ui/text/text.component';

@Component({
  selector: 'app-hist-prices',
  templateUrl: './hist-prices.component.html',
  styleUrls: ['./hist-prices.component.css'],
  standalone: true,
  imports: [
    DatePipe,
    DecimalPipe,
    FormsModule,
    DashboardLayoutComponent,
    // PrimeNG modules
    BadgeModule,
    ButtonModule,
    CalendarModule,
    CardModule,
    ChartModule,
    ConfirmDialogModule,
    DialogModule,
    InputTextModule,
    ProgressSpinnerModule,
    SplitButtonModule,
    TableModule,
    ToastModule,
    TooltipModule,
    CardComponent,
    ButtonComponent,
    TextComponent,
    IconFieldModule,
    InputIconModule,
    CustomSortIconComponent,
  ],
  providers: [MessageService, ConfirmationService],
})
export class HistPricesComponent implements OnInit, OnDestroy {
  private readonly utilityService = inject(UtilityService);
  private readonly messageService = inject(MessageService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly cdr = inject(ChangeDetectorRef);
  private destroy$ = new Subject<void>();

  @ViewChild('fromDateCalendar') fromDateCalendar!: ElementRef;

  // Data
  historicalPrices: HistoricalPrice[] = [];
  companies: string[] = [];
  exchange: Exchange = Exchange.ZSE;
  market: Market = Market.REG;

  // States
  loading = true;
  showVisualizationDialog = false;
  hasLoadedData = false;

  // Dates
  today = new Date();
  minDateValue = new Date(
    this.today.getFullYear() - 2,
    this.today.getMonth(),
    this.today.getDate(),
  );
  maxDateValue = this.today;
  fromDate: Date;
  toDate: Date;

  // Route parameters
  productId: string | null = null;
  subscriptionId: string | null = null;
  type: ClientProductType | null = null;
  name: string | null = null;
  expiryPeriod: number | null = null;
  exchangeId: number | null = null;

  // Chart data
  chartData: any;
  chartOptions: any;

  constructor(
    private router: Router,
    private histPricesService: HistPricesService,
    private excelExportService: ExcelExportService,
  ) {
    // Initialize dates with sensible defaults - 30 days by default
    const defaultStartDate = this.getDefaultStartDate();
    this.fromDate = new Date(defaultStartDate);
    this.toDate = new Date();

    // Check for route state
    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras.state as { [key: string]: any };

    if (state) {
      this.productId = state['productId'];
      this.subscriptionId = state['subscriptionId'];
      this.type = state['type'];
      this.name = state['name'] || 'Historical Prices';
      this.expiryPeriod = state['expiryPeriod'];
      this.exchangeId = state['exchangeId'];

      // Parse dates from strings first
      const startDate = state['startDate']
        ? new Date(state['startDate'])
        : null;
      const endDate = state['endDate'] ? new Date(state['endDate']) : null;

      // Set from and to dates
      if (startDate) {
        this.fromDate = startDate;
      }
      if (endDate) {
        this.toDate = endDate;
      }

      // Set min date (2 years before start date)
      if (startDate) {
        this.minDateValue = new Date(
          startDate.getFullYear(),
          startDate.getMonth(),
          startDate.getDate(),
        );
      }

      // Set max date to today
      if (endDate) {
        this.maxDateValue = new Date(
          endDate.getFullYear(),
          endDate.getMonth(),
          endDate.getDate(),
        );
      }
    }
  }

  ngOnInit(): void {
    // Add this code to read the exchange parameter from URL
    this.router.routerState.root.queryParams.subscribe((params: { [key: string]: any }) => {
      const exchangeParam = params["exchange"];
      if (exchangeParam) {
        this.exchange = exchangeParam as Exchange;
        this.updateMinDateValue();
      }
      
      if (this.exchangeId) {
        this.setExchangeFromId();
      } else {
        this.loadHistoricalPricesData();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Getters for template
  get isZse(): boolean {
    return this.exchange === Exchange.ZSE;
  }

  get exchangeName(): string {
    return `${this.isZse ? 'Zimbabwe' : 'Victoria Falls'} Stock Exchange`;
  }

  get fromDateFormatted(): string {
    return this.fromDate
      ? new Intl.DateTimeFormat('en-GB', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        }).format(this.fromDate)
      : '';
  }

  get toDateFormatted(): string {
    return this.toDate
      ? new Intl.DateTimeFormat('en-GB', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        }).format(this.toDate)
      : '';
  }

  // Methods
  getDefaultStartDate(): string {
    const date = new Date();
    date.setMonth(date.getMonth() - 1);
    return date.toISOString().split('T')[0];
  }

  setExchangeFromId() {
    this.utilityService
      .getExchangeById(this.exchangeId as number)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          if (res && res.data) {
            this.exchange = res.data === 'ZSE' ? Exchange.ZSE : Exchange.VFEX;
            this.loadHistoricalPricesData();
          }
        },
        error: (error) => {
          console.error('Error fetching exchange id:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to determine exchange type. Using default.',
            life: 5000,
          });
          this.loadHistoricalPricesData();
        },
      });
  }

  loadHistoricalPricesData(): void {
    this.loading = true;
    this.hasLoadedData = false;

    // It's good practice to create new Date objects to avoid modifying the original ones.
    const newFromDate = new Date(this.fromDate);
    const newToDate = new Date(this.toDate);

    // Add one year to the fromDate
    newFromDate.setDate(newFromDate.getDate() + 1);

    // Add one day to the toDate
    newToDate.setDate(newToDate.getDate() + 1);

    // Now, convert the newly calculated dates to strings
    const fromDateStr = newFromDate.toISOString().split('T')[0];
    const toDateStr = newToDate.toISOString().split('T')[0];

    this.histPricesService
      .getHistoricalPrices(this.exchange, fromDateStr, toDateStr)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.loading = false;
          this.hasLoadedData = true;
          this.cdr.detectChanges();
        }),
      )
      .subscribe({
        next: (response: HistoricalPricesResponse) => {
          // Add searchable date field to each item
          this.historicalPrices = (response.data || []).map((item) => {
            return {
              ...item,
              // Add a searchable date string for filtering
              searchableDate: new Date(item['stats-date']).toLocaleDateString(
                'en-GB',
              ),
            };
          });
          this.extractHeaders();
          this.prepareChartData();
        },
        error: (error) => {
          console.error('Error fetching historical prices:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Data Error',
            detail: 'Failed to load historical prices. Please try again.',
            life: 5000,
          });
          // Reset data on error
          this.historicalPrices = [];
          this.companies = [];
        },
      });
  }

  extractHeaders(): void {
    if (this.historicalPrices.length > 0) {
      // Get all unique keys except 'stats-date' and 'searchableDate'
      const allKeys = this.historicalPrices.reduce((keys, item) => {
        Object.keys(item).forEach((key) => {
          if (
            key !== 'stats-date' &&
            key !== 'searchableDate' &&
            !keys.includes(key)
          ) {
            keys.push(key);
          }
        });
        return keys;
      }, [] as string[]);

      // Sort alphabetically
      this.companies = allKeys.sort();
    } else {
      this.companies = [];
    }
  }

  get globalFilterFields(): string[] {
    // Start with the static fields
    const fields = ['searchableDate', 'stats-date'];

    // Add all company names to the fields array if companies exists
    if (this.companies && this.companies.length) {
      return [...fields, ...this.companies];
    }

    return fields;
  }

  setDateRange(): void {
    // Validate date range
    if (!this.fromDate || !this.toDate) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Invalid Date Range',
        detail: 'Please select both start and end dates',
        life: 5000,
      });
      return;
    }

    if (this.fromDate > this.toDate) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Invalid Date Range',
        detail: 'The start date must be before the end date',
        life: 5000,
      });
      return;
    }

    // Check if date range is too wide (e.g., more than 2 years)
    const diffTime = Math.abs(this.toDate.getTime() - this.fromDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 730) {
      // 2 years
      this.confirmationService.confirm({
        message:
          'The selected date range is very large and may slow down performance. Do you want to continue?',
        header: 'Date Range Warning',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          this.loadHistoricalPricesData();
        },
      });
      return;
    }

    this.loadHistoricalPricesData();
  }

  getCompanyValue(date: string, company: string): number {
    const entry = this.historicalPrices.find(
      (item) => item['stats-date'] === date,
    );
    return entry && entry[company] ? Number(entry[company]) : 0;
  }

  focusDatePicker(): void {
    if (this.fromDateCalendar) {
      setTimeout(() => {
        this.fromDateCalendar.nativeElement.click();
      });
    }
  }

  exportToExcel() {
    this.confirmationService.confirm({
      message: 'Are you sure you want to export this data to Excel?',
      header: 'Export Confirmation',
      icon: 'pi pi-info-circle',
      accept: () => {
        // Format dates for filename
        const fromDateStr = this.fromDate.toISOString().split('T')[0];
        const toDateStr = this.toDate.toISOString().split('T')[0];
        const dateRange = `${fromDateStr}_to_${toDateStr}`;

        // Call the simplified export function
        this.histPricesService.exportToExcel(
          this.historicalPrices,
          this.companies,
          this.isZse ? 'ZSE' : 'VFEX',
          dateRange,
        );

        this.messageService.add({
          severity: 'success',
          summary: 'Export Complete',
          detail: 'Excel file has been generated successfully',
        });
      },
    });
  }

  prepareChartData() {
    if (!this.historicalPrices || this.historicalPrices.length === 0) {
      return;
    }

    // Format dates for the x-axis
    const labels = this.historicalPrices.map((item) =>
      new Date(item['stats-date']).toLocaleDateString('en-GB'),
    );

    // Get top companies by average price for better visualization
    const topCompanies = this.getTopCompanies(5);

    // Create datasets for top companies
    const datasets: any = [];

    // Array of colors for different companies
    const colors = [
      { border: '#3B82F6', bg: 'rgba(59, 130, 246, 0.05)' }, // Blue-500
      { border: '#F97316', bg: 'rgba(249, 115, 22, 0.05)' }, // Orange-500
      { border: '#10B981', bg: 'rgba(16, 185, 129, 0.05)' }, // Emerald-500
      { border: '#EC4899', bg: 'rgba(236, 72, 153, 0.05)' }, // Pink-500
      { border: '#8B5CF6', bg: 'rgba(139, 92, 246, 0.05)' }, // Violet-500
    ];

    // Add datasets for top companies
    topCompanies.forEach((company, index) => {
      datasets.push({
        label: company,
        data: this.historicalPrices.map((item) =>
          this.getCompanyValue(item['stats-date'], company),
        ),
        fill: false,
        borderColor: colors[index % colors.length].border,
        backgroundColor: colors[index % colors.length].bg,
        tension: 0.3,
        borderWidth: 2,
        pointRadius: 2,
        pointBackgroundColor: colors[index % colors.length].border,
        pointBorderColor: '#FFFFFF',
        pointBorderWidth: 1,
        pointHoverRadius: 4,
        order: index + 1,
      } as any);
    });

    this.chartData = {
      labels: labels,
      datasets: datasets,
    };

    // Configure chart options with improved styling
    this.chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        mode: 'index',
        intersect: false,
      },
      plugins: {
        legend: {
          position: 'top',
          align: 'center',
          labels: {
            boxWidth: 12,
            boxHeight: 12,
            padding: 15,
            usePointStyle: true,
            pointStyle: 'circle',
            font: {
              size: 12,
              family:
                "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
              weight: '500',
            },
          },
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          titleColor: '#1F2937',
          bodyColor: '#4B5563',
          borderColor: '#E5E7EB',
          borderWidth: 1,
          padding: 12,
          cornerRadius: 6,
          boxPadding: 6,
          usePointStyle: true,
          titleFont: {
            size: 14,
            weight: '600',
          },
          bodyFont: {
            size: 13,
            weight: '400',
          },
          callbacks: {
            // Format numbers with commas
            label: function (context: any) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed.y !== null) {
                label += new Intl.NumberFormat('en-US').format(
                  context.parsed.y,
                );
              }
              return label;
            },
          },
        },
        title: {
          display: true,
          text: `${this.exchangeName} Historical Prices Trend`,
          font: {
            size: 16,
            weight: '600',
            family:
              "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
          },
          padding: { top: 10, bottom: 20 },
          color: '#1F2937',
        },
        // Add subtle grid line styling
        datalabels: {
          display: false,
        },
      },
      scales: {
        x: {
          grid: {
            color: '#F3F4F6',
            drawBorder: false,
          },
          ticks: {
            maxRotation: 45,
            minRotation: 45,
            padding: 8,
            font: {
              size: 11,
              family:
                "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
            },
            color: '#6B7280',
          },
        },
        y: {
          beginAtZero: false,
          grid: {
            color: '#F3F4F6',
            drawBorder: false,
          },
          ticks: {
            padding: 10,
            font: {
              size: 11,
              family:
                "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
            },
            color: '#6B7280',
            callback: function (value: any) {
              return new Intl.NumberFormat('en-US', {
                notation: 'compact',
                compactDisplay: 'short',
              }).format(value);
            },
          },
          title: {
            display: true,
            text: 'Price',
            font: {
              size: 12,
              family:
                "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
              weight: '500',
            },
            color: '#4B5563',
            padding: {
              bottom: 10,
            },
          },
        },
      },
      animation: {
        duration: 1000,
        easing: 'easeOutQuart',
      },
      elements: {
        line: {
          tension: 0.3,
        },
      },
      // Set chart padding
      layout: {
        padding: {
          top: 20,
          right: 20,
          bottom: 20,
          left: 10,
        },
      },
    };
  }

  getTopCompanies(count: number): string[] {
    if (
      !this.historicalPrices ||
      this.historicalPrices.length === 0 ||
      !this.companies ||
      this.companies.length === 0
    ) {
      return [];
    }

    // Calculate average price for each company
    const companyAverages = this.companies.map((company) => {
      const values = this.historicalPrices.map((item) =>
        this.getCompanyValue(item['stats-date'], company),
      );
      const sum = values.reduce((acc, val) => acc + val, 0);
      return {
        company,
        average: sum / values.length,
      };
    });

    // Sort by average price (descending) and take top 'count'
    return companyAverages
      .sort((a, b) => b.average - a.average)
      .slice(0, count)
      .map((item) => item.company);
  }

  openDataVisualization() {
    this.prepareChartData();
    this.showVisualizationDialog = true;
  }

  updateMinDateValue(): void {
    const baseDate = new Date();
    const isZse = this.isZse;

    const yearOffset = isZse ? 15 : 2;

    const currentMonth = baseDate.getMonth(); // 0-based index
    const currentDay = baseDate.getDate();

    const monthOffset = 12 + currentMonth;

    const daysInMonth = new Date(baseDate.getFullYear(), currentMonth + 1, 0).getDate();
    const dayOffset = daysInMonth - currentDay;

    // Start from today
    const minDate = new Date(baseDate);

    // Subtract years, months, and days
    minDate.setFullYear(minDate.getFullYear() - yearOffset);
    minDate.setMonth(minDate.getMonth() - monthOffset);
    minDate.setDate(1);

    this.minDateValue = minDate;
  }
}
