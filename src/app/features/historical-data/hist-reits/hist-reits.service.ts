// hist-reits.service.ts
import { Exchange } from '@/app/core/enums/exchange.enum';
import { environment } from '@/environments/environment.development';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, retry, tap } from 'rxjs/operators';
import { HistoricalReitsResponse } from './hist-reits.model';

import * as ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root',
})
export class HistReitsService {
  private baseUrl = `${environment.apiBaseUrl}/historical`;
  private cachedData: Map<
    string,
    { timestamp: number; data: HistoricalReitsResponse }
  > = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

  constructor(private http: HttpClient) {}

  /**
   * Get historical REIT data with date range
   * @param exchange Stock exchange (ZSE or VFEX)
   * @param startDate Start date in ISO format (YYYY-MM-DD)
   * @param endDate End date in ISO format (YYYY-MM-DD)
   * @param useCache Whether to use cached data if available
   */
  getHistoricalReits(
    exchange: Exchange,
    startDate: string,
    endDate: string,
    useCache: boolean = true,
  ): Observable<HistoricalReitsResponse> {
    const cacheKey = `reits_${exchange}_${startDate}_${endDate}`;
    const now = Date.now();
    const cachedEntry = this.cachedData.get(cacheKey);

    // Return cached data if it exists and is still valid
    if (
      useCache &&
      cachedEntry &&
      now - cachedEntry.timestamp < this.CACHE_DURATION
    ) {
      console.log('Using cached historical REITs data');
      return of(cachedEntry.data);
    }

    const url = `${this.baseUrl}/reits?exchange=${exchange}&startDate=${startDate}&endDate=${endDate}`;

    return this.http.get<HistoricalReitsResponse>(url).pipe(
      retry(1), // Retry once on failure
      tap((response) => {
        // Cache the response
        this.cachedData.set(cacheKey, {
          timestamp: now,
          data: response,
        });
      }),
      catchError((error) => {
        console.error('Error fetching historical REITs:', error);
        return throwError(
          () =>
            new Error(
              'Failed to load historical REITs. Please try again later.',
            ),
        );
      }),
      // Process and normalize the data
      map((response) => {
        if (response && response.data && response.data.length > 0) {
          // Sort data by date (descending) and then by symbol (ascending)
          response.data.sort((a, b) => {
            const dateComparison =
              new Date(b['stats-date']).getTime() -
              new Date(a['stats-date']).getTime();
            if (dateComparison !== 0) return dateComparison;
            return a.symbol.localeCompare(b.symbol);
          });
        }
        return response;
      }),
    );
  }

  /**
   * Export data to Excel using ExcelJS, with each REIT on its own sheet
   * @param data Historical REITs data
   * @param exchangeName Exchange name (ZSE or VFEX)
   * @param dateRange Date range string for filename
   */
  exportToExcel(data: any[], exchangeName: string, dateRange: string): void {
    // Create a new workbook
    const workbook = new ExcelJS.Workbook();

    // Group data by symbol
    const reitsBySymbol: { [symbol: string]: any[] } = {};

    data.forEach((reit) => {
      const { symbol } = reit;
      if (!reitsBySymbol[symbol]) {
        reitsBySymbol[symbol] = [];
      }
      reitsBySymbol[symbol].push(reit);
    });

    // First, create a summary sheet
    const summarySheet = workbook.addWorksheet('Summary');

    // Add headers to summary sheet
    const summaryHeaders = [
      'Symbol',
      'Average Price',
      'Total Volume',
      'Total Value',
      'Latest Market Cap',
      'Data Points',
    ];
    summarySheet.columns = summaryHeaders.map((header) => ({
      header,
      key: header.replace(/\s+/g, ''),
      width: Math.max(16, header.length + 4),
    }));

    // Add summary data for each REIT
    Object.entries(reitsBySymbol).forEach(([symbol, reitData]) => {
      // Calculate summary statistics
      const avgPrice =
        reitData.reduce((sum, item) => sum + item.price, 0) / reitData.length;
      const totalVolume = reitData.reduce((sum, item) => sum + item.volume, 0);
      const totalValue = reitData.reduce((sum, item) => sum + item.value, 0);

      // Find the latest market cap (by date)
      const latestReit = reitData.sort(
        (a, b) =>
          new Date(b['stats-date']).getTime() -
          new Date(a['stats-date']).getTime(),
      )[0];

      // Add row to summary sheet
      summarySheet.addRow({
        Symbol: symbol,
        AveragePrice: avgPrice === 0 ? '-' : avgPrice,
        TotalVolume: totalVolume === 0 ? '-' : totalVolume,
        TotalValue: totalValue === 0 ? '-' : totalValue,
        LatestMarketCap:
          latestReit.marketCapitalisation === 0
            ? '-'
            : latestReit.marketCapitalisation,
        DataPoints: reitData.length === 0 ? '-' : reitData.length,
      });
    });

    // Style summary sheet
    this.styleWorksheet(summarySheet, summaryHeaders);

    // Define column mappings for data sheets
    const columnMappings: Record<string, string> = {
      Date: 'stats-date',
      Price: 'price',
      Volume: 'volume',
      Value: 'value',
      Units: 'units',
      'Market Cap': 'marketCapitalisation',
    };

    // Create individual sheets for each REIT symbol
    Object.entries(reitsBySymbol).forEach(([symbol, reitData]) => {
      // Create sheet for this REIT (limit sheet name to 31 characters per Excel limitations)
      const sheetName =
        symbol.length > 31 ? symbol.substring(0, 28) + '...' : symbol;
      const worksheet = workbook.addWorksheet(sheetName);

      // Add headers
      const headers = [
        'Date',
        'Price',
        'Volume',
        'Value',
        'Units',
        'Market Cap',
      ];
      worksheet.columns = headers.map((header) => ({
        header,
        key: header.replace(/\s+/g, ''),
        width: Math.max(12, header.length + 2),
      }));

      // Sort data by date (newest first)
      const sortedData = [...reitData].sort(
        (a, b) =>
          new Date(b['stats-date']).getTime() -
          new Date(a['stats-date']).getTime(),
      );

      // Add data rows
      sortedData.forEach((item) => {
        const row: any = {};

        // Process each column according to mappings
        Object.entries(columnMappings).forEach(([colKey, dataKey]) => {
          if (colKey === 'Date') {
            // Format date as DD/MM/YYYY
            row[colKey.replace(/\s+/g, '')] = new Date(
              item[dataKey],
            ).toLocaleDateString('en-GB');
          } else if (item[dataKey] === 0) {
            // Display dash for zero values
            row[colKey.replace(/\s+/g, '')] = '-';
          } else if (colKey === 'Value' || colKey === 'Market Cap') {
            // Add currency prefix based on exchange
            const currency = exchangeName.includes('ZSE') ? 'ZWG' : 'USD';
            row[colKey.replace(/\s+/g, '')] =
              `${currency} ${item[dataKey].toLocaleString()}`;
          } else {
            row[colKey.replace(/\s+/g, '')] = item[dataKey];
          }
        });

        worksheet.addRow(row);
      });

      // Style the worksheet
      this.styleWorksheet(worksheet, headers);
    });

    // Generate the Excel file
    workbook.xlsx.writeBuffer().then((buffer) => {
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, `${exchangeName}_Historical_REITs_${dateRange}.xlsx`);
    });
  }

  /**
   * Helper method to style Excel worksheets
   * @param worksheet Worksheet to style
   * @param headers Array of column headers
   */
  private styleWorksheet(
    worksheet: ExcelJS.Worksheet,
    headers: string[],
  ): void {
    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, size: 11 };
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }, // Light gray background
    };

    // Style all data cells
    for (let i = 2; i <= worksheet.rowCount; i++) {
      const row = worksheet.getRow(i);

      // Apply font size to all cells in the row
      row.font = { name: 'Calibri', size: 11 };

      // Apply number format and alignment to each cell
      row.eachCell((cell, colNumber) => {
        const columnName = headers[colNumber - 1];

        // Set alignment based on column type
        if (
          [
            'Price',
            'Volume',
            'Value',
            'Units',
            'Market Cap',
            'Average Price',
            'Total Volume',
            'Total Value',
            'Latest Market Cap',
            'Data Points',
          ].includes(columnName)
        ) {
          cell.alignment = { horizontal: 'right' };
        } else if (columnName === 'Date') {
          cell.alignment = { horizontal: 'center' };
        } else {
          cell.alignment = { horizontal: 'left' };
        }

        // Format numbers
        if (columnName === 'Price' || columnName === 'Average Price') {
          cell.numFmt = '#,##0.0000';
        } else if (
          ['Value', 'Market Cap', 'Total Value', 'Latest Market Cap'].includes(
            columnName,
          )
        ) {
          cell.numFmt = '#,##0.00';
        } else if (
          ['Volume', 'Units', 'Total Volume', 'Data Points'].includes(
            columnName,
          )
        ) {
          cell.numFmt = '#,##0';
        }

        // Add borders
        cell.border = {
          top: { style: 'thin', color: { argb: 'e0e0e0' } },
          left: { style: 'thin', color: { argb: 'e0e0e0' } },
          bottom: { style: 'thin', color: { argb: 'e0e0e0' } },
          right: { style: 'thin', color: { argb: 'e0e0e0' } },
        };
      });
    }

    // Apply alternating row colors
    for (let i = 2; i <= worksheet.rowCount; i++) {
      if (i % 2 === 0) {
        worksheet.getRow(i).eachCell((cell) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFF9F9F9' }, // Very light gray for even rows
          };
        });
      }
    }

    // Freeze header row and columns
    worksheet.views = [
      {
        state: 'frozen',
        xSplit: 1, // Freeze first column
        ySplit: 1, // Freeze first row
        topLeftCell: 'B2',
        activeCell: 'B2',
      },
    ];
  }
}
