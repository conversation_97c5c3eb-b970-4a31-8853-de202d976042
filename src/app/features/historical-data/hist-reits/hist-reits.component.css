/* Custom Tab View Styles - Mimicking PrimeNG TabView */
.custom-tab-view {
  @apply border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm;
}

.tab-nav {
  @apply bg-gray-50 border-b border-gray-200;
}

.tab-nav-content {
  @apply flex overflow-x-auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.tab-nav-content::-webkit-scrollbar {
  height: 4px;
}

.tab-nav-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.tab-nav-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.tab-nav-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.tab-header {
  @apply px-4 py-3 text-sm font-medium text-gray-600 border-b-2 border-transparent 
         hover:text-gray-800 hover:border-gray-300 hover:bg-gray-100
         transition-all duration-200 whitespace-nowrap flex-shrink-0 
         focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset
         cursor-pointer relative;
  background: transparent;
  min-width: fit-content;
}

.tab-header:hover {
  background: linear-gradient(to bottom, #f9fafb, #f3f4f6);
}

.tab-header-active {
  @apply text-blue-600 border-blue-600 bg-white font-semibold;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.tab-header-active::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: #2563eb;
  border-radius: 1px 1px 0 0;
}

.tab-header-active:hover {
  @apply text-blue-700 border-blue-700;
  background: white;
}

.tab-content {
  @apply p-0 bg-white;
}

/* Chart container styles for dialog */
.chart-container {
  height: 400px;
  width: 100%;
}

/* Visualization dialog styles */
:host ::ng-deep .visualization-dialog .p-dialog-content {
  padding: 1rem;
}

:host ::ng-deep .visualization-dialog .p-dialog-header {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

/* Responsive design for tabs */
@media (max-width: 640px) {
  .tab-header {
    @apply px-3 py-2 text-xs;
    min-width: 80px;
  }
  
  .custom-tab-view {
    @apply rounded-md;
  }
}

/* Focus styles for accessibility */
.tab-header:focus-visible {
  @apply ring-2 ring-blue-500 ring-offset-2;
  outline: none;
}

/* Animation for tab switching */
.tab-content {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
