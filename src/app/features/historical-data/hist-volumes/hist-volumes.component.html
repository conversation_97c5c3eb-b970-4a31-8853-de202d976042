<div class="flex flex-col gap-5 max-w-screen-2xl mx-auto">
    <!-- Header Card with Controls -->
    <app-card>
      <div class="flex flex-wrap justify-between items-center gap-6">
        <!-- Title with indicator -->
        <div class="flex items-center gap-2">
          <!-- <app-text variant="titleLg/semibold">{{ name }}</app-text> -->
           <app-text variant="titleLg/semibold">Historical Volumes</app-text>
        </div>

        <!-- Toolbar controls -->
        <div class="flex gap-6 flex-wrap">
          <!-- Date range with P-Calendar -->
          <div class="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
            <span class="flex items-center gap-2">
              <label for="fromDate">From</label>

              <p-calendar
                [(ngModel)]="fromDate"
                [showIcon]="true"
                [minDate]="minDateValue"
                [maxDate]="maxDateValue"
                [readonlyInput]="true"
                dateFormat="dd/mm/yy"
                inputId="fromDate"
                inputStyleClass="w-36 py-1"
              >
              </p-calendar>
            </span>

            <span class="flex items-center gap-2">
              <label for="toDate">To</label>

              <p-calendar
                [(ngModel)]="toDate"
                [showIcon]="true"
                [minDate]="minDateValue"
                [maxDate]="maxDateValue"
                [readonlyInput]="true"
                dateFormat="dd/mm/yy"
                inputId="toDate"
                inputStyleClass="w-36 py-1"
              >
              </p-calendar>
            </span>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-3 justify-end">
            <app-button (click)="setDateRange()">
              <span class="flex items-center gap-2">
                <i class="text-sm pi pi-filter"></i>
                <span> Apply </span>
              </span>
            </app-button>
          </div>
        </div>
      </div>
    </app-card>

    <!-- Main Content Card -->
    <app-card class="overflow-hidden">
      <!-- Header -->
      <div class="text-center mb-6">
        <app-text variant="title/semibold">
          {{ isZse ? "Zimbabwe" : "Victoria Falls" }} Stock Exchange Historical
          Volumes
        </app-text>
        <p class="text-sm text-gray-500">
          {{ fromDateFormatted }} - {{ toDateFormatted }}
        </p>
      </div>

      <!-- Loading state -->
      @if (loading) {
        <div class="flex justify-center items-center py-16">
          <p-progressSpinner
            [style]="{ width: '50px', height: '50px' }"
            animationDuration=".5s"
            strokeWidth="4"
          ></p-progressSpinner>
        </div>
      } @else {
        <div id="exportDiv">
          @if (historicalVolumes && historicalVolumes.length > 0) {
            <!-- Data Table -->
            <p-table
              #dt
              [value]="historicalVolumes"
              [scrollable]="true"
              scrollHeight="500px"
              scrollDirection="both"
              [tableStyle]="{ 'min-width': '100%' }"
              styleClass="p-datatable-sm p-datatable-gridlines"
              [resizableColumns]="true"
              [reorderableColumns]="true"
              columnResizeMode="expand"
              [paginator]="false"
              [rows]="10"
              [rowsPerPageOptions]="[10, 25, 50]"
              [globalFilterFields]="globalFilterFields"
              responsiveLayout="scroll"
              [showCurrentPageReport]="true"
              currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
            >
              <!-- Table Caption -->
              <ng-template pTemplate="caption">
                <div class="flex flex-col sm:flex-row justify-between gap-3">
                  <div class="flex gap-3 items-center">
                    @if (historicalVolumes && historicalVolumes.length > 0) {
                      <app-button
                        class="hidden sm:block"
                        size="sm"
                        variant="primary"
                        (click)="openDataVisualization()"
                      >
                        <div class="flex gap-2 items-center">
                          <i class="pi pi-chart-line text-sm"></i>
                          <span>Visualize Data</span>
                        </div>
                      </app-button>
                    }

                    <app-button
                      size="sm"
                      variant="success"
                      (click)="exportToExcel()"
                    >
                      <span class="flex items-center gap-2">
                        <i class="text-sm pi pi-file-excel"></i>
                        <span> Export </span>
                      </span>
                    </app-button>
                  </div>

                  <p-iconfield>
                    <p-inputicon styleClass="pi pi-search" />

                    <input
                      pSize="small"
                      pInputText
                      type="text"
                      (input)="
                        dt.filterGlobal($any($event.target).value, 'contains')
                      "
                      placeholder="Search by date"
                      class="w-full sm:w-auto"
                    />
                  </p-iconfield>
                </div>
              </ng-template>

              <!-- Table Header -->
              <ng-template pTemplate="header">
                <tr
                  class="text-xs font-semibold text-gray-600 uppercase text-nowrap"
                >
                  <th
                    pFrozenColumn
                    alignFrozen="left"
                    pSortableColumn="stats-date"
                    class="min-w-[120px] bg-gray-100 text-gray-600 font-semibold"
                  >
                    <div class="flex items-center gap-2">
                      <span>Date</span>
                      <app-custom-sort-icon
                        field="stats-date"
                        [table]="dt"
                      ></app-custom-sort-icon>
                    </div>
                  </th>

                  @for (company of companies; track company) {
                    <th
                      [pSortableColumn]="company"
                      class="min-w-[130px] bg-gray-50 text-gray-600 font-semibold"
                    >
                      <div class="flex items-center gap-2">
                        <span>{{ company }}</span>
                        <app-custom-sort-icon
                          [field]="company"
                          [table]="dt"
                        ></app-custom-sort-icon>
                      </div>
                    </th>
                  }

                  <th
                    pFrozenColumn
                    alignFrozen="right"
                    pSortableColumn="total"
                    class="min-w-[120px] bg-gray-100 text-gray-600 font-semibold text-right"
                  >
                    <div class="flex items-center justify-end gap-2">
                      <span>Total</span>
                      <app-custom-sort-icon
                        field="total"
                        [table]="dt"
                      ></app-custom-sort-icon>
                    </div>
                  </th>
                </tr>
              </ng-template>

              <!-- Table Body -->
              <ng-template pTemplate="body" let-item>
                <tr class="hover:bg-gray-50 transition-colors">
                  <td
                    pFrozenColumn
                    alignFrozen="left"
                    class="font-medium text-gray-800 bg-gray-100"
                  >
                    {{ item["stats-date"] | date: "dd/MM/yyyy" }}
                  </td>

                  @for (company of companies; track company) {
                    <td class="text-right">
                      {{
                        getCompanyValue(item["stats-date"], company) === 0
                          ? "-"
                          : (getCompanyValue(item["stats-date"], company)
                            | number)
                      }}
                    </td>
                  }

                  <td
                    pFrozenColumn
                    alignFrozen="right"
                    class="font-semibold text-right text-gray-800 bg-gray-100"
                  >
                    {{ item.total | number }}
                  </td>
                </tr>
              </ng-template>

              <!-- Empty Message -->
              <ng-template pTemplate="emptymessage">
                <tr>
                  <td colspan="100">
                    <div class="flex flex-col items-center py-10">
                      <i
                        class="pi pi-info-circle text-4xl text-gray-300 mb-4"
                      ></i>
                      <p class="text-base text-gray-500 mb-2">
                        No data available
                      </p>
                      <p class="text-sm text-gray-400">
                        No historical data for the selected date range
                      </p>
                    </div>
                  </td>
                </tr>
              </ng-template>
            </p-table>
          } @else {
            <!-- Empty state when no data -->
            <div class="flex flex-col items-center justify-center py-16 px-4">
              <div class="bg-gray-50 rounded-full p-6 mb-5">
                <i class="pi pi-database text-4xl text-gray-300"></i>
              </div>
              <h3 class="text-base font-semibold text-gray-700 mb-2">
                No Data Available
              </h3>
              <p class="text-base text-gray-500 text-center mb-6">
                There are no historical volumes for the selected period.
              </p>
            </div>
          }
        </div>
      }
    </app-card>

    <!-- Data Visualization Dialog -->
    <p-dialog
      [(visible)]="showVisualizationDialog"
      [style]="{ width: '90%' }"
      [modal]="true"
      [draggable]="false"
      [resizable]="false"
      header="Historical Volumes Visualization"
      styleClass="visualization-dialog"
      [maximizable]="true"
    >
      <div class="chart-container">
        <p-chart
          type="line"
          [data]="chartData"
          [options]="chartOptions"
        ></p-chart>
      </div>

      <ng-template pTemplate="footer">
        <div class="flex justify-end gap-3">
          <p-button
            label="Close"
            icon="pi pi-times"
            (click)="showVisualizationDialog = false"
            styleClass="p-button-outlined"
          ></p-button>
        </div>
      </ng-template>
    </p-dialog>
  </div>

  <!-- Toast for notifications -->
  <p-toast position="top-right"></p-toast>

  <!-- Confirmation dialog for actions -->
  <p-confirmDialog
    header="Confirmation"
    icon="pi pi-exclamation-triangle"
    [style]="{ width: '450px' }"
    [position]="'top'"
    acceptButtonStyleClass="p-button-primary"
    rejectButtonStyleClass="p-button-outlined"
  ></p-confirmDialog>