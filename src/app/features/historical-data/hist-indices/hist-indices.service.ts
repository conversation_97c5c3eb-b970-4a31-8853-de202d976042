// hist-indices.service.ts
import { Exchange } from '@/app/core/enums/exchange.enum';
import { environment } from '@/environments/environment.development';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, retry, tap } from 'rxjs/operators';
import { HistoricalIndicesResponse } from './hist-indices.model';

import * as ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root',
})
export class HistIndicesService {
  private baseUrl = `${environment.apiBaseUrl}/historical`;
  private cachedData: Map<
    string,
    { timestamp: number; data: HistoricalIndicesResponse }
  > = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

  constructor(private http: HttpClient) {}

  /**
   * Get historical indices with date range
   * @param exchange Stock exchange (ZSE or VFEX)
   * @param startDate Start date in ISO format (YYYY-MM-DD)
   * @param endDate End date in ISO format (YYYY-MM-DD)
   * @param useCache Whether to use cached data if available
   */
  getHistoricalIndices(
    exchange: Exchange,
    startDate: string,
    endDate: string,
    useCache: boolean = true,
  ): Observable<HistoricalIndicesResponse> {
    const cacheKey = `${exchange}_${startDate}_${endDate}`;
    const now = Date.now();
    const cachedEntry = this.cachedData.get(cacheKey);

    // Return cached data if it exists and is still valid
    if (
      useCache &&
      cachedEntry &&
      now - cachedEntry.timestamp < this.CACHE_DURATION
    ) {
      console.log('Using cached historical indices data');
      return of(cachedEntry.data);
    }

    const url = `${this.baseUrl}/indices?exchange=${exchange}&startDate=${startDate}&endDate=${endDate}`;

    return this.http.get<HistoricalIndicesResponse>(url).pipe(
      retry(1), // Retry once on failure
      tap((response) => {
        // Cache the response
        this.cachedData.set(cacheKey, {
          timestamp: now,
          data: response,
        });
      }),
      catchError((error) => {
        console.error('Error fetching historical indices:', error);
        return throwError(
          () =>
            new Error(
              'Failed to load historical indices. Please try again later.',
            ),
        );
      }),
      // Post-process the data to ensure all entries have the same structure
      map((response) => {
        if (response && response.data && response.data.length > 0) {
          // Normalize the data to ensure consistent properties
          const allKeys = this.extractAllIndices(response.data);

          response.data = response.data.map((item) => {
            // Ensure all keys exist on each item, with default value 0
            allKeys.forEach((key) => {
              if (key !== 'stats-date' && item[key] === undefined) {
                item[key] = 0;
              }
            });

            return item;
          });
        }

        return response;
      }),
    );
  }

  /**
   * Extract all unique keys from the data set (index names)
   */
  private extractAllIndices(data: any[]): string[] {
    const keySet = new Set<string>();

    data.forEach((item) => {
      Object.keys(item).forEach((key) => {
        if (key !== 'stats-date') {
          keySet.add(key);
        }
      });
    });

    return Array.from(keySet).sort();
  }

  /**
   * Export data to Excel using ExcelJS
   * @param data Historical indices data
   * @param indices List of index names
   * @param exchangeName Exchange name (ZSE or VFEX)
   * @param dateRange Date range string for filename
   */
  exportToExcel(
    data: any[],
    indices: string[],
    exchangeName: string,
    dateRange: string,
  ): void {
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Historical Indices');

    // Add headers - including Date as first column
    const headers = ['Date', ...indices];
    worksheet.columns = headers.map((header) => ({
      header,
      key: header,
      width: header === 'Date' ? 15 : Math.max(12, header.length + 2),
    }));

    // Add data rows
    data.forEach((item) => {
      const row: any = {
        // Format date as DD/MM/YYYY
        Date: new Date(item['stats-date']).toLocaleDateString('en-GB'),
      };

      // Add index values
      indices.forEach((index) => {
        // Use dash for zero values
        row[index] = item[index] ? item[index] : '-';
      });

      worksheet.addRow(row);
    });

    // Auto-calculate widths based on content
    worksheet.columns.forEach((column) => {
      if (!column) return;

      let maxLength = 0;
      column.eachCell &&
        column.eachCell({ includeEmpty: true }, (cell, rowNumber) => {
          if (!cell) return;

          // Get displayed value length
          let cellLength = 0;
          if (cell.value !== null && cell.value !== undefined) {
            if (typeof cell.value === 'number') {
              // For numbers, estimate display length with formatting
              const numStr = cell.value.toFixed(4).toString();
              // Account for thousand separators (roughly)
              cellLength = numStr.length + Math.floor((numStr.length - 5) / 3);
            } else {
              cellLength = cell.value.toString().length;
            }
          }

          // Get column header length
          const headerLength =
            column.header && column.header.length ? column.header.length : 0;

          // Take the max
          maxLength = Math.max(maxLength, cellLength, headerLength);
        });

      // Add a buffer (larger for numeric columns)
      const isNumeric = column.key !== 'Date';
      column.width = maxLength + (isNumeric ? 6 : 4);
    });

    // Style the header row - all headers are bold
    const headerRow = worksheet.getRow(1);
    if (headerRow) {
      headerRow.font = { bold: true, size: 11 };
      headerRow.alignment = { vertical: 'middle', horizontal: 'center' };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }, // Light gray background
      };
    }

    // Style all data cells
    for (let i = 2; i <= data.length + 1; i++) {
      const row = worksheet.getRow(i);
      if (!row) continue;

      // Apply font size to all cells in the row
      row.font = { name: 'Calibri', size: 11 };

      // Apply number format and alignment to each cell
      row.eachCell &&
        row.eachCell((cell, colNumber) => {
          if (!cell) return;

          const columnName = headers[colNumber - 1];

          // Right-align all cells except Date column
          if (columnName !== 'Date') {
            cell.alignment = { horizontal: 'right' };
          }

          // Format numbers with thousand separators and 4 decimal places, except for Date column
          if (columnName !== 'Date' && typeof cell.value === 'number') {
            cell.numFmt = '#,##0.0000';
          }

          // Bold and highlight the Date column
          if (columnName === 'Date') {
            cell.font = { bold: true, name: 'Calibri', size: 11 };
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFE0E0E0' }, // Light gray background
            };
          } else {
            // Set a lighter color for value cells
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFFFFFF' }, // Very light gray/almost white
            };
          }

          // Center align dates
          if (columnName === 'Date') {
            cell.alignment = { horizontal: 'center' };
          }
        });
    }

    // Freeze header row and Date column
    worksheet.views = [
      {
        state: 'frozen',
        xSplit: 1, // Freeze first column (Date)
        ySplit: 1, // Freeze first row (Headers)
        topLeftCell: 'B2',
        activeCell: 'B2',
      },
    ];

    // Add a thin border to all cells
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin', color: { argb: 'e0e0e0' } },
          left: { style: 'thin', color: { argb: 'e0e0e0' } },
          bottom: { style: 'thin', color: { argb: 'e0e0e0' } },
          right: { style: 'thin', color: { argb: 'e0e0e0' } },
        };
      });
    });

    // Generate the Excel file
    workbook.xlsx.writeBuffer().then((buffer) => {
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, `${exchangeName}_Historical_Indices_${dateRange}.xlsx`);
    });
  }
}
