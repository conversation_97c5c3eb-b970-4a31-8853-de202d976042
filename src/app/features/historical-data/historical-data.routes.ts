import { Routes } from "@angular/router";

export const HISTORICAL_DATA_ROUTES: Routes = [
  {
    path: "",
    loadComponent: () =>
      import("./historical-data.component").then((m) => m.HistoricalDataComponent),
  },
  {
    path: "prices",
    loadComponent: () =>
      import("./hist-prices/hist-prices.component").then(
        (m) => m.HistPricesComponent,
      ),
  },
   {
    path: "market-cap",
    loadComponent: () =>
      import("./hist-market-cap/hist-market-cap.component").then(
        (m) => m.HistMarketCapComponent,
      ),
  },

   {
    path: "etfs",
    loadComponent: () =>
      import("./hist-etfs/hist-etfs.component").then(
        (m) => m.HistEtfsComponent,
      ),
  },

  {
    path: "indices",
    loadComponent: () =>
      import("./hist-indices/hist-indices.component").then(
        (m) => m.HistIndicesComponent,
      ),
  },

  {
    path: "reits",
    loadComponent: () =>
      import("./hist-reits/hist-reits.component").then(
        (m) => m.HistReitsComponent,
      ),
  },

  {
    path: "values",
    loadComponent: () =>
      import("./hist-values/hist-values.component").then(
        (m) => m.HistValuesComponent,
      ),
  },

   {
    path: "volumes",
    loadComponent: () =>
      import("./hist-volumes/hist-volumes.component").then(
        (m) => m.HistVolumesComponent,
      ),
  },
//   {
//     path: "indices",
//     loadComponent: () =>
//       import("./indices/indices.component").then((m) => m.ATSIndicesComponent),
//   },
//   {
//     path: "reits-stats",
//     loadComponent: () =>
//       import("./reitsStats/reits-stats.component").then(
//         (m) => m.ATSREITSStatsComponent,
//       ),
//   },
//   {
//     path: "vfex-stats",
//     loadComponent: () =>
//       import("./vfex-stats/vfex-stats.component").then(
//         (m) => m.ATSVFEXStatsComponent,
//       ),
//   },
//   // VFEX REITs Stats now uses the same component as ZSE REITs Stats
//   {
//     path: "foreign-trades",
//     loadComponent: () =>
//       import("./foreign-trades/foreign-trades.component").then(
//         (m) => m.ATSForeignTradesComponent,
//       ),
//   },
//   {
//     path: "dtr",
//     loadComponent: () =>
//       import("./dtr/dtr.component").then((m) => m.ATSDtrComponent),
//   },
//   {
//     path: "wtr",
//     loadComponent: () =>
//       import("./wtr/wtr.component").then((m) => m.ATSWtrComponent),
//   },
//   {
//     path: "price-sheet",
//     loadComponent: () =>
//       import("./price-sheet/price-sheet.component").then(
//         (m) => m.ATSPriceSheetComponent,
//       ),
//   },
//   {
//     path: "market-commentary",
//     loadComponent: () =>
//       import("./market-commentary/market-commentary.component").then(
//         (m) => m.AtsMarketCommentaryComponent,
//       ),
//   },
];
