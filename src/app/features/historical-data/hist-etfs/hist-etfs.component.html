<div class="flex flex-col gap-5 max-w-screen-2xl mx-auto">
    <!-- Header Card with Controls -->
    <app-card>
      <div class="flex flex-wrap justify-between items-center gap-6">
        <!-- Title with indicator -->
        <div class="flex items-center gap-2">
          <!-- <app-text variant="titleLg/semibold">{{ name }}</app-text> -->
          <app-text variant="titleLg/semibold">Historical ETFs</app-text>
        </div>

        <!-- Toolbar controls -->
        <div class="flex gap-6 flex-wrap">
          <!-- Date range with P-Calendar -->
          <div class="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
            <span class="flex items-center gap-2">
              <label for="fromDate">From</label>

              <p-calendar
                [(ngModel)]="fromDate"
                [showIcon]="true"
                [minDate]="minDateValue"
                [maxDate]="maxDateValue"
                [readonlyInput]="true"
                dateFormat="dd/mm/yy"
                inputId="fromDate"
                inputStyleClass="w-36 py-1"
              >
              </p-calendar>
            </span>

            <span class="flex items-center gap-2">
              <label for="toDate">To</label>

              <p-calendar
                [(ngModel)]="toDate"
                [showIcon]="true"
                [minDate]="minDateValue"
                [maxDate]="maxDateValue"
                [readonlyInput]="true"
                dateFormat="dd/mm/yy"
                inputId="toDate"
                inputStyleClass="w-36 py-1"
              >
              </p-calendar>
            </span>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-3 justify-end">
            <app-button (click)="setDateRange()">
              <span class="flex items-center gap-2">
                <i class="text-sm pi pi-filter"></i>
                <span> Apply </span>
              </span>
            </app-button>
          </div>
        </div>
      </div>
    </app-card>

    <!-- Main Content Card -->
    <app-card class="overflow-hidden">
      <!-- Header -->
      <div class="text-center mb-6">
        <app-text variant="title/semibold">
          {{ isZse ? "Zimbabwe" : "Victoria Falls" }} Stock Exchange Historical
          ETFs
        </app-text>
        <p class="text-sm text-gray-500">
          {{ fromDateFormatted }} - {{ toDateFormatted }}
        </p>
      </div>

      <!-- Loading state -->
      @if (loading) {
        <div class="flex justify-center items-center py-16">
          <p-progressSpinner
            [style]="{ width: '50px', height: '50px' }"
            animationDuration=".5s"
            strokeWidth="4"
          ></p-progressSpinner>
        </div>
      } @else {
        <div id="exportDiv">
          @if (
            historicalEtfsBySymbol &&
            Object.keys(historicalEtfsBySymbol).length > 0
          ) {

            <!-- Custom Tab View -->
            <div class="custom-tab-view">
                <!-- Tab Navigation -->
                <div class="tab-nav">
                    <div class="tab-nav-content">
                        @for (symbol of etfSymbols; track symbol; let i = $index) {
                        <button
                            class="tab-header"
                            [class.tab-header-active]="activeEtfTab === i"
                            (click)="setActiveTab(i)">
                            {{ symbol }}
                        </button>
                        }
                    </div>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                    @if (etfSymbols[activeEtfTab]) {
                                     <!-- Data Table for active ETF symbol -->
                     <p-table
                     #dt
                     [value]="historicalEtfsBySymbol[etfSymbols[activeEtfTab]]"
                     [scrollable]="true"
                     [tableStyle]="{ 'min-width': '100%' }"
                     styleClass="p-datatable-sm p-datatable-gridlines"
                     [resizableColumns]="true"
                     columnResizeMode="expand"
                     [paginator]="true"
                     [rows]="10"
                     [rowsPerPageOptions]="[10, 25, 50]"
                     [globalFilterFields]="[
                       'stats-date',
                       'price',
                       'volume',
                       'value',
                       'units',
                       'marketCapitalisation',
                     ]"
                     responsiveLayout="scroll"
                     [showCurrentPageReport]="true"
                     currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                     >
                     <!-- Table Caption -->
                     <ng-template pTemplate="caption">
                         <div
                         class="flex flex-col sm:flex-row justify-between gap-3"
                         >
                         <div class="flex gap-3 items-center">
                             <app-button
                             class="hidden sm:block"
                             size="sm"
                             variant="primary"
                             (click)="openDataVisualization(etfSymbols[activeEtfTab])"
                             >
                             <div class="flex gap-2 items-center">
                                 <i class="pi pi-chart-line text-sm"></i>
                                 <span>Visualize Data</span>
                             </div>
                             </app-button>

                             <app-button
                             size="sm"
                             variant="success"
                             (click)="exportToExcel(etfSymbols[activeEtfTab])"
                             >
                             <span class="flex items-center gap-2">
                                 <i class="text-sm pi pi-file-excel"></i>
                                 <span> Export {{ etfSymbols[activeEtfTab] }} </span>
                             </span>
                             </app-button>
                        </div>

                        <p-iconfield>
                          <p-inputicon styleClass="pi pi-search" />

                          <input
                            pSize="small"
                            pInputText
                            type="text"
                            (input)="
                              dt.filterGlobal(
                                $any($event.target).value,
                                'contains'
                              )
                            "
                            placeholder="Search by date..."
                            class="w-full sm:w-auto"
                          />
                        </p-iconfield>
                      </div>
                    </ng-template>

                    <!-- Table Header -->
                    <ng-template pTemplate="header">
                      <tr
                        class="text-xs font-semibold text-gray-600 uppercase text-nowrap"
                      >
                        <th
                          pFrozenColumn
                          alignFrozen="left"
                          pSortableColumn="stats-date"
                          class="min-w-[120px] bg-gray-100 text-gray-600 font-semibold"
                        >
                          <div class="flex items-center gap-2">
                            <span>Date</span>
                            <app-custom-sort-icon
                              field="stats-date"
                              [table]="dt"
                            ></app-custom-sort-icon>
                          </div>
                        </th>

                        <th
                          pSortableColumn="price"
                          class="min-w-[100px] text-gray-600 font-semibold"
                        >
                          <div class="flex items-center gap-2">
                            <span>Price</span>
                            <app-custom-sort-icon
                              field="price"
                              [table]="dt"
                            ></app-custom-sort-icon>
                          </div>
                        </th>

                        <th
                          pSortableColumn="volume"
                          class="min-w-[120px] text-gray-600 font-semibold"
                        >
                          <div class="flex items-center gap-2">
                            <span>Volume</span>
                            <app-custom-sort-icon
                              field="volume"
                              [table]="dt"
                            ></app-custom-sort-icon>
                          </div>
                        </th>

                        <th
                          pSortableColumn="value"
                          class="min-w-[120px] text-gray-600 font-semibold"
                        >
                          <div class="flex items-center gap-2">
                            <span>Value</span>
                            <app-custom-sort-icon
                              field="value"
                              [table]="dt"
                            ></app-custom-sort-icon>
                          </div>
                        </th>

                        <th
                          pSortableColumn="units"
                          class="min-w-[120px] text-gray-600 font-semibold"
                        >
                          <div class="flex items-center gap-2">
                            <span>Units</span>
                            <app-custom-sort-icon
                              field="units"
                              [table]="dt"
                            ></app-custom-sort-icon>
                          </div>
                        </th>

                        <th
                          pSortableColumn="marketCapitalisation"
                          class="min-w-[150px] text-gray-600 font-semibold"
                        >
                          <div class="flex items-center gap-2">
                            <span>Market Cap</span>
                            <app-custom-sort-icon
                              field="marketCapitalisation"
                              [table]="dt"
                            ></app-custom-sort-icon>
                          </div>
                        </th>
                      </tr>
                    </ng-template>

                    <!-- Table Body -->
                    <ng-template pTemplate="body" let-etf>
                      <tr class="hover:bg-gray-50 transition-colors">
                        <td
                          pFrozenColumn
                          alignFrozen="left"
                          class="font-medium text-gray-800 bg-gray-100"
                        >
                          {{ etf["stats-date"] | date: "dd/MM/yyyy" }}
                        </td>

                        <td class="text-right">
                          {{
                            etf.price === 0
                              ? "-"
                              : (etf.price | number: "1.2-2")
                          }}
                        </td>

                        <td class="text-right">
                          {{ etf.volume === 0 ? "-" : (etf.volume | number) }}
                        </td>

                        <td class="text-right">
                          {{ etf.value === 0 ? "-" : (etf.value | number) }}
                        </td>

                        <td class="text-right">
                          {{ etf.units === 0 ? "-" : (etf.units | number) }}
                        </td>

                        <td class="text-right">
                          {{
                            etf.marketCapitalisation === 0
                              ? "-"
                              : (etf.marketCapitalisation | number)
                          }}
                        </td>
                      </tr>
                    </ng-template>

                    <!-- Empty Message -->
                    <ng-template pTemplate="emptymessage">
                      <tr>
                        <td colspan="6">
                          <div class="flex flex-col items-center py-10">
                            <i
                              class="pi pi-info-circle text-4xl text-gray-300 mb-4"
                            ></i>
                            <p class="text-base text-gray-500 mb-2">
                              No data available
                            </p>
                             <p class="text-sm text-gray-400">
                                 No historical data for {{ etfSymbols[activeEtfTab] }} in the
                                 selected date range
                             </p>
                             </div>
                         </td>
                         </tr>
                     </ng-template>
                     </p-table>
                     }
                 </div>
             </div>
          } @else {
            <!-- Empty state when no data -->
            <div class="flex flex-col items-center justify-center py-16 px-4">
              <div class="bg-gray-50 rounded-full p-6 mb-5">
                <i class="pi pi-database text-4xl text-gray-300"></i>
              </div>
              <h3 class="text-base font-semibold text-gray-700 mb-2">
                No ETF Data Available
              </h3>
              <p class="text-base text-gray-500 text-center mb-6">
                There are no historical ETF values for the selected period.
              </p>
            </div>
          }
        </div>
      }
    </app-card>

    <!-- Data Visualization Dialog -->
    <p-dialog
      [(visible)]="showVisualizationDialog"
      [style]="{ width: '90%' }"
      [modal]="true"
      [draggable]="false"
      [resizable]="false"
      [header]="visualizationDialogTitle"
      styleClass="visualization-dialog"
      [maximizable]="true"
    >
      <div class="chart-container">
        <p-chart
          type="line"
          [data]="chartData"
          [options]="chartOptions"
        ></p-chart>
      </div>

      <ng-template pTemplate="footer">
        <div class="flex justify-end gap-3">
          <p-button
            label="Close"
            icon="pi pi-times"
            (click)="showVisualizationDialog = false"
            styleClass="p-button-outlined"
          ></p-button>
        </div>
      </ng-template>
    </p-dialog>
  </div>

  <!-- Toast for notifications -->
  <p-toast position="top-right"></p-toast>

  <!-- Confirmation dialog for actions -->
  <p-confirmDialog
    header="Confirmation"
    icon="pi pi-exclamation-triangle"
    [style]="{ width: '450px' }"
    [position]="'top'"
    acceptButtonStyleClass="p-button-primary"
    rejectButtonStyleClass="p-button-outlined"
  ></p-confirmDialog>
