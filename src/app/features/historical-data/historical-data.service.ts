import { Exchange } from "@/app/core/enums/exchange.enum";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from '@angular/core';
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
@Injectable({
  providedIn: 'root'
})
export class HistoricalDataService {

  private apiServerUrl = environment.apiBaseUrl;

  constructor(private http: HttpClient) {}

  /**
   * Regenerate ATS data for a specific date and exchange
   * @param date - Date in string format (YYYY-MM-DD)
   * @param exchange - Exchange enum (VFEX or ZSE)
   * @returns Observable with API response
   */
  public regenerateATSData(date: string, exchange: Exchange): Observable<any> {
    const params = new HttpParams()
      .set("date", date)
      .set("exchange", exchange);

    return this.http.get<any>(
      `${this.apiServerUrl}/ats-products/regenerate-ats-data`,
      { params }
    );
  }
}
