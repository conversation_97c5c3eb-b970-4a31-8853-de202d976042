<div class="flex items-center justify-between gap-3">
  <app-button-group>
    <app-button
      (click)="setExchange(Exchange.VFEX)"
      [variant]="exchange === Exchange.VFEX ? 'primary' : 'outline-primary'"
    >
      VFEX
    </app-button>

    <app-button
      (click)="setExchange(Exchange.ZSE)"
      [variant]="exchange === Exchange.ZSE ? 'primary' : 'outline-primary'"
      >ZSE</app-button
    >
  </app-button-group>

  @if (exchange === Exchange.VFEX) {
    <app-text variant="title/semibold" className="text-primary">
      {{ exchange }} Historical Data
    </app-text>
  } @else {
    <app-text variant="title/semibold" className="text-primary">
      {{ exchange }} Historical Data
    </app-text>
  }
</div>

<div class="flex flex-col gap-6 mt-6">
  @if (exchange === Exchange.VFEX) {
    <div
      class="grid grid-cols-1 gap-3 lg:gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
    >
      @for (product of vfexHistoricalData; track product) {
        <!-- <a [routerLink]="[product.link]" [queryParams]="{ exchange: exchange }"> -->
        <a [routerLink]="[product.link]" [queryParams]="{ exchange: exchange }">
          <app-card
            className="hover:bg-primary transition-transform duration-300 transform group rounded-xl"
          >
            <app-text
              variant="regular/semibold"
              className="text-primary group-hover:text-background transition-transform duration-300 transform"
            >
              {{ product.title }}
            </app-text>
          </app-card>
        </a>
      }
    </div>
  } @else {
    <div
      class="grid grid-cols-1 gap-3 lg:gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
    >
      @for (product of zseHistoricalData; track product) {
        <a [routerLink]="[product.link]" [queryParams]="{ exchange: exchange }">
          <app-card
            className="hover:bg-primary transition-transform duration-300 transform group rounded-xl"
          >
            <app-text
              variant="regular/semibold"
              className="text-primary group-hover:text-background transition-transform duration-300 transform"
            >
              {{ product.title }}
            </app-text>
          </app-card>
        </a>
      }
    </div>
  }
</div>


