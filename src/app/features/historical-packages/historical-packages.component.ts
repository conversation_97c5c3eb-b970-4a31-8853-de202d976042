// features/historical-packages/historical-packages.component.ts
import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/app/core/interfaces/common.interface";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { CurrencyPipe, DatePipe } from "@angular/common";
import { Component, inject, OnInit, signal } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { SpinnerComponent } from "../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import { AddHistoricalPackageComponent } from "./components/add-historical-package/add-historical-package.component";
import { EditHistoricalPackageComponent } from "./components/edit-historical-package/edit-historical-package.component";
import { ViewHistoricalPackageComponent } from "./components/view-historical-package/view-historical-package.component";
import { HistoricalPackage } from "./historical-packages.model";
import { HistoricalPackagesService } from "./historical-packages.service";

@Component({
  selector: "app-historical-packages",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ModalComponent,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    SpinnerComponent,
    AddHistoricalPackageComponent,
    EditHistoricalPackageComponent,
    ViewHistoricalPackageComponent,
    ButtonPermissionDirective,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    ButtonModule,
    CurrencyPipe,
    DatePipe,
  ],
  providers: [DatePipe],
  templateUrl: "./historical-packages.component.html",
})
export class HistoricalPackagesComponent implements OnInit {
  private historicalPackagesService = inject(HistoricalPackagesService);
  private readonly datePipe = inject(DatePipe);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;

  historicalPackages = signal<HistoricalPackage[]>([]);
  filteredHistoricalPackages = signal<HistoricalPackage[]>([]);
  selectedHistoricalPackage: HistoricalPackage | null = null;
  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  // Flag to indicate if a historical package already exists
  hasExistingPackage = false;

  get exportData(): any[] {
    return this.filteredHistoricalPackages().map((pkg) => ({
      "Package Name": pkg.name,
      Category: pkg.productPackageCategoryName,
      "Monthly Price": pkg.price,
      Recommended: pkg.isRecommended ? "Yes" : "No",
      Status: pkg.status,
      "Expiry Period (Days)": pkg.expiryPeriod,
      "Number of Historical Products": pkg.addons?.length || 0,
      "Created Date": this.datePipe.transform(
        pkg.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": pkg.modifiedDate
        ? this.datePipe.transform(pkg.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": pkg.createdUserName ?? "-",
      "Modified By": pkg.modifiedUserName ?? "-",
    }));
  }

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  isDataLoading = false;

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadHistoricalPackages();
      this.checkExistingPackage();
    });
  }

  loadHistoricalPackages(): void {
    this.isDataLoading = true;
    this.historicalPackagesService.getHistoricalPackages().subscribe({
      next: (response) => {
        this.historicalPackages.set(response.data || []);

        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);
        this.isDataLoading = false;

        // Check if there are any historical packages
        this.hasExistingPackage = this.historicalPackages().length > 0;
      },
      error: (error) => {
        handleError(error, "Failed to load historical packages");
        this.isDataLoading = false;
      },
    });
  }

  checkExistingPackage(): void {
    this.historicalPackagesService.checkHistoricalPackageExists().subscribe({
      next: (response) => {
        this.hasExistingPackage = response.exists;
      },
      error: (error) => {
        handleError(error, "Failed to check existing historical package");
      },
    });
  }

  toggleModal(
    mode: ModalKey,
    historicalPackage?: HistoricalPackage | null,
  ): void {
    // For 'addItem', check if a historical package already exists
    if (mode === "addItem" && this.hasExistingPackage) {
      showToast({
        message: "Only one Historical Package can exist at a time.",
        type: "warning",
      });
      return;
    }

    this.selectedHistoricalPackage = historicalPackage || null;
    this.modals[mode] = !this.modals[mode];
  }

  toggleRow(pkg: HistoricalPackage): void {
    if (this.expandedRows[pkg.id]) {
      delete this.expandedRows[pkg.id];
    } else {
      this.expandedRows[pkg.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.historicalPackages();

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (pkg) => pkg?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (pkg) =>
          pkg.name.toLowerCase().includes(term) ||
          pkg.productPackageCategoryName.toLowerCase().includes(term) ||
          pkg.price.toString().includes(term),
      );
    }

    this.filteredHistoricalPackages.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }
}
