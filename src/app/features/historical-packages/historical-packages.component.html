<!-- features/historical-packages/historical-packages.component.html -->
<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter dropdown -->
      <div class="mr-3 flex items-center">
        <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
        <div class="relative flex items-center">
          <i class="pi pi-filter absolute left-2 text-gray-500"></i>
          <select
            [ngModel]="statusFilter"
            (ngModelChange)="updateStatusFilter($event)"
            class="pl-8 pr-4 py-2 border rounded-lg text-sm"
            >
            <option [value]="null">All Statuses</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
        </div>
      </div>
    </ng-template>

    <ng-template #end>
      <div class="flex gap-2 items-center">
        <app-button
          size="sm"
          class="!gap-2"
          variant="secondary"
          (click)="
            reportExportService.exportToExcel(
              exportData,
              'historical-package.xlsx'
            )
          "
          >
          <i class="text-sm pi pi-upload"></i>
          Export
        </app-button>

        <ng-container *appButtonPermission="'create'">
          <app-button
            size="sm"
            (click)="toggleModal('addItem')"
            [disabled]="hasExistingPackage"
            [title]="
              hasExistingPackage
                ? 'Only one Historical Package can exist at a time'
                : 'Create Historical Package'
            "
            >
            <span>Create</span>
          </app-button>
        </ng-container>
      </div>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isDataLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading historical package...</span>
      </div>
    </div>
  } @else if (historicalPackages().length === 0) {
    <!-- Empty State -->
    <div class="p-8 text-center">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl text-gray-400 pi pi-inbox"></i>
        <span class="text-gray-600">No historical package found</span>
        <app-button
          *appButtonPermission="'create'"
          (click)="toggleModal('addItem')"
          [disabled]="hasExistingPackage"
          >
          Create Historical Package
        </app-button>
      </div>
    </div>
  } @else {
    <!-- Historical Package Table -->
    <p-table
      [value]="filteredHistoricalPackages()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
      >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold"
            >Manage Historical Package</app-text
            >
            <div class="flex gap-2">
              <p-iconfield>
                <p-inputicon styleClass="pi pi-search" />
                <input
                  pSize="small"
                  pInputText
                  type="text"
                  [(ngModel)]="searchTerm"
                  (ngModelChange)="onSearch($event)"
                  placeholder="Search historical package..."
                  />
              </p-iconfield>
            </div>
          </div>
        </ng-template>

        <!-- Table Header -->
        <ng-template pTemplate="header">
          <tr class="bg-gray-50 text-nowrap">
            <th style="width: 4rem"></th>
            <th pSortableColumn="createdDate">
              <div class="flex items-center gap-2">
                Created Date
                <p-sortIcon field="createdDate" />
              </div>
            </th>
            <th pSortableColumn="name">
              <div class="flex items-center justify-between gap-2">
                Name
                <p-sortIcon field="name" />
                <p-columnFilter
                  type="text"
                  field="name"
                  display="menu"
                  class="ml-auto"
                  />
              </div>
            </th>

            <th pSortableColumn="productPackageCategoryName">
              <div class="flex items-center justify-between gap-2">
                Category
                <p-sortIcon field="productPackageCategoryName" />
                <p-columnFilter
                  type="text"
                  field="productPackageCategoryName"
                  display="menu"
                  class="ml-auto"
                  />
              </div>
            </th>

            <!-- <th pSortableColumn="price">
            <div class="flex items-center justify-between gap-2">
              Monthly Price
              <p-sortIcon field="price" />
              <p-columnFilter
                type="numeric"
                field="price"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>

          <th pSortableColumn="isRecommended">
            <div class="flex items-center justify-between gap-2">
              Recommended
              <p-sortIcon field="isRecommended" />
              <p-columnFilter
                type="boolean"
                field="isRecommended"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th> -->

          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>

          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-package let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(package)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ package.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ package.name }}</td>
          <td>{{ package.productPackageCategoryName }}</td>
          <!-- <td>{{ package.price | currency: package.currencyCode || "USD" }}</td>
          <td>{{ package.isRecommended ? "Yes" : "No" }}</td> -->
          <td>{{ package.status }}</td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button
                  size="icon"
                  (click)="toggleModal('viewItem', package)"
                  >
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'create'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', package)"
                  >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-package>
        <tr>
          <td colspan="8">
            <div class="p-4 bg-gray-50 rounded-md">
              <div
                class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
                >
                <!-- Basic Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">Name</h5>
                  <p class="text-gray-800">{{ package.name }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Category
                  </h5>
                  <p class="text-gray-800">
                    {{ package.productPackageCategoryName }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">{{ package.status }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Price
                  </h5>
                  <p class="text-gray-800">
                    {{
                    package.price | currency: package.currencyCode || "USD"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Expiry Period
                  </h5>
                  <p class="text-gray-800">{{ package.expiryPeriod }} days</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Recommended
                  </h5>
                  <p class="text-gray-800">
                    {{ package.isRecommended ? "Yes" : "No" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Period
                  </h5>
                  <p class="text-gray-800">
                    {{ package.period.length }} {{ package.period.name }}(s)
                  </p>
                </div>

                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ package.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                    package.modifiedDate
                    ? (package.modifiedDate | date: "yyyy-MM-dd HH:mm:ss")
                    : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ package.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ package.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Description if available -->
              @if (package.description) {
                <div class="mt-4">
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Description
                  </h5>
                  <p class="text-gray-800 whitespace-pre-line">
                    {{ package.description }}
                  </p>
                </div>
              }

              <!-- Historical Products -->
              @if (package.addons?.length) {
                <div class="mt-4">
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Historical Products
                  </h5>
                  <div
                    class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2"
                    >
                    @for (product of package.addons; track product) {
                      <div
                        class="p-2 border rounded bg-white"
                        >
                        <div class="font-medium">{{ product.name }}</div>
                        <div class="text-sm text-gray-600">
                          {{ product.exchangeName }}
                        </div>
                        <div class="text-sm">
                          {{
                          product.price | currency: product.currencyCode || "USD"
                          }}
                        </div>
                      </div>
                    }
                  </div>
                </div>
              }

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  *appButtonPermission="'create'"
                  (click)="toggleModal('editItem', package)"
                  >
                  Edit
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- Add Historical Package Modal -->
<app-modal
  [isVisible]="modals.addItem"
  size="xl"
  [config]="{ title: 'Add Historical Package' }"
  >
  <app-add-historical-package
    (modalEvent)="toggleModal($event.mode, $event.historicalPackage)"
    (loadHistoricalPackagesEvent)="loadHistoricalPackages()"
    >
  </app-add-historical-package>
</app-modal>

<!-- Edit Historical Package Modal -->
<app-modal
  size="xl"
  [isVisible]="modals.editItem"
  [config]="{ title: 'Edit Historical Package' }"
  >
  <app-edit-historical-package
    [packageToEdit]="selectedHistoricalPackage!"
    (modalEvent)="toggleModal($event.mode, $event.historicalPackage)"
    (loadHistoricalPackagesEvent)="loadHistoricalPackages()"
    >
  </app-edit-historical-package>
</app-modal>

<!-- View Historical Package Modal -->
<app-modal
  [isVisible]="modals.viewItem"
  [config]="{ title: 'View Historical Package' }"
  >
  <app-view-historical-package
    [package]="selectedHistoricalPackage"
    (modalEvent)="toggleModal($event.mode, $event.historicalPackage)"
    >
  </app-view-historical-package>
</app-modal>

<p-toast />
