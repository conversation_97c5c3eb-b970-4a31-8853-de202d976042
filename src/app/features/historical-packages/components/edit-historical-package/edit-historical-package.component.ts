// features/historical-packages/components/edit-historical-package/edit-historical-package.component.ts
import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/app/core/interfaces/common.interface";
import { handleError } from "@/app/core/utils/error-handler.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";

import { CurrencyPipe } from "@angular/common";
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  inject,
} from "@angular/core";
import {
  FormBuilder,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { Router } from "@angular/router";
import Swal from "sweetalert2";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { FormFieldComponent } from "../../../../shared/ui/form-field/form-field.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { CurrenciesService } from "../../../currency/currencies.service";
import { Currency } from "../../../currency/currency.model";
import { HistoricalProductsService } from "../../../historical-products/historical-products.service";
import { PackageCategoriesService } from "../../../package-categories/package-categories.service";
import { PeriodsService } from "../../../periods/periods.service";
import {
  HistoricalPackage,
  UpdateHistoricalPackageRequest,
} from "../../historical-packages.model";
import { HistoricalPackagesService } from "../../historical-packages.service";

interface ModalEvent {
  mode: ModalKey;
  historicalPackage?: HistoricalPackage | null;
}

@Component({
  selector: "app-edit-historical-package",
  standalone: true,
  imports: [
    FormsModule,
    TextComponent,
    ButtonComponent,
    ReactiveFormsModule,
    FormFieldComponent,
    CurrencyPipe,
  ],
  templateUrl: "./edit-historical-package.component.html",
})
export class EditHistoricalPackageComponent implements OnInit, OnChanges {
  @Input() packageToEdit: HistoricalPackage | null = null;
  @Output() modalEvent = new EventEmitter<ModalEvent>();
  @Output() loadHistoricalPackagesEvent = new EventEmitter<void>();

  packageCategories: any[] = [];
  periods: any[] = [];
  historicalProducts: any[] = [];
  currencies: Currency[] = [];
  selectedAddons: any[] = [];

  isLoading: boolean = false;
  isPeriodsLoading: boolean = false;
  isPackageCategoriesLoading: boolean = false;
  isHistoricalProductsLoading: boolean = false;
  isCurrenciesLoading: boolean = false;

  private fb = inject(FormBuilder);

  form = this.fb.group({
    id: [""],
    name: ["", [Validators.required, Validators.minLength(3)]],
    description: [""],
    price: [0, [Validators.required, Validators.min(0)]],
    expiryPeriod: [30, [Validators.required, Validators.min(1)]],
    isRecommended: [false],
    productPackageCategoryId: ["", [Validators.required]],
    periodId: ["", [Validators.required]],
    status: ["ACTIVE", [Validators.required]],
  });

  constructor(
    private router: Router,
    private packageCategoriesService: PackageCategoriesService,
    private historicalProductsService: HistoricalProductsService,
    private periodsService: PeriodsService,
    private currenciesService: CurrenciesService,
    private historicalPackagesService: HistoricalPackagesService,
  ) {}

  ngOnInit(): void {
    this.getPackageCategories();
    this.getHistoricalProducts();
    this.getPeriods();
    this.getCurrencies();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes["packageToEdit"] && this.packageToEdit) {
      // Wait for all data to be loaded before populating form
      Promise.all([
        this.loadPackageCategories(),
        this.loadHistoricalProducts(),
        this.loadPeriods(),
        this.loadCurrencies(),
      ]).then(() => {
        this.populateForm();
      });
    }
  }

  toggleModal(): void {
    this.modalEvent.emit({
      mode: "editItem",
      historicalPackage: this.packageToEdit,
    });
  }

  get packageCategoriesOptions() {
    return this.packageCategories.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  }

  get periodsOptions() {
    return this.periods.map((item) => ({
      label: `${item.length} ${item.name}(s)`,
      value: item.id,
    }));
  }

  get currencyOptions() {
    return this.currencies.map((currency) => ({
      label: currency.name,
      value: currency.id,
    }));
  }

  private populateForm(): void {
    if (!this.packageToEdit) return;

    // Reset the form first to clear any existing values
    this.form.reset();

    // Patch the form with values from the package
    this.form.patchValue({
      id: this.packageToEdit.id,
      name: this.packageToEdit.name,
      description: this.packageToEdit.description || "",
      price: this.packageToEdit.price,
      expiryPeriod: this.packageToEdit.expiryPeriod,
      isRecommended: this.packageToEdit.isRecommended,
      productPackageCategoryId: this.packageToEdit.productPackageCategoryId,
      status: this.packageToEdit.status,
      periodId: this.packageToEdit.periods?.[0]?.id || "",
    });

    // Clear and set selected addons (historical products)
    this.selectedAddons = [];

    // Make sure addons array is defined before working with it
    if (this.packageToEdit.addons && this.packageToEdit.addons.length > 0) {
      // Create a new array to avoid reference issues
      this.selectedAddons = [...this.packageToEdit.addons];
    }
  }

  // Update the add/remove product methods to work with addons
  onAddAddon(addon: any) {
    if (!this.isAddonAdded(addon)) {
      this.selectedAddons.push(addon);
    }
  }

  onRemoveAddon(addon: any) {
    this.selectedAddons = this.selectedAddons.filter((a) => a.id !== addon.id);
  }

  isAddonAdded(addon: any): boolean {
    return this.selectedAddons.some((a) => a.id === addon.id);
  }

  // Update the form submission
  editHistoricalPackage() {
    if (!this.validateForm()) {
      return;
    }

    const formValues = this.form.getRawValue();

    const selectedPeriod = this.periods.find(
      (period) => period.id === formValues.periodId,
    );

    if (!selectedPeriod) {
      Swal.fire({
        icon: "error",
        title: "Invalid Selection",
        text: "Please select a valid period.",
      });
      return;
    }

    const periods = [
      {
        id: selectedPeriod.id,
        periodName: selectedPeriod.name,
        periodLength: selectedPeriod.length,
        discount: 0,
        price: 0,
        isDefault: true,
      },
    ];

    const payload = {
      ...formValues,
      addonIds: this.selectedAddons.map((addon) => addon.id),
      productCategoryType: ProductCategoryType.HISTORICAL,
      periods,
    };

    this.isLoading = true;
    this.historicalPackagesService
      .updateHistoricalPackage(payload as UpdateHistoricalPackageRequest)
      .subscribe({
        next: () => {
          this.loadHistoricalPackagesEvent.emit();
          this.toggleModal();
          this.isLoading = false;

          Swal.fire({
            icon: "success",
            title: "Success",
            text: "Historical package has been updated successfully.",
          });
        },
        error: (error) => {
          handleError(error);
          this.isLoading = false;
        },
      });
  }

  // Update the validation method
  private validateForm(): boolean {
    if (this.form.invalid) {
      markFormGroupTouched(this.form);
      Swal.fire({
        icon: "error",
        title: "Invalid Form",
        text: "Please fix the form errors.",
      });
      return false;
    }

    if (!this.selectedAddons.length) {
      Swal.fire({
        icon: "error",
        title: "Invalid Form",
        text: "Please add at least one historical product.",
      });
      return false;
    }

    return true;
  }

  // Helper methods to load related data
  private loadPackageCategories(): Promise<void> {
    return new Promise<void>((resolve) => {
      if (this.packageCategories.length > 0) {
        resolve();
        return;
      }

      this.isPackageCategoriesLoading = true;
      this.packageCategoriesService.getPackageCategories().subscribe({
        next: (response) => {
          this.packageCategories = response.data;
          this.isPackageCategoriesLoading = false;
          resolve();
        },
        error: (error) => {
          handleError(error, "Failed to load package categories");
          this.isPackageCategoriesLoading = false;
          resolve();
        },
      });
    });
  }

  private loadHistoricalProducts(): Promise<void> {
    return new Promise<void>((resolve) => {
      if (this.historicalProducts.length > 0) {
        resolve();
        return;
      }

      this.isHistoricalProductsLoading = true;
      this.historicalProductsService.getActiveHistoricalProducts().subscribe({
        next: (response) => {
          this.historicalProducts = response.data;
          this.isHistoricalProductsLoading = false;
          resolve();
        },
        error: (error) => {
          handleError(error, "Failed to load historical products");
          this.isHistoricalProductsLoading = false;
          resolve();
        },
      });
    });
  }

  private loadPeriods(): Promise<void> {
    return new Promise<void>((resolve) => {
      if (this.periods.length > 0) {
        resolve();
        return;
      }

      this.isPeriodsLoading = true;
      this.periodsService.getPeriods().subscribe({
        next: (response) => {
          this.periods = response.data;
          this.isPeriodsLoading = false;
          resolve();
        },
        error: (error) => {
          handleError(error, "Failed to load periods");
          this.isPeriodsLoading = false;
          resolve();
        },
      });
    });
  }

  private loadCurrencies(): Promise<void> {
    return new Promise<void>((resolve) => {
      if (this.currencies.length > 0) {
        resolve();
        return;
      }

      this.isCurrenciesLoading = true;
      this.currenciesService.getCurrencies().subscribe({
        next: (response) => {
          this.currencies = response.data;
          this.isCurrenciesLoading = false;
          resolve();
        },
        error: (error) => {
          handleError(error, "Failed to load currencies");
          this.isCurrenciesLoading = false;
          resolve();
        },
      });
    });
  }

  getCurrencies() {
    if (this.currencies.length > 0) return;

    this.isCurrenciesLoading = true;
    this.currenciesService.getCurrencies().subscribe({
      next: (response) => {
        this.currencies = response.data;
        this.isCurrenciesLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load currencies");
        this.isCurrenciesLoading = false;
      },
    });
  }

  getPackageCategories() {
    if (this.packageCategories.length > 0) return;

    this.isPackageCategoriesLoading = true;
    this.packageCategoriesService.getPackageCategories().subscribe({
      next: (response) => {
        this.packageCategories = response.data;
        this.isPackageCategoriesLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load package categories");
        this.isPackageCategoriesLoading = false;
      },
    });
  }

  getHistoricalProducts() {
    if (this.historicalProducts.length > 0) return;

    this.isHistoricalProductsLoading = true;
    this.historicalProductsService.getActiveHistoricalProducts().subscribe({
      next: (response) => {
        this.historicalProducts = response.data;
        this.isHistoricalProductsLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load historical products");
        this.isHistoricalProductsLoading = false;
      },
    });
  }

  getPeriods() {
    if (this.periods.length > 0) return;

    this.isPeriodsLoading = true;
    this.periodsService.getPeriods().subscribe({
      next: (response) => {
        this.periods = response.data;
        this.isPeriodsLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load periods");
        this.isPeriodsLoading = false;
      },
    });
  }

  onAddProduct(product: any) {
    if (!this.isProductAdded(product)) {
      this.selectedAddons.push(product);
    }
  }

  onRemoveProduct(product: any) {
    this.selectedAddons = this.selectedAddons.filter(
      (p) => p.id !== product.id,
    );
  }

  isProductAdded(product: any): boolean {
    return this.selectedAddons.some((p) => p.id === product.id);
  }
}
