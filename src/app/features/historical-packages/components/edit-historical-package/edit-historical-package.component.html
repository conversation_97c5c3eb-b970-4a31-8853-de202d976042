<!-- features/historical-packages/components/edit-historical-package/edit-historical-package.component.html -->
<form
  [formGroup]="form"
  (ngSubmit)="editHistoricalPackage()"
  class="flex flex-col gap-6"
>
  <div class="flex flex-col gap-2">
    <app-text variant="title/semibold">General Information</app-text>
    <div class="grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4">
      <app-form-field
        label="Package Category"
        type="select"
        [options]="packageCategoriesOptions"
        [control]="form.controls.productPackageCategoryId"
        [required]="true"
      />

      <app-form-field
        label="Package Name"
        type="text"
        placeholder="Package Name"
        [control]="form.controls.name"
        [required]="true"
      />

      <!-- <app-form-field
        label="Set as Recommended"
        type="select"
        [options]="[
          { label: 'No', value: false },
          { label: 'Yes', value: true },
        ]"
        [control]="form.controls.isRecommended"
      /> -->

      <!-- <app-form-field
        label="Set the Price for a month"
        type="number"
        step="0.01"
        [control]="form.controls.price"
        [required]="true"
        min="0"
      /> -->

      <app-form-field
        label="Expiry Period (Days)"
        type="number"
        [control]="form.controls.expiryPeriod"
        [required]="true"
        min="1"
      />

      <app-form-field
        label="Period"
        type="select"
        [options]="periodsOptions"
        [control]="form.controls.periodId"
        [required]="true"
      />

      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
        [control]="form.controls.status"
        [required]="true"
      />

      <app-form-field
        label="Description"
        type="textarea"
        [control]="form.controls.description"
      />
    </div>
  </div>

  <hr />

  <div class="flex flex-col gap-2">
    <app-text variant="title/semibold">Historical Products (Addons)</app-text>

    @if (isHistoricalProductsLoading) {
      <div class="flex items-center justify-center p-8">
        <div class="flex flex-col items-center gap-4">
          <i class="text-xl pi pi-spin pi-spinner text-primary"></i>
          <span class="text-gray-600">Loading historical products...</span>
        </div>
      </div>
    } @else {
      <div class="modern-scrollbar">
        <table class="table w-full">
          <thead>
            <tr>
              <th class="text-start">Product Name</th>
              <th class="text-start">Exchange</th>
              <th class="text-start">Price</th>
              <th class="text-start">Actions</th>
            </tr>
          </thead>

          <tbody>
            <!-- Replace isProductAdded, onAddProduct and onRemoveProduct with their addon equivalents -->
            @for (addon of historicalProducts; track addon.id) {
              <tr>
                <td>{{ addon.name }}</td>
                <td>{{ addon.exchangeName }}</td>
                <td>{{ addon.price | currency: addon.currencyCode }}</td>
                <td>
                  <app-button
                    type="button"
                    size="sm"
                    [variant]="isAddonAdded(addon) ? 'danger' : 'primary'"
                    (click)="
                      isAddonAdded(addon)
                        ? onRemoveAddon(addon)
                        : onAddAddon(addon)
                    "
                  >
                    {{ isAddonAdded(addon) ? "Remove" : "Add" }}
                  </app-button>
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>

      <!-- Update selectedAddons to selectedAddons -->
      @if (selectedAddons.length > 0) {
        <div class="mt-4">
          <app-text variant="small/semibold"
            >Selected Products: {{ selectedAddons.length }}</app-text
          >
          <div class="flex flex-wrap gap-2 mt-2">
            @for (addon of selectedAddons; track addon.id) {
              <div
                class="bg-gray-100 rounded-md px-3 py-1 flex items-center gap-2"
              >
                <span>{{ addon.name }}</span>
                <button
                  type="button"
                  class="text-red-500 hover:text-red-700"
                  (click)="onRemoveAddon(addon)"
                >
                  <i class="pi pi-times"></i>
                </button>
              </div>
            }
          </div>
        </div>
      }
    }
  </div>

  <hr />

  <div class="flex items-center justify-end gap-3 mt-4">
    <app-button variant="outline" (click)="toggleModal()" type="button">
      Cancel
    </app-button>
    <app-button
      type="submit"
      [loading]="isLoading"
      [disabled]="form.invalid || selectedAddons.length === 0"
    >
      Submit
    </app-button>
  </div>
</form>
