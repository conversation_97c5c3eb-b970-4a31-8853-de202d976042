// features/historical-packages/components/view-historical-package/view-historical-package.component.ts
import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { ModalKey } from "@/app/core/interfaces/common.interface";
import { CurrencyPipe, NgClass } from "@angular/common";
import { Component, EventEmitter, Input, Output } from "@angular/core";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { HistoricalPackage } from "../../historical-packages.model";

@Component({
  selector: "app-view-historical-package",
  templateUrl: "./view-historical-package.component.html",
  standalone: true,
  imports: [
    NgClass,
    ButtonModule,
    DialogModule,
    ButtonComponent,
    <PERSON><PERSON><PERSON>cyPipe,
    ButtonPermissionDirective,
    FontAwesomeModule
],
})
export class ViewHistoricalPackageComponent {
  @Input() package: HistoricalPackage | null = null;
  @Output() modalEvent = new EventEmitter<{
    mode: ModalKey;
    historicalPackage?: HistoricalPackage | null;
  }>();

  closeModal(): void {
    this.modalEvent.emit({ mode: "viewItem" });
  }

  editPackage(): void {
    this.modalEvent.emit({ mode: "editItem", historicalPackage: this.package });
  }
}
