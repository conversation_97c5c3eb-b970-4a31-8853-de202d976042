<!-- features/historical-packages/components/view-historical-package/view-historical-package.component.html -->
<div class="p-4">
  <!-- Package Information Card -->
  <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
    <div class="flex justify-between items-start mb-4">
      <div>
        <h2 class="text-2xl font-semibold text-gray-800">
          {{ package?.name }}
        </h2>
        <p class="text-gray-600 mt-1">
          {{ package?.productPackageCategoryName }}
        </p>
      </div>
      <div class="flex items-center">
        <span
          class="px-3 py-1 rounded-full text-sm"
          [ngClass]="{
            'bg-green-100 text-green-800': package?.status === 'ACTIVE',
            'bg-red-100 text-red-800': package?.status === 'INACTIVE',
          }"
          >
          {{ package?.status }}
        </span>
        <!-- <span
        *ngIf="package?.isRecommended"
        class="ml-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
        >
        Recommended
      </span> -->
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-4">
    <div>
      <h3 class="text-sm font-medium text-gray-500">Category</h3>
      <p class="mt-1 text-gray-900">
        {{ package?.productPackageCategoryName }}
      </p>
    </div>
    <!-- <div>
    <h3 class="text-sm font-medium text-gray-500">Base Price</h3>
    <p class="mt-1 text-gray-900">
      {{ package?.price | currency: package?.currencyCode || "USD" }}
    </p>
  </div> -->
  <div>
    <h3 class="text-sm font-medium text-gray-500">Expiry Period</h3>
    <p class="mt-1 text-gray-900">{{ package?.expiryPeriod }} days</p>
  </div>
  <div>
    <h3 class="text-sm font-medium text-gray-500">Currency</h3>
    <p class="mt-1 text-gray-900">{{ package?.currencyCode || "USD" }}</p>
  </div>
  <div>
    <h3 class="text-sm font-medium text-gray-500">Period</h3>
    @if (package?.periods?.[0]) {
      <p class="mt-1 text-gray-900">
        {{ package?.periods?.[0]?.length }}
        {{ package?.periods?.[0]?.name }}(s)
      </p>
    }
  </div>
</div>

<!-- Description -->
@if (package?.description) {
  <div class="mb-6">
    <h3 class="text-sm font-medium text-gray-500 mb-2">Description</h3>
    <p class="text-gray-700 whitespace-pre-line">
      {{ package?.description }}
    </p>
  </div>
}
</div>

<div class="bg-white rounded-lg shadow-sm border p-6">
  <h3 class="text-lg font-medium text-gray-900 mb-4">
    Historical Products ({{ package?.addons?.length || 0 }})
  </h3>

  @if (package?.addons) {
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
      >
      @for (product of package?.addons; track product) {
        <div
          class="border rounded-lg p-4 hover:shadow-md transition-shadow"
          >
          <div class="flex items-start justify-between">
            <div>
              <h4 class="font-medium text-gray-800">{{ product.name }}</h4>
              <p class="text-sm text-gray-600">{{ product.exchangeName }}</p>
            </div>
            <span class="font-semibold text-gray-900">
              {{ product.price | currency: product.currencyCode || "USD" }}
            </span>
          </div>
          @if (product.linkTable) {
            <div
              class="mt-2 flex items-center text-sm text-gray-600"
              >
              <span class="mr-1">Link Table:</span>
              <span class="capitalize">{{
                product.linkTable?.replaceAll("_", " ")
              }}</span>
            </div>
          }
          @if (product.description) {
            <div class="mt-2 text-sm text-gray-700">
              {{ product.description }}
            </div>
          }
        </div>
      }
    </div>
  }

  @if (!package?.addons || package?.addons?.length === 0) {
    <p
      class="text-gray-500 text-sm"
      >
      No historical products included in this package.
    </p>
  }
</div>

<!-- Action Buttons -->
<div class="flex justify-end gap-3 mt-6">
  <app-button variant="outline" (click)="closeModal()">Close</app-button>
  <app-button
    variant="outline"
    *appButtonPermission="'create'"
    (click)="editPackage()"
    >
    <i class="pi pi-pencil mr-2"></i>
    Edit Package
  </app-button>
</div>
</div>
