// features/historical-packages/components/add-historical-package/add-historical-package.component.ts
import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/app/core/interfaces/common.interface";
import { handleError } from "@/app/core/utils/error-handler.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";

import { CurrencyPipe } from "@angular/common";
import { Component, EventEmitter, inject, OnInit, Output } from "@angular/core";
import {
  FormBuilder,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import Swal from "sweetalert2";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { FormFieldComponent } from "../../../../shared/ui/form-field/form-field.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { CurrenciesService } from "../../../currency/currencies.service";
import { Currency } from "../../../currency/currency.model";
import { HistoricalProductsService } from "../../../historical-products/historical-products.service";
import { PackageCategoriesService } from "../../../package-categories/package-categories.service";
import { PeriodsService } from "../../../periods/periods.service";
import {
  CreateHistoricalPackageRequest,
  HistoricalPackage,
} from "../../historical-packages.model";
import { HistoricalPackagesService } from "../../historical-packages.service";

interface ModalEvent {
  mode: ModalKey;
  historicalPackage?: HistoricalPackage | null;
}

@Component({
  selector: "app-add-historical-package",
  standalone: true,
  imports: [
    FormsModule,
    TextComponent,
    ButtonComponent,
    ReactiveFormsModule,
    FormFieldComponent,
    CurrencyPipe,
  ],
  templateUrl: "./add-historical-package.component.html",
})
export class AddHistoricalPackageComponent implements OnInit {
  @Output() modalEvent = new EventEmitter<ModalEvent>();
  @Output() loadHistoricalPackagesEvent = new EventEmitter<void>();

  packageCategories: any[] = [];
  periods: any[] = [];
  historicalProducts: any[] = [];
  currencies: Currency[] = [];
  selectedAddons: any[] = [];

  isLoading: boolean = false;
  isPeriodsLoading: boolean = false;
  isPackageCategoriesLoading: boolean = false;
  isHistoricalProductsLoading: boolean = false;
  isCurrenciesLoading: boolean = false;

  private fb = inject(FormBuilder);

  form = this.fb.group({
    name: ["", [Validators.required, Validators.minLength(3)]],
    description: [""],
    price: [0, [Validators.required, Validators.min(0)]],
    expiryPeriod: [30, [Validators.required, Validators.min(1)]],
    isRecommended: [false],
    productPackageCategoryId: ["", [Validators.required]],
    periodId: ["", [Validators.required]],
  });

  constructor(
    private packageCategoriesService: PackageCategoriesService,
    private historicalProductsService: HistoricalProductsService,
    private periodsService: PeriodsService,
    private currenciesService: CurrenciesService,
    private historicalPackagesService: HistoricalPackagesService,
  ) {}

  ngOnInit(): void {
    this.getPackageCategories();
    this.getHistoricalProducts();
    this.getPeriods();
    this.getCurrencies();
  }

  toggleModal(): void {
    this.modalEvent.emit({ mode: "addItem" });
  }

  get packageCategoriesOptions() {
    return this.packageCategories
      ?.filter(
        (item) => item.productCategoryType === ProductCategoryType.HISTORICAL,
      )
      ?.map((item) => ({
        label: item.name,
        value: item.id,
      }));
  }

  get periodsOptions() {
    return this.periods.map((item) => ({
      label: `${item.length} ${item.name}(s)`,
      value: item.id,
    }));
  }

  get currencyOptions() {
    return this.currencies.map((currency) => ({
      label: currency.name,
      value: currency.id,
    }));
  }

  getCurrencies() {
    this.isCurrenciesLoading = true;
    this.currenciesService.getCurrencies().subscribe({
      next: (response) => {
        this.currencies = response.data;
        this.isCurrenciesLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load currencies");
        this.isCurrenciesLoading = false;
      },
    });
  }

  getPackageCategories() {
    this.isPackageCategoriesLoading = true;
    this.packageCategoriesService.getPackageCategories().subscribe({
      next: (response) => {
        this.packageCategories = response.data;
        this.isPackageCategoriesLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load package categories");
        this.isPackageCategoriesLoading = false;
      },
    });
  }

  getHistoricalProducts() {
    this.isHistoricalProductsLoading = true;
    this.historicalProductsService.getActiveHistoricalProducts().subscribe({
      next: (response) => {
        this.historicalProducts = response.data;
        this.isHistoricalProductsLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load historical products");
        this.isHistoricalProductsLoading = false;
      },
    });
  }

  getPeriods() {
    this.isPeriodsLoading = true;
    this.periodsService.getPeriods().subscribe({
      next: (response) => {
        this.periods = response.data;
        this.isPeriodsLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load periods");
        this.isPeriodsLoading = false;
      },
    });
  }

  onAddProduct(product: any) {
    if (!this.isProductAdded(product)) {
      this.selectedAddons.push(product);
    }
  }

  onRemoveProduct(product: any) {
    this.selectedAddons = this.selectedAddons.filter(
      (p) => p.id !== product.id,
    );
  }

  isProductAdded(product: any): boolean {
    return this.selectedAddons.some((p) => p.id === product.id);
  }

  addHistoricalPackage() {
    if (!this.validateForm()) {
      return;
    }

    const formValues = this.form.getRawValue();

    const selectedPeriod = this.periods.find(
      (period) => period.id === formValues.periodId,
    );

    if (!selectedPeriod) {
      Swal.fire({
        icon: "error",
        title: "Invalid Selection",
        text: "Please select a valid period.",
      });
      return;
    }

    const periods = [
      {
        id: selectedPeriod.id,
        periodName: selectedPeriod.name,
        periodLength: selectedPeriod.length,
        discount: 0,
        price: 0,
        isDefault: true,
      },
    ];

    const payload = {
      ...formValues,
      addonIds: this.selectedAddons.map((addon) => addon.id),
      productCategoryType: ProductCategoryType.HISTORICAL,
      periods,
    };

    this.isLoading = true;
    this.historicalPackagesService
      .createHistoricalPackage(payload as CreateHistoricalPackageRequest)
      .subscribe({
        next: () => {
          this.form.reset();
          this.resetFormFields();
          this.loadHistoricalPackagesEvent.emit();
          this.toggleModal();
          this.isLoading = false;

          Swal.fire({
            icon: "success",
            title: "Success",
            text: "Historical package has been created successfully.",
          });
        },
        error: (error) => {
          handleError(error);
          this.isLoading = false;
        },
      });
  }

  // Update the methods for adding/removing products to use addons
  onAddAddon(addon: any) {
    if (!this.isAddonAdded(addon)) {
      this.selectedAddons.push(addon);
    }
    console.log(
      "🚀 ~ AddHistoricalPackageComponent ~ onAddAddon ~ this.selectedAddons:",
      this.selectedAddons,
    );
  }

  onRemoveAddon(addon: any) {
    this.selectedAddons = this.selectedAddons.filter((a) => a.id !== addon.id);
    console.log(
      "🚀 ~ AddHistoricalPackageComponent ~ onRemoveAddon ~ this.selectedAddons:",
      this.selectedAddons,
    );
    console.log(
      "🚀 ~ AddHistoricalPackageComponent ~ onRemoveAddon ~ this.selectedAddons:",
      this.selectedAddons,
    );
  }

  isAddonAdded(addon: any): boolean {
    return this.selectedAddons.some((a) => a.id === addon.id);
  }

  // Update validation method
  private validateForm(): boolean {
    if (this.form.invalid) {
      markFormGroupTouched(this.form);
      Swal.fire({
        icon: "error",
        title: "Invalid Form",
        text: "Please fix the form errors.",
      });
      return false;
    }

    if (!this.selectedAddons.length) {
      console.log(
        "🚀 ~ AddHistoricalPackageComponent ~ validateForm ~ this.selectedAddons:",
        this.selectedAddons,
      );
      Swal.fire({
        icon: "error",
        title: "Invalid Form",
        text: "Please add at least one historical product.",
      });
      return false;
    }

    return true;
  }

  private resetFormFields() {
    this.selectedAddons = [];
    this.selectedAddons = [];
    console.log(
      "🚀 ~ AddHistoricalPackageComponent ~ resetFormFields ~ this.selectedAddons:",
      this.selectedAddons,
    );
  }
}
