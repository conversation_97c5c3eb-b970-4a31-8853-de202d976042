// features/historical-packages/historical-packages.service.ts
import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable, map } from "rxjs";
import { environment } from "src/environments/environment.development";
import {
  CreateHistoricalPackageRequest,
  UpdateHistoricalPackageRequest,
} from "./historical-packages.model";

@Injectable({
  providedIn: "root",
})
export class HistoricalPackagesService {
  private apiServerUrl = environment.apiBaseUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get all historical packages
   * Uses the product-packages endpoint with a filter for historical type
   */
  public getHistoricalPackages(): Observable<any> {
    // Use the product-packages endpoint but filter for historical type
    return this.http
      .get<any>(`${this.apiServerUrl}/product-packages`, {
        params: new HttpParams().set("type", ProductCategoryType.HISTORICAL),
      })
      .pipe(
        map((response) => {
          // Ensure we return the correct format even if the API returns all packages
          const historicalPackages = response.data.filter(
            (item: any) =>
              item.productCategoryType === ProductCategoryType.HISTORICAL,
          );
          return { ...response, data: historicalPackages };
        }),
      );
  }

  /**
   * Get a specific historical package by id
   */
  public getHistoricalPackage(id: string): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/product-packages/${id}`);
  }

  /**
   * Create a new historical package
   * Adds the historical category type to the payload
   */
  public createHistoricalPackage(
    packageData: CreateHistoricalPackageRequest,
  ): Observable<any> {
    // Using the structure that matches the product-packages endpoint
    const payload = {
      name: packageData.name,
      description: packageData.description,
      price: packageData.price,
      expiryPeriod: packageData.expiryPeriod,
      isRecommended: packageData.isRecommended,
      productPackageCategoryId: packageData.productPackageCategoryId,
      productCategoryType: ProductCategoryType.HISTORICAL,
      // Empty arrays for standard product package fields
      products: [],
      periods: packageData.periods,
      // Convert addonIds to addon objects for the API
      addons: packageData.addonIds.map((id) => ({ id })),
    };

    return this.http.post<any>(
      `${this.apiServerUrl}/product-packages`,
      payload,
    );
  }

  /**
   * Update an existing historical package
   */
  public updateHistoricalPackage(
    packageData: UpdateHistoricalPackageRequest,
  ): Observable<any> {
    // Start with basic package properties
    const payload: any = {
      id: packageData.id,
      name: packageData.name,
      description: packageData.description,
      price: packageData.price,
      expiryPeriod: packageData.expiryPeriod,
      isRecommended: packageData.isRecommended,
      productPackageCategoryId: packageData.productPackageCategoryId,
      status: packageData.status,
      productCategoryType: ProductCategoryType.HISTORICAL,
      // Empty arrays for standard product package fields
      products: [],
      periods: packageData.periods,
    };

    // Add addons array if addonIds exist
    if (packageData.addonIds && packageData.addonIds.length > 0) {
      payload.addons = packageData.addonIds.map((id) => ({ id }));
    } else {
      payload.addons = [];
    }

    return this.http.put<any>(`${this.apiServerUrl}/product-packages`, payload);
  }

  /**
   * Check if a historical package already exists
   */
  public checkHistoricalPackageExists(): Observable<any> {
    return this.getHistoricalPackages().pipe(
      map((response) => ({
        exists: response.data && response.data.length > 0,
        package: response.data?.[0] || null,
      })),
    );
  }
}
