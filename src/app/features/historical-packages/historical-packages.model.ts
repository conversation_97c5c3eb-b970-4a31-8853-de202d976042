// features/historical-packages/historical-packages.model.ts
import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { Status } from "@/app/core/enums/status.enum";
import { BaseResponse } from "@/app/core/interfaces/common.interface";

// Aligning with the actual API response structure
export interface HistoricalPackage extends BaseResponse {
  name: string;
  description?: string;
  price: number;
  expiryPeriod: number;
  isRecommended: boolean;
  productPackageCategoryId: string;
  productPackageCategoryName: string;
  currencyId: string | null;
  currencyCode: string | null;
  productCategoryType: ProductCategoryType;
  // Note: According to the response, these are arrays - not separate objects
  products: any[]; // Empty array in the response
  addons: any[]; // We'll use this for historical products
  periods: any[]; // Empty array in the response
}

export interface CreateHistoricalPackageRequest {
  name: string;
  description?: string;
  price: number;
  expiryPeriod: number;
  isRecommended: boolean;
  productPackageCategoryId: string;
  // Based on your instruction that historical products are actually addons:
  addonIds: string[]; // Changed from historicalProductIds
  productCategoryType: ProductCategoryType.HISTORICAL;
  periods: any[];
}

export interface UpdateHistoricalPackageRequest {
  id: string;
  name?: string;
  description?: string;
  price?: number;
  expiryPeriod?: number;
  isRecommended?: boolean;
  productPackageCategoryId?: string;
  addonIds?: string[]; // Changed from historicalProductIds
  status?: Status;
  productCategoryType?: ProductCategoryType.HISTORICAL;
  periods: any[];
}
