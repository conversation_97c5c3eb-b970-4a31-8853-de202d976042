<div class="flex flex-col gap-3">
  <app-text variant="title/semibold"
    >{{ exchange }} Daily Trading Report</app-text
  >

  <div class="flex justify-between gap-3">
    <app-button-group>
      <app-button
        (click)="setStatus(Status.DRAFT)"
        size="sm"
        [variant]="status === Status.DRAFT ? 'success' : 'outline-success'"
      >
        DRAFTS
      </app-button>
      <app-button
        (click)="setStatus(Status.APPROVED)"
        size="sm"
        [variant]="status === Status.APPROVED ? 'success' : 'outline-success'"
      >
        APPROVED
      </app-button>
      <app-button
        (click)="setStatus(Status.PUBLISHED)"
        size="sm"
        [variant]="status === Status.PUBLISHED ? 'success' : 'outline-success'"
      >
        PUBLISHED
      </app-button>
    </app-button-group>

    <div class="flex items-center gap-3" style="width: fit-content">
      <span class="text-sm font-medium" style="font-size: small"> DATE </span>
      <input
        name="date"
        type="date"
        [(ngModel)]="date"
        (change)="getDataByDate()"
        [max]="maxDate"
        class="form-control"
        style="width: fit-content"
      />
    </div>
  </div>

  <div class="flex justify-between align-items-baseline">
    <div class="flex justify-between gap-3">
      <app-button-group>
        <app-button
          (click)="setMarket(Market.REG)"
          size="sm"
          [variant]="market === Market.REG ? 'warning' : 'outline-warning'"
        >
          EQUITIES
        </app-button>

        @if (isZse) {
          <app-button
            (click)="setMarket(Market.ETF)"
            size="sm"
            [variant]="market === Market.ETF ? 'warning' : 'outline-warning'"
          >
            ETFS
          </app-button>
        }
      </app-button-group>
    </div>

    @if (dtrScore?.missingComponents?.length == 0) {
      <div class="flex items-center justify-between gap-2">
        <div class="flex justify-end gap-2 align-items-center">
          <app-button size="sm" variant="success" (click)="exportData()">
            Export Data
          </app-button>
        </div>

        <div class="flex justify-end gap-2">
          <ng-container *appButtonPermission="'edit'">
            @switch (status) {
              @case (Status.DRAFT) {
                <app-button
                  size="sm"
                  variant="warning"
                  (click)="approveDtr(Status.APPROVED)"
                >
                  Approve
                </app-button>
              }

              @case (Status.APPROVED) {
                <app-button
                  size="sm"
                  variant="danger"
                  (click)="approveDtr(Status.DRAFT)"
                >
                  Convert back to draft
                </app-button>

                <app-button
                  size="sm"
                  variant="success"
                  (click)="approveDtr(Status.PUBLISHED)"
                >
                  Publish
                </app-button>
              }

              @case (Status.PUBLISHED) {
                <app-button
                  size="sm"
                  variant="danger"
                  (click)="approveDtr(Status.DRAFT)"
                >
                  Convert back to draft
                </app-button>
              }
            }
          </ng-container>
        </div>
      </div>
    }
  </div>

  <div>
    @if (loading) {
      <app-spinner />
    }
  </div>

  @if ((dtrScore?.missingComponents)!.length > 0 && status === Status.DRAFT) {
    <app-messages
      variant="warning"
      [message]="getMissingComponentsMessage()"
      title="Publish these components inorder to see the Daily Trading Report:"
    >
    </app-messages>
  } @else {
    <app-card id="exportDiv">
      @if (dtr) {
        <table
          class="table-bordered text-nowrap w-100 styled-table"
          id="dtrTable"
        >
          <tr
            [ngClass]="{
              'reg-striped-row': market === Market.REG,
              'etf-striped-row': market === Market.ETF,
            }"
          >
            <td colspan="2">
              <h5 class="my-3">
                {{ exchange }} DAILY TRADING REPORT({{ market }}):
              </h5>
            </td>
            <td colspan="5">
              <h5 class="my-3">{{ date | date: "EEEE, MMMM dd, yyyy" }}</h5>
            </td>
          </tr>
          <tr>
            <td class="text-start">
              <strong> 1. Volume and Turnover </strong>
            </td>
            <td colspan="6"></td>
          </tr>

          <tr>
            <td colspan="2">1.1 Total Market Volume of Shares Traded</td>
            <td class="text-end">
              {{
                dtrContent?.volumeAndTurnover?.totalMarketVolumeShares | number
              }}
            </td>
            <td colspan="4"></td>
          </tr>

          <tr>
            <td colspan="2">
              1.2 Total Market Turnover
              {{ isZse ? "(ZWG)" : "" }}
            </td>
            <td class="text-end">
              {{
                dtrContent?.volumeAndTurnover?.totalMarketTurnover
                  | currency: "USD" : "symbol" : "1.2-2"
              }}
            </td>
            <td colspan="4"></td>
          </tr>

          <tr>
            <td>1.3 Foreign Purchases</td>
            <td>Value {{ isZse ? "(ZWG)" : "" }}</td>
            <td class="text-end">
              {{
                dtrContent?.volumeAndTurnover?.foreignPurchases?.value
                  | currency: "USD" : "symbol" : "1.2-2"
              }}
            </td>
            <td colspan="4"></td>
          </tr>
          <tr>
            <td></td>
            <td>Number of Shares Traded</td>
            <td class="text-end">
              {{
                dtrContent?.volumeAndTurnover?.foreignPurchases
                  ?.numberOfSharesTraded | number
              }}
            </td>
            <td colspan="4"></td>
          </tr>
          <tr>
            <td>1.4 Foreign Sales</td>
            <td>Value {{ isZse ? "(ZWG)" : "" }}</td>
            <td class="text-end">
              {{
                dtrContent?.volumeAndTurnover?.foreignSales?.value
                  | currency: "USD" : "symbol" : "1.2-2"
              }}
            </td>
            <td colspan="4"></td>
          </tr>
          <tr>
            <td></td>
            <td>Number of Shares Traded</td>
            <td class="text-end">
              {{
                dtrContent?.volumeAndTurnover?.foreignSales
                  ?.numberOfSharesTraded | number
              }}
            </td>
            <td colspan="4"></td>
          </tr>
          <tr>
            <td colspan="2">Total number of trades ({{ market }})</td>
            <td class="text-end">
              {{ dtrContent?.volumeAndTurnover?.totalNumberOfTrades | number }}
            </td>
            <td colspan="4"></td>
          </tr>
          <tr>
            <td colspan="2">
              Total number of trades
              {{ isZse ? "(REG, ODD, REIT)" : "(REG, BOND)" }}
            </td>
            <td class="text-end">
              {{
                dtrContent?.volumeAndTurnover
                  ?.totalNumberOfTradesForOtherMarkets | number
              }}
            </td>
            <td colspan="4"></td>
          </tr>
          <tr>
            <td colspan="3">
              <b>
                <u>
                  2.
                  {{ market === Market.ETF ? Market.ETF : exchange }} Indices
                </u>
              </b>
            </td>
            <td class="text-center" colspan="4">
              <b>
                <u> Change </u>
              </b>
            </td>
          </tr>

          <tr>
            <td colspan="2">
              {{
                exchange == Exchange.ZSE
                  ? dtrContent?.indices?.index
                  : dtrContent?.indices?.index === "NULL"
                    ? "NULL"
                    : "VFEX ALLSHARE"
              }}
            </td>

            <td class="text-end">
              {{ dtrContent?.indices?.closingPrice | number: "1.1-2" }}
            </td>

            <td class="text-end">
              {{ dtrContent?.indices?.changePercentage | number: "1.1-2" }}%
            </td>

            <td class="text-end">
              {{ dtrContent?.indices?.changePoints | number: "1.2-2" }}
            </td>

            <td class="text-end">
              {{ dtrContent?.indices?.changePoints }} points
            </td>

            <td class="text-end"></td>
          </tr>

          <tr>
            <td colspan="7">
              <b>
                <u>3. Top 5 Counters </u>
              </b>
            </td>
          </tr>
          <tr>
            <td colspan="7">3.1 Volumes Traded</td>
          </tr>
          <tr>
            <td></td>
            <td>
              <b>
                <u> Counter </u>
              </b>
            </td>
            <td class="text-end">
              <b>
                <u> Volumes </u>
              </b>
            </td>
            <td colspan="2"></td>
            <td class="text-end">
              <b>
                <u> Closing Price </u>
              </b>
            </td>
            <td class="text-end">
              <b>
                <u> Value Traded </u>
              </b>
            </td>
          </tr>

          @for (
            volumesTraded of dtrContent?.topFiveCounters?.volumesTraded;
            track volumesTraded
          ) {
            <tr>
              <td></td>
              <td>
                {{ volumesTraded.counter }}
              </td>
              <td class="text-end">
                {{ volumesTraded.volumes | number }}
              </td>
              <td colspan="2"></td>
              <td class="text-end">
                {{ volumesTraded.closingPrice | number: "1.1-2" }} cents
              </td>
              <td class="text-end">
                {{
                  volumesTraded.valueTraded
                    | currency: "USD" : "symbol" : "1.2-2"
                }}
              </td>
            </tr>
          }

          <tr>
            <td colspan="7">3.2 Values Traded {{ isZse ? "(ZWG)" : "" }}</td>
          </tr>
          <tr>
            <td></td>
            <td>
              <b>
                <u> Counter </u>
              </b>
            </td>
            <td class="text-end">
              <b>
                <u> Turnover Value </u>
              </b>
            </td>
            <td colspan="2"></td>
            <td class="text-end">
              <b>
                <u> Closing Price </u>
              </b>
            </td>
            <td class="text-end">
              <b>
                <u> Volumes </u>
              </b>
            </td>
          </tr>
          @for (
            valuesTraded of dtrContent?.topFiveCounters?.valuesTraded;
            track valuesTraded
          ) {
            <tr>
              <td></td>
              <td>
                {{ valuesTraded.counter }}
              </td>
              <td class="text-end">
                {{
                  valuesTraded.valueTraded
                    | currency: "USD" : "symbol" : "1.2-2"
                }}
              </td>
              <td colspan="2"></td>
              <td class="text-end">
                {{ valuesTraded.closingPrice | number: "1.1-2" }} cents
              </td>
              <td class="text-end">
                {{ valuesTraded.volumes | number }}
              </td>
            </tr>
          }

          <!-- Foreign Deals -->
          <tr>
            <td colspan="7">Foreign Deals&nbsp;-&nbsp;Purchases</td>
          </tr>

          @for (
            foreignDealsPurchase of dtrContent?.topFiveCounters
              ?.foreignPurchases;
            track foreignDealsPurchase
          ) {
            <tr>
              <td></td>
              <td>
                {{ foreignDealsPurchase.counter }}
              </td>
              <td class="text-end">
                {{
                  foreignDealsPurchase.value
                    | currency: "USD" : "symbol" : "1.2-2"
                }}
              </td>
              <td colspan="2"></td>
              <td class="text-end">
                {{ foreignDealsPurchase.price | number: "1.1-2" }} cents
              </td>
              <td class="text-end">
                {{ foreignDealsPurchase.volume | number }}
              </td>
            </tr>
          }

          <!-- Foreign Sales -->
          <tr>
            <td colspan="7">Foreign Deals&nbsp;-&nbsp;Sales</td>
          </tr>
          @for (
            foreignDealsSale of dtrContent?.topFiveCounters?.foreignSales;
            track foreignDealsSale
          ) {
            <tr>
              <td></td>
              <td>
                {{ foreignDealsSale.counter }}
              </td>
              <td class="text-end">
                {{
                  foreignDealsSale.value | currency: "USD" : "symbol" : "1.2-2"
                }}
              </td>
              <td colspan="2"></td>
              <td class="text-end">
                {{ foreignDealsSale.price | number: "1.1-2" }} cents
              </td>
              <td class="text-end">
                {{ foreignDealsSale.volume | number }}
              </td>
            </tr>
          }

          <tr>
            <td colspan="7">
              <b>
                <u> 4. Movers and Shakers </u>
              </b>
            </td>
          </tr>
          <tr>
            <td colspan="2">4.1 Movers</td>
            <td class="text-end">
              <b>
                <u>Today's Price</u>
              </b>
            </td>
            <td class="text-end">
              <b>
                <u> Change </u>
              </b>
            </td>
            <td></td>
            <td class="text-end">
              <b>
                <u> Prev Price </u>
              </b>
            </td>
            <td class="text-end">
              <b>
                <u> % Change </u>
              </b>
            </td>
          </tr>

          @for (
            topMover of dtrContent?.moversAndShakers?.movers;
            track topMover
          ) {
            <tr>
              <td></td>
              <td>
                {{ topMover.counter }}
              </td>
              <td class="text-end">
                {{ topMover.todayPrice | number: "1.2-2" }} cents
              </td>
              <td class="text-end">
                {{ topMover.change | number: "1.2-2" }} cents
              </td>
              <td></td>
              <td class="text-end">
                {{ topMover.prevPrice | number: "1.2-2" }} cents
              </td>
              <td class="text-end">{{ topMover.percentageChange }} %</td>
            </tr>
          }

          <tr>
            <td colspan="2">4.2 Shakers</td>
            <td class="text-end">
              <b>
                <u></u>
              </b>
            </td>
            <td class="text-end">
              <b>
                <u> </u>
              </b>
            </td>
            <td></td>
            <td class="text-end">
              <b>
                <u> </u>
              </b>
            </td>
            <td class="text-end">
              <b>
                <u> </u>
              </b>
            </td>
          </tr>

          @for (
            topShaker of dtrContent?.moversAndShakers?.shakers;
            track topShaker
          ) {
            <tr>
              <td></td>
              <td>
                {{ topShaker.counter }}
              </td>
              <td class="text-end">
                {{ topShaker.todayPrice | number: "1.2-2" }} cents
              </td>
              <td class="text-end">
                {{ topShaker.change | number: "1.2-2" }} cents
              </td>
              <td></td>
              <td class="text-end">
                {{ topShaker.prevPrice | number: "1.2-2" }} cents
              </td>
              <td class="text-end">{{ topShaker.percentageChange }} %</td>
            </tr>
          }

          <tr>
            <td colspan="7">
              <b>
                <u>
                  5. {{ exchange }}
                  {{
                    market === Market.ETF
                      ? "ETFs Market Capitalisation"
                      : "Total Market Capitalisation"
                  }}
                </u>
              </b>
            </td>
          </tr>
          <tr>
            <td></td>
            <td>TOTAL</td>
            <td class="text-end">
              {{
                dtrContent?.totalMarketCapitalisation
                  | currency: "USD" : "symbol" : "1.2-2"
              }}
            </td>
          </tr>
          <tr>
            <td colspan="7">
              <b>
                <u>
                  {{
                    market === Market.ETF
                      ? ""
                      : "6. Top 10 in Market Capitalisation"
                  }}
                </u>
              </b>
            </td>
          </tr>
          <tr>
            <td colspan="3"></td>
            <td>
              <b>
                <u class="text-end"> % of total mkt cap </u>
              </b>
            </td>
            <td colspan="3"></td>
          </tr>
          <tr>
            <td></td>
            <td><strong>COUNTER</strong></td>
            <td class="text-end"><strong>VALUE</strong></td>
            <td colspan="4"></td>
          </tr>

          @for (topTen of dtrContent?.topTenMarketCap; track topTen) {
            <tr>
              <td></td>
              <td>
                {{ topTen.counter }}
              </td>
              <td class="text-end">
                {{ topTen.value | currency: "USD" : "symbol" : "1.2-2" }}
              </td>
              <td class="text-end">
                {{ topTen.percentage | number: "1.1-2" }}
              </td>
              <td colspan="3"></td>
            </tr>
          }
        </table>
      } @else {
        <app-text variant="small/medium" class="text-center"
          >No DTR data found.</app-text
        >
      }
    </app-card>
  }
</div>
