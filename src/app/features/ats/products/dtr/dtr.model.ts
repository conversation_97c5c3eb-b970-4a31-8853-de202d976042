import { Status } from "@/app/core/enums/status.enum";

export interface Dtr {
  id: string;
  status: Status;
  createdDate: string;
  modifiedDate: string;
  createdUser: string;
  modifiedUser: string;
  systemField: string;
  approvedBy: string | null;
  dateApproved: string | null;
  content: string;
  statsDate: string;
  exchangeId: number;
  marketId: number;
}

export interface DailyTradingReport {
  // 1. Volume and Turnover
  volumeAndTurnover: VolumeAndTurnover;

  // 2. Indices
  indices: Indices;

  // 3. Top five counters
  topFiveCounters: TopFiveCounters;

  // 4. Movers and shakers
  moversAndShakers: MoversAndShakers;

  // 5. ZSE total market capitalisation
  totalMarketCapitalisation: number;

  // 6. Top ten in market capitalisation
  topTenMarketCap: MarketCapEntry[];

  // 7. Dtr Score
  dtrScore: ScoreResult;
}

export interface ScoreResult {
  score: number;
  missingComponents: string[];
}

export interface VolumeAndTurnover {
  totalMarketVolumeShares: number;
  totalMarketTurnover: number;
  foreignPurchases: ForeignTrade;
  foreignSales: ForeignTrade;
  totalNumberOfTrades: number;
  totalNumberOfTradesForOtherMarkets: number;
}

export interface ForeignTrade {
  value: number;
  numberOfSharesTraded: number;
}

export interface Indices {
  index: string;
  closingPrice: number;
  changePercentage: number;
  changePoints: number;
}

export interface TopFiveCounters {
  volumesTraded: VVTraded[];
  valuesTraded: VVTraded[];
  specialBargains: null;
  foreignPurchases: ForeignDeal[];
  foreignSales: ForeignDeal[];
}

export interface VVTraded {
  counter: string;
  volumes: number;
  closingPrice: number;
  valueTraded: number;
}

export interface ForeignDeal {
  counter: string;
  value: number;
  price: number;
  volume: number;
}

export interface MoversAndShakers {
  movers: PriceMovement[];
  shakers: PriceMovement[];
}

export interface PriceMovement {
  counter: string;
  todayPrice: number;
  change: number;
  prevPrice: number;
  percentageChange: number;
}

export interface MarketCapEntry {
  counter: string;
  value: number;
  percentage: number;
}

export interface UpdateDTRPayload {
  id: string;
  status: Status;
}
