import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { Market } from "@/app/core/enums/market.enum";
import { ProductLinkTable } from "@/app/core/enums/product-link-table.enum";
import { Status } from "@/app/core/enums/status.enum";
import { changeStatusByAvailableStatus } from "@/app/core/utils/change-status.util";
import { getMarket } from "@/app/core/utils/common.util";
import { handleError } from "@/app/core/utils/error-handler.util";
import { CurrencyPipe, DatePipe, DecimalPipe, NgClass } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import Swal from "sweetalert2";
import { ButtonGroupComponent } from "../../../../shared/ui/button/button-group/button-group.component";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { MessagesComponent } from "../../../../shared/ui/messages/messages.component";
import { SpinnerComponent } from "../../../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { ChangeATSProductStatusRequest } from "../products.model";
import { DailyTradingReport, Dtr, ScoreResult } from "./dtr.model";
import { DtrService } from "./dtr.service";

@Component({
  selector: "app-foreign-trades",
  templateUrl: "./dtr.component.html",
  styleUrls: ["./dtr.component.css"],
  standalone: true,
  imports: [
    FormsModule,
    NgClass,
    DecimalPipe,
    CurrencyPipe,
    DatePipe,
    SpinnerComponent,
    TextComponent,
    ButtonGroupComponent,
    ButtonComponent,
    CardComponent,
    MessagesComponent,
    ButtonPermissionDirective,
  ],
})
export class ATSDtrComponent implements OnInit {
  dtr: Dtr | undefined = undefined;
  today: string = new Date().toISOString().split("T")[0];
  date: string = this.today;
  maxDate: string = this.today;

  getMarket = getMarket;

  exchange: Exchange = Exchange.VFEX;
  Exchange = Exchange;
  isZse: boolean = false;

  market: Market = Market.REG;
  status: Status = Status.DRAFT;
  Status = Status;
  Market = Market;
  loading: boolean = true;
  dtrContent: DailyTradingReport | null = null;
  dtrScore: ScoreResult | null = null;

  get exchangeName(): string {
    return `${this.exchange === Exchange.ZSE ? "Zimbabwe" : "Victoria Falls"} Stock Exchange`;
  }

  get reportTitle(): string {
    return `${this.getMarket(this.market)} Daily Trading Report`;
  }

  constructor(
    private readonly dtrService: DtrService,
    private readonly route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.exchange = params["exchange"] || Exchange.VFEX;
      this.isZse = params["exchange"] === Exchange.ZSE;
    });

    this.loadDtrData();
  }

  loadDtrData() {
    this.loading = true;
    this.dtrService
      .getDtrByDate(this.date, this.exchange, this.market, this.status)
      .subscribe(
        (res: { data: { data: Dtr; availableStatus: number } }) => {
          this.dtr = res?.data?.data;
          this.dtrContent = res?.data?.data?.content
            ? JSON.parse(res?.data?.data?.content)
            : {};

          this.dtrScore = this.dtrContent?.dtrScore
            ? this.dtrContent?.dtrScore
            : null;

          changeStatusByAvailableStatus(
            res?.data?.availableStatus,
            this.setStatus.bind(this),
          );

          this.loading = false;
        },
        (error: any) => {
          this.loading = false;
          Swal.fire(
            "Error",
            "An error occurred while fetching DTR data.",
            "error",
          );
        },
      );
  }

  getMissingComponentsMessage(): string {
    if (
      !this.dtrScore?.missingComponents ||
      this.dtrScore.missingComponents.length === 0
    ) {
      return "No components are missing.";
    }
    return this.dtrScore.missingComponents.join(", ");
  }

  async approveDtr(status: Status) {
    this.loading = true;

    const payload: ChangeATSProductStatusRequest = {
      market: this.market,
      exchange: this.exchange,
      date: this.date,
      status: status,
      productType: ProductLinkTable.DTR,
    };

    (await this.dtrService.changeStatus(payload)).subscribe({
      next: () => this.setStatus(status),
      error: (error: any) => {
        handleError(error);
        this.loading = false;
      },
    });
  }

  setStatus(status: Status) {
    if (this.status !== status) {
      // Avoid redundant status changes
      this.status = status;
      this.loadDtrData();
    }
  }

  setMarket(market: Market) {
    this.market = market;
    this.loadDtrData();
  }

  getDataByDate() {
    this.loadDtrData();
  }

  exportData() {
    this.dtrService.exportData({
      date: this.date,
      exchange: this.exchange,
      reportTitle: this.reportTitle,
      exchangeName: this.exchangeName,
    });
  }
}
