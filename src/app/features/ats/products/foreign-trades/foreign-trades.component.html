<div class="flex flex-col gap-3">
  <app-text variant="title/semibold">{{ exchange }} Foreign Trades</app-text>

  <div class="flex justify-between gap-3">
    <app-button-group>
      @for (statusOption of statusOptions; track statusOption.value) {
        <app-button
          (click)="setStatus(statusOption.value)"
          size="sm"
          [variant]="
            status === statusOption.value ? 'success' : 'outline-success'
          "
          [attr.aria-pressed]="status === statusOption.value"
        >
          {{ statusOption.key }}
        </app-button>
      }
    </app-button-group>

    <div class="flex items-center gap-3" style="width: fit-content">
      <label for="tradeDate" class="text-sm font-medium">DATE</label>
      <input
        id="tradeDate"
        name="date"
        type="date"
        [(ngModel)]="date"
        (ngModelChange)="loadForeignTradesData()"
        [max]="maxDate"
        class="form-control"
      />
    </div>
  </div>

  <div class="flex flex-col gap-3">
    <div class="flex justify-between gap-2">
      <app-button-group>
        <app-button
          (click)="setMarket(Market.REG)"
          size="sm"
          [variant]="market === Market.REG ? 'warning' : 'outline-warning'"
        >
          EQUITIES
        </app-button>

        @if (isZse) {
          <app-button
            (click)="setMarket(Market.ETF)"
            size="sm"
            [variant]="market === Market.ETF ? 'warning' : 'outline-warning'"
          >
            ETFS
          </app-button>

          <app-button
            (click)="setMarket(Market.REIT)"
            size="sm"
            [variant]="market === Market.REIT ? 'warning' : 'outline-warning'"
          >
            REITS
          </app-button>
        }

        @if (!isZse) {
          <app-button
            (click)="setMarket(Market.BOND)"
            size="sm"
            [variant]="market === Market.BOND ? 'warning' : 'outline-warning'"
          >
            BOND
          </app-button>

          <app-button
            (click)="setMarket(Market.REIT)"
            size="sm"
            [variant]="market === Market.REIT ? 'warning' : 'outline-warning'"
          >
            REITS
          </app-button>
        }
      </app-button-group>

      @if (foreignPurchases.length > 0 || foreignSales.length > 0) {
        <div class="flex items-center justify-between gap-2">
          <div class="flex justify-end gap-2">
            <app-button size="sm" variant="success" (click)="exportData()">
              Export Data
            </app-button>
          </div>

          <ng-container *appButtonPermission="'edit'">
            <div class="flex justify-end gap-2">
              @if (editedPurchaseRows.length > 0) {
                <app-button size="sm" (click)="updateForeignTrades(true)">
                  Update Purchases
                </app-button>
              }
              @if (editedSaleRows.length > 0) {
                <app-button size="sm" (click)="updateForeignTrades(false)">
                  Update Sales
                </app-button>
              }

              @switch (status) {
                @case (Status.DRAFT) {
                  <app-button
                    size="sm"
                    variant="warning"
                    (click)="changeStatus(Status.APPROVED)"
                  >
                    Approve
                  </app-button>
                }

                @case (Status.APPROVED) {
                  <app-button
                    size="sm"
                    variant="danger"
                    (click)="changeStatus(Status.DRAFT)"
                  >
                    Convert back to draft
                  </app-button>

                  <app-button
                    size="sm"
                    variant="success"
                    (click)="changeStatus(Status.PUBLISHED)"
                  >
                    Publish
                  </app-button>
                }

                @case (Status.PUBLISHED) {
                  <app-button
                    size="sm"
                    variant="danger"
                    (click)="changeStatus(Status.DRAFT)"
                  >
                    Convert back to draft
                  </app-button>
                }
              }
            </div>
          </ng-container>
        </div>
      }
    </div>

    <app-card>
      <div>
        <app-text variant="regular/semibold" class="text-center">
          {{ isZse ? "Zimbabwe" : "Victoria Falls" }} Stock Exchange - Foreign
          Trades Report
        </app-text>
        <app-text variant="small/semibold" class="text-center">
          {{ date | date: "fullDate" }}
        </app-text>
      </div>
    </app-card>

    @if (loading) {
      <app-spinner size="md" />
    } @else {
      @if (foreignPurchases.length > 0) {
        <ng-container>
          <ng-template
            [ngTemplateOutlet]="tradeTable"
            [ngTemplateOutletContext]="{
              trades: foreignPurchases,
              title: 'Foreign Purchases',
              type: ForeignTradeType.BUY,
            }"
          ></ng-template>
        </ng-container>
      } @else {
        <app-messages variant="primary" message="No purchase data available." />
      }

      @if (foreignSales.length > 0) {
        <ng-container>
          <ng-template
            [ngTemplateOutlet]="tradeTable"
            [ngTemplateOutletContext]="{
              trades: foreignSales,
              title: 'Foreign Sales',
              type: ForeignTradeType.SELL,
            }"
          ></ng-template>
        </ng-container>
      } @else {
        <app-messages variant="primary" message="No sales data available." />
      }
    }

    <ng-template
      #tradeTable
      let-trades="trades"
      let-title="title"
      let-type="type"
    >
      <app-card>
        <div class="flex items-center justify-between mb-3">
          <app-text [variant]="'title/semibold'">{{ title }}</app-text>

          <div>
            <input
              class="form-control"
              type="text"
              (input)="dt.filterGlobal($any($event.target).value, 'contains')"
              placeholder="Search keyword"
            />
          </div>
        </div>

        <p-table
          #dt
          [value]="trades"
          dataKey="id"
          [rows]="10"
          [rowsPerPageOptions]="[10, 25, 50]"
          [paginator]="false"
          [globalFilterFields]="['symbol', 'price', 'volume', 'grandAmount']"
          [tableStyle]="{ 'min-width': '50rem' }"
          styleClass="p-datatable-striped p-datatable-hoverable"
        >
          <ng-template pTemplate="header">
            <tr
              class="text-xs font-semibold tracking-wider text-gray-600 uppercase bg-gray-100"
            >
              <th>
                <div class="flex items-center gap-2" pSortableColumn="symbol">
                  Counter <p-sortIcon field="symbol"></p-sortIcon>
                </div>
              </th>
              <th>
                <div
                  class="flex items-center justify-end gap-2"
                  pSortableColumn="volume"
                >
                  Volume <p-sortIcon field="volume"></p-sortIcon>
                </div>
              </th>
              <th>
                <div
                  class="flex items-center justify-end gap-2"
                  pSortableColumn="price"
                >
                  Price {{ isZse ? "(ZWG)c" : "(USD)" }}
                  <p-sortIcon field="price"></p-sortIcon>
                </div>
              </th>
              <th>
                <div
                  class="flex items-center justify-end gap-2"
                  pSortableColumn="grandAmount"
                >
                  Value {{ isZse ? "(ZWG)c" : "(USD)" }}
                  <p-sortIcon field="grandAmount"></p-sortIcon>
                </div>
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-trade>
            <tr class="transition-colors duration-200 hover:bg-gray-50">
              <td>{{ trade.symbol }}</td>
              <td class="text-end">
                {{ trade.volume | number }}
              </td>
              <td class="text-end">
                {{ trade.price | number: "1.4-4" }}
              </td>
              <td class="text-end">
                {{ trade.grandAmount | number: "1.4-4" }}
              </td>
            </tr>
          </ng-template>
          <ng-template pTemplate="footer">
            <tr>
              <th></th>
              <th class="text-end">
                {{
                  type === ForeignTradeType.BUY
                    ? (purchasesStats?.totalVolume | number)
                    : (salesStats?.totalVolume | number)
                }}
              </th>
              <th></th>
              <th class="text-end">
                {{
                  type === ForeignTradeType.BUY
                    ? (purchasesStats?.grandAmount | number: "1.4-4")
                    : (salesStats?.grandAmount | number: "1.4-4")
                }}
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
            <tr>
              <td colspan="4" class="text-center">No data found</td>
            </tr>
          </ng-template>
        </p-table>
      </app-card>
    </ng-template>
  </div>
</div>
