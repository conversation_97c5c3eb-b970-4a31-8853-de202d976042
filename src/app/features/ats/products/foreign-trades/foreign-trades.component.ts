import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { ForeignTradeType } from "@/app/core/enums/foreign-trade-type";
import { Market } from "@/app/core/enums/market.enum";
import { ProductLinkTable } from "@/app/core/enums/product-link-table.enum";
import { Status } from "@/app/core/enums/status.enum";
import { ExcelExportOptions } from "@/app/core/interfaces/export.interface";
import { changeStatusByAvailableStatus } from "@/app/core/utils/change-status.util";
import { getMarket } from "@/app/core/utils/common.util";
import { handleError } from "@/app/core/utils/error-handler.util";
import { CommonModule, DecimalPipe } from "@angular/common";
import { Component, OnInit, inject } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { ButtonModule } from "primeng/button";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { TableModule } from "primeng/table";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import Swal from "sweetalert2";
import { ButtonGroupComponent } from "../../../../shared/ui/button/button-group/button-group.component";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { MessagesComponent } from "../../../../shared/ui/messages/messages.component";
import { SpinnerComponent } from "../../../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { ChangeATSProductStatusRequest } from "../products.model";
import { ForeignTrade, ForeignTradesStats } from "./foreign-trades.model";
import {
  ForeignTradesExportData,
  ForeignTradesExportService,
} from "./services/foreign-trades-export.service";
import { ForeignTradesService } from "./services/foreign-trades.service";

@Component({
  selector: "app-foreign-trades",
  templateUrl: "./foreign-trades.component.html",
  styleUrl: "./foreign-trades.component.css",
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    DecimalPipe,
    IconFieldModule,
    InputIconModule,
    ButtonModule,
    SpinnerComponent,
    ButtonGroupComponent,
    ButtonComponent,
    TextComponent,
    MessagesComponent,
    CardComponent,
    ButtonPermissionDirective,
  ],
})
export class ATSForeignTradesComponent implements OnInit {
  private route = inject(ActivatedRoute);
  private foreignTradesService = inject(ForeignTradesService);
  private exportService = inject(ForeignTradesExportService);
  private destroy$ = new Subject<void>();

  readonly Status = Status;
  readonly ForeignTradeType = ForeignTradeType;
  readonly Exchange = Exchange;
  readonly Market = Market;

  statusOptions = [
    { key: "Draft", value: Status.DRAFT },
    { key: "Approved", value: Status.APPROVED },
    { key: "Published", value: Status.PUBLISHED },
  ];

  foreignPurchases: ForeignTrade[] = [];
  foreignSales: ForeignTrade[] = [];
  purchasesStats: ForeignTradesStats | null = null;
  salesStats: ForeignTradesStats | null = null;
  loading = true;

  getMarket = getMarket;

  today = new Date().toISOString().split("T")[0];
  date = this.today;
  maxDate = this.today;
  exchange: Exchange = Exchange.VFEX;
  status: Status = Status.DRAFT;
  market: Market = Market.REG;

  isZse: boolean = false;

  get exchangeName(): string {
    return `${this.exchange === Exchange.ZSE ? "Zimbabwe" : "Victoria Falls"} Stock Exchange`;
  }

  get reportTitle(): string {
    return `${this.getMarket(this.market)} Daily Trading Report`;
  }

  editedPurchaseRows: any[] = [];
  editedSaleRows: any[] = [];

  ngOnInit(): void {
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe((params) => {
        this.exchange = params["exchange"] as Exchange;
        this.isZse = params["exchange"] === Exchange.ZSE ? true : false;
      });

    this.loadForeignTradesData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadForeignTradesData(): void {
    this.editedPurchaseRows = [];
    this.editedSaleRows = [];
    this.loading = true;

    this.foreignTradesService
      .getForeignTradesByDate(
        this.date,
        this.exchange,
        this.status,
        this.market,
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          this.foreignPurchases = res.data?.buys?.trades || [];
          this.purchasesStats = res.data?.buys?.stats || null;
          this.foreignSales = res.data?.sells?.trades || [];
          this.salesStats = res.data?.sells?.stats || null;

          changeStatusByAvailableStatus(
            res?.data?.availableStatus,
            this.setStatus.bind(this),
          );
          this.loading = false;
        },
        error: (error: any) => {
          if (error?.error?.message === "Wait for market to close!") {
            handleError(error, "Market is still open!", "info");
          } else {
            handleError(error);
          }
          this.loading = false;
        },
      });
  }

  setStatus(status: Status) {
    if (this.status !== status) {
      // Avoid redundant status changes
      this.status = status;
      this.loadForeignTradesData();
    }
  }

  setMarket(market: Market) {
    this.market = market;
    this.loadForeignTradesData();
  }

  getNextStatus(currentStatus: Status): Status {
    switch (currentStatus) {
      case Status.DRAFT:
        return Status.APPROVED;
      case Status.APPROVED:
        return Status.PUBLISHED;
      case Status.PUBLISHED:
        return Status.DRAFT;
      default:
        return Status.DRAFT;
    }
  }

  getStatusActionText(status: Status): string {
    switch (status) {
      case Status.DRAFT:
        return "Approve";
      case Status.APPROVED:
        return "Publish";
      case Status.PUBLISHED:
        return "Send back to Draft";
      default:
        return "Change Status";
    }
  }

  getStatusButtonClass(status: Status): string {
    switch (status) {
      case Status.DRAFT:
        return "btn-warning";
      case Status.APPROVED:
        return "btn-success";
      case Status.PUBLISHED:
        return "btn-danger";
      default:
        return "btn-secondary";
    }
  }

  async changeStatus(newStatus: Status) {
    this.loading = true;
    let message: string;
    let item: string = "foreign trades";

    switch (newStatus) {
      case Status.DRAFT:
        message = `convert ${item} to draft?`;
        break;
      case Status.APPROVED:
        message = `approve ${item}?`;
        break;
      case Status.PUBLISHED:
        message = `publish ${item}?`;
        break;
      default:
        message = "perform this action?";
        break;
    }

    Swal.fire({
      title: "Warning",
      text: `Are you sure you want to ${message}`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
    }).then(async (result) => {
      if (result.isConfirmed) {
        const updatedData = [
          ...this.foreignPurchases,
          ...this.foreignSales,
        ].map((trade) => ({
          ...trade,
          exchange: this.exchange,
          market: this.market,
          status: newStatus,
          statsDate: this.date,
        }));

        const updatedPurchases = this.foreignPurchases.map((trade) => ({
          ...trade,
          exchange: this.exchange,
          market: this.market,
          status: newStatus,
          statsDate: this.date,
        }));

        const updatedSales = this.foreignSales.map((trade) => ({
          ...trade,
          exchange: this.exchange,
          market: this.market,
          status: newStatus,
          statsDate: this.date,
        }));

        const exportData: ForeignTradesExportData = {
          purchases: updatedPurchases,
          sales: updatedSales,
          purchasesStats: this.purchasesStats!,
          salesStats: this.salesStats!,
        };

        const payload: ChangeATSProductStatusRequest = {
          market: this.market,
          exchange: this.exchange,
          date: updatedData[0].statsDate,
          status: newStatus,
          productType: ProductLinkTable.FOREIGN_TRADES,
        };

        (
          await this.foreignTradesService.changeStatus(payload, exportData)
        ).subscribe({
          next: () => this.setStatus(newStatus),
          error: (error: any) => {
            handleError(error);
            this.loading = false;
          },
        });
      } else {
        this.loading = false;
      }
    });
  }

  updateForeignTrades(isPurchase: boolean): void {
    const editedRows = isPurchase
      ? this.editedPurchaseRows
      : this.editedSaleRows;

    this.foreignTradesService
      .updateForeignTrades(editedRows)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.loadForeignTradesData();
          if (isPurchase) {
            this.editedPurchaseRows = [];
          } else {
            this.editedSaleRows = [];
          }
        },
        error: handleError,
      });
  }

  markCellAsEdited(
    event: Event,
    type: ForeignTradeType,
    trade: ForeignTrade,
  ): void {
    const target = event.target as HTMLElement;
    const field = target.getAttribute("data-field") as keyof ForeignTrade;
    const newValue = target.innerText.trim();

    const removeCommas = (value: string): number =>
      parseFloat(value?.replace(/,/g, "")) || 0;

    const parsedNewValue = removeCommas(newValue);

    if (trade[field] !== parsedNewValue) {
      const updatedTrade = {
        id: trade.id,
        price: trade.price,
        volume: trade.volume,
        grandAmount: trade.grandAmount,
        [field]: parsedNewValue,
      };
      const editedRows =
        type === ForeignTradeType.BUY
          ? this.editedPurchaseRows
          : this.editedSaleRows;

      const updatedRows = editedRows.some((row) => row.id === trade.id)
        ? editedRows.map((row) => (row.id === trade.id ? updatedTrade : row))
        : [...editedRows, updatedTrade];

      if (type === ForeignTradeType.BUY) {
        this.editedPurchaseRows = updatedRows;
      } else {
        this.editedSaleRows = updatedRows;
      }
    }
  }

  exportData(): void {
    const data = {
      purchases: this.foreignPurchases as ForeignTrade[],
      sales: this.foreignSales as ForeignTrade[],
      purchasesStats: this.purchasesStats as ForeignTradesStats,
      salesStats: this.salesStats as ForeignTradesStats,
    };

    const options: ExcelExportOptions = {
      fileName: `Foreign_Trades_Report_${this.date}`,
      sheetName: "Foreign Trades Report",
      exchangeName: this.exchangeName,
      exchange: this.exchange,
      reportTitle: "Foreign Trades Report",
    };

    this.exportService.exportExcel({
      data,
      fileName: "Foreign_Trades",
      exchange: this.exchange,
      options,
      rawDate: this.date,
    });
  }
}
