export interface ForeignTrade {
  id: string;
  status: string;
  companyStatus: string;
  createdDate: string;
  modifiedDate: string;
  createdUser: string;
  modifiedUser: string;
  systemField: string;
  approvedBy: string;
  dateApproved: string;
  statsDate: string;
  symbol: string;
  price: number;
  volume: number;
  grandAmount: number;
  exchangeId: number;
  marketId: number;
}

export interface ForeignTradesStats {
  grandAmount: number;
  totalVolume: number;
  totalPrice: number;
}

export interface ForeignPurchases {
  data: ForeignTrade[];
  stats: ForeignTradesStats;
  availableStatus: number;
}

export interface ForeignSales {
  data: ForeignTrade[];
  stats: ForeignTradesStats;
}
