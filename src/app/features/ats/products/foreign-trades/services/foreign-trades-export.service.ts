import { Exchange } from "@/app/core/enums/exchange.enum";
import { UtilityService } from "@/app/core/services/utility.service";
import { Injectable } from "@angular/core";
import * as ExcelJS from "exceljs";
import { ForeignTrade, ForeignTradesStats } from "../foreign-trades.model";

export interface ForeignTradesExportData {
  purchases: ForeignTrade[];
  sales: ForeignTrade[];
  purchasesStats: ForeignTradesStats;
  salesStats: ForeignTradesStats;
}

@Injectable({
  providedIn: "root",
})
export class ForeignTradesExportService {
  constructor(private readonly utilityService: UtilityService) {}

  private lastGeneratedFile: File | null = null;

  async getLastGeneratedFile(): Promise<File | null> {
    return this.lastGeneratedFile;
  }

  async exportExcel({
    data,
    fileName,
    exchange,
    options,
    rawDate,
    shouldSaveFile = true,
  }: {
    data: ForeignTradesExportData;
    fileName: string;
    exchange: Exchange;
    options: any;
    rawDate: string;
    shouldSaveFile?: boolean;
  }): Promise<void> {
    try {
      const workbook = new ExcelJS.Workbook();

      // Create worksheets for Purchases and Sales
      const worksheetPurchases = workbook.addWorksheet("Foreign Purchases", {
        pageSetup: { paperSize: 9, orientation: "landscape" },
      });
      const worksheetSales = workbook.addWorksheet("Foreign Sales", {
        pageSetup: { paperSize: 9, orientation: "landscape" },
      });

      // Add logo
      const logoImage = await this.utilityService.convertImageToBase64(
        exchange === "ZSE"
          ? "images/logos/zselogo.png"
          : "images/logos/vfexlogo.png",
      );
      const imageId = workbook.addImage({
        base64: logoImage,
        extension: "png",
      });

      worksheetPurchases.addImage(imageId, {
        tl: { col: 0, row: 1 },
        ext: { width: exchange === "ZSE" ? 201 : 254.9, height: 117 },
      });
      worksheetSales.addImage(imageId, {
        tl: { col: 0, row: 1 },
        ext: { width: exchange === "ZSE" ? 201 : 254.9, height: 117 },
      });

      // Add address and headers
      this.addAddressAndHeaders(worksheetPurchases, options, exchange, rawDate);
      this.addAddressAndHeaders(worksheetSales, options, exchange, rawDate);

      // Add trade data
      this.addTradeDataToSheet(worksheetPurchases, data.purchases);
      this.addTradeDataToSheet(worksheetSales, data.sales);

      // Add totals
      worksheetPurchases.addRow([
        "Total",
        data.purchasesStats.totalVolume,
        data.purchasesStats.totalPrice,
        data.purchasesStats.grandAmount,
      ]);
      worksheetSales.addRow([
        "Total",
        data.salesStats.totalVolume,
        data.salesStats.totalPrice,
        data.salesStats.grandAmount,
      ]);

      // Save workbook to buffer
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // Create the File object
      this.lastGeneratedFile = new File([blob], `${fileName}.xlsx`, {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // Optionally save the file
      if (shouldSaveFile) {
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = `${fileName}_${new Date(rawDate).toISOString()}.xlsx`;
        link.click();
      }
    } catch (error) {
      console.error("Error exporting to Excel:", error);
    }
  }

  // Helper function to add address and headers to a worksheet
  private addAddressAndHeaders(
    worksheet: ExcelJS.Worksheet,
    options: any,
    exchange: Exchange,
    rawDate: string,
  ): void {
    const addressText = [
      "Zimbabwe Stock Exchange Limited",
      "44 Ridgeway North Highlands Harare",
      "P O Box CY 2231 Causeway",
      "Tel: (263-4) 886830-5",
      "Email: <EMAIL>",
      "Website: www.zse.co.zw",
    ];

    let addressStartRow = 2;
    const addressColumn = 4;

    addressText.forEach((line, index) => {
      const row = worksheet.getRow(addressStartRow + index);
      const cell = row.getCell(addressColumn);

      cell.value = line;
      cell.font = { size: 10, bold: true };
      cell.alignment = { vertical: "middle", horizontal: "right" };
      cell.border = {
        top: index === 0 ? { style: "medium" } : undefined,
        bottom:
          index === addressText.length - 1 ? { style: "medium" } : undefined,
        left: { style: "medium" },
        right: { style: "medium" },
      };
    });

    worksheet.addRow([]); // Add space

    const date = new Date(rawDate).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "long",
      year: "numeric",
    });

    // Add title rows
    const titleRow1 = worksheet.addRow([options.exchangeName]);
    const titleRow2 = worksheet.addRow([options.reportTitle]);
    const titleRow3 = worksheet.addRow([date]);

    [titleRow1, titleRow2, titleRow3].forEach((row) => {
      const cell = row.getCell(1);
      cell.font = { size: 12, bold: true, color: { argb: "000000" } };
      cell.alignment = { horizontal: "right", vertical: "middle" };
    });

    worksheet.addRow([]); // Add space

    // Add headers
    const headerRow = worksheet.addRow([
      "Counter",
      "Volume",
      `Price ${exchange === Exchange.ZSE ? "(ZWG)c" : "(USD)"}`,
      `Value ${exchange === Exchange.ZSE ? "(ZWG)c" : "(USD)"}`,
    ]);
    headerRow.eachCell((cell) => {
      cell.alignment = { horizontal: "right" };
    });
  }

  // Helper function to add trade data to a sheet and auto-size columns
  private addTradeDataToSheet(
    sheet: ExcelJS.Worksheet,
    trades: ForeignTrade[],
  ): void {
    trades.forEach((trade) => {
      const row = sheet.addRow([
        trade.symbol,
        this.formatNumber(trade.volume),
        this.formatNumber(trade.price),
        this.formatNumber(trade.grandAmount),
      ]);

      // Right-align the numeric data columns
      row.getCell(2).alignment = { horizontal: "right" };
      row.getCell(3).alignment = { horizontal: "right" };
      row.getCell(4).alignment = { horizontal: "right" };
    });

    // Auto-size columns based on content
    this.autoSizeColumns(sheet);
  }

  // Function to auto-size columns for a given worksheet
  private autoSizeColumns(sheet: ExcelJS.Worksheet): void {
    sheet.columns.forEach((column) => {
      let maxLength = 0;
      column.eachCell?.({ includeEmpty: true }, (cell) => {
        const valueLength = cell.value ? cell.value.toString().length : 0;
        maxLength = Math.max(maxLength, valueLength);
      });
      column.width = maxLength + 2; // Adding padding to avoid clipping text
    });
  }

  // Helper function to format numbers with two decimal places
  private formatNumber(value: number): string {
    return value.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  }
}
