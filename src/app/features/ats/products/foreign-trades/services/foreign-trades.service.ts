import { Exchange } from "@/app/core/enums/exchange.enum";
import { Market } from "@/app/core/enums/market.enum";
import { Status } from "@/app/core/enums/status.enum";
import { ExcelExportOptions } from "@/app/core/interfaces/export.interface";
import { DateUtilsService } from "@/app/core/services/date-utils.service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { HttpClient } from "@angular/common/http";
import { inject, Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { ChangeATSProductStatusRequest } from "../../products.model";
import { ForeignTrade } from "../foreign-trades.model";
import {
  ForeignTradesExportData,
  ForeignTradesExportService,
} from "./foreign-trades-export.service";

@Injectable({
  providedIn: "root",
})
export class ForeignTradesService {
  private readonly apiServerUrl = environment.apiBaseUrl;
  private readonly http = inject(HttpClient);
  private readonly excelExportService = inject(ForeignTradesExportService);
  private readonly dateUtilsService = inject(DateUtilsService);

  public getForeignTrades(): Observable<ForeignTrade[]> {
    return this.http.get<ForeignTrade[]>(
      `${this.apiServerUrl}/ats-products/foreign-trades`,
    );
  }

  public getForeignTradesByDate(
    date: string,
    exchange: Exchange,
    status: Status,
    market: Market,
  ): any {
    return this.http.get<ForeignTrade[]>(
      `${this.apiServerUrl}/ats-products/foreign-trades?date=${date}&exchange=${exchange}&status=${status}&market=${market}`,
    );
  }

  public updateForeignTrades(market_cap: ForeignTrade[]): Observable<any[]> {
    return this.http.put<any>(
      `${this.apiServerUrl}/ats-products/foreign-trades`,
      market_cap,
    );
  }

  public async changeStatus(
    request: ChangeATSProductStatusRequest,
    foreignTrades: ForeignTradesExportData,
  ) {
    const { status, market, exchange, date } = request;
    const formattedDate = this.dateUtilsService.formatDateToISO(date);
    const formData = new FormData();

    const baseFormData = {
      exchange: exchange,
      ...(market && { market: market }),
      date: formattedDate,
      status,
      productType: request.productType,
    };

    Object.entries(baseFormData).forEach(([key, value]) => {
      formData.append(key, value);
    });

    if (status === Status.PUBLISHED) {
      const fileName = `Foreign_Trades_Report_${formattedDate}`;

      const options: ExcelExportOptions = {
        fileName,
        sheetName: "Foreign Trades Report",
        exchangeName: Exchange[exchange],
        exchange,
        reportTitle: "Foreign Trades Report",
      };

      try {
        await this.excelExportService.exportExcel({
          data: foreignTrades,
          exchange,
          fileName: `${market} Market Capitalisation Report`,
          options,
          rawDate: date,
          shouldSaveFile: false,
        });

        const file = await this.excelExportService.getLastGeneratedFile();

        if (!file) {
          throw new Error("No file generated to save");
        }

        formData.append("file", file);
        formData.append("extension", "xlsx");
      } catch (error) {
        handleError(error);
        throw error;
      }
    }

    return this.http.patch<unknown[]>(
      `${this.apiServerUrl}/ats-products/change-status`,
      formData,
    );
  }
}
