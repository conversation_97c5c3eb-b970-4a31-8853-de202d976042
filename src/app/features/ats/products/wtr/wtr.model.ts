import { Status } from "@/app/core/enums/status.enum";

export interface WtrResponse {
  wtr: Wtr;
  missingComponents: string[];
}

export interface Wtr {
  id: string;
  status: Status;
  createdDate: string;
  modifiedDate: string;
  createdUser: string;
  modifiedUser: string;
  approvedBy: string | null;
  dateApproved: string | null;
  statsDate: string;
  exchangeId: number;
  content: string;
}

export interface UpdateWtrPayload {
  id: string;
  status: Status;
}

export interface WtrContent {
  equityMarket: WeeklyStats;
  etfMarket: WeeklyStats;
  reitsMarket: WeeklyStats;
  foreignTrades: ForeignTradeStats;
  indexMovements: IndexMovementStats;
  marketCapitalisation: WeeklyValueStats;
}

export interface WeeklyStats {
  currentWeekTrades: number;
  lastWeekTrades: number;
  tradesPercentChange: number;
  currentWeekVolume: number;
  lastWeekVolume: number;
  volumePercentChange: number;
  currentWeekValue: number;
  lastWeekValue: number;
  valuePercentChange: number;
}

export interface ForeignTradeStats {
  currentWeekPurchases: number;
  lastWeekPurchases: number;
  purchasesPercentChange: number;
  currentWeekSales: number;
  lastWeekSales: number;
  salesPercentChange: number;
  netSalesOrPurchases: number;
  lastNetSalesOrPurchases: number;
}

export interface IndexMovementStats {
  allShare: IndexStats;
  top10: IndexStats;
  top15: IndexStats;
  smallCap: IndexStats;
  mediumCap: IndexStats;
  mining: IndexStats;
}

export interface IndexStats {
  currentWeek: number;
  lastWeek: number;
  percentChange: number;
}

export interface WeeklyValueStats {
  currentWeekValue: number;
  lastWeekValue: number;
  valuePercentChange: number;
}
