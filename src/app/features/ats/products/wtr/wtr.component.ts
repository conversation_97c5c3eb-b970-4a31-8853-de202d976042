import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { Market } from "@/app/core/enums/market.enum";
import { ProductLinkTable } from "@/app/core/enums/product-link-table.enum";
import { Status } from "@/app/core/enums/status.enum";
import { changeStatusByAvailableStatus } from "@/app/core/utils/change-status.util";
import { handleError } from "@/app/core/utils/error-handler.util";
import { DatePipe, DecimalPipe, JsonPipe, NgClass } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { DataTablesModule } from "angular-datatables";
import Swal from "sweetalert2";
import { ButtonGroupComponent } from "../../../../shared/ui/button/button-group/button-group.component";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { MessagesComponent } from "../../../../shared/ui/messages/messages.component";
import { SpinnerComponent } from "../../../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { ChangeATSProductStatusRequest } from "../products.model";
import { Wtr, WtrContent, WtrResponse } from "./wtr.model";
import { WtrService } from "./wtr.service";

@Component({
  selector: "app-wtr",
  templateUrl: "./wtr.component.html",
  styleUrl: "./wtr.component.css",
  standalone: true,
  imports: [
    FormsModule,
    NgClass,
    DatePipe,
    DecimalPipe,
    DataTablesModule,
    SpinnerComponent,
    MessagesComponent,
    CardComponent,
    TextComponent,
    ButtonGroupComponent,
    ButtonComponent,
    ButtonPermissionDirective,
  ],
})
export class ATSWtrComponent implements OnInit {
  wtr: Wtr | null = null;
  wtrContent!: WtrContent;
  statsDate: string | null = null;
  lastWeekDate!: Date | null;
  missingComponents: string[] = [];
  today: string = new Date().toISOString().split("T")[0];
  date: string = this.today;
  maxDate: string = this.today;
  exchange: Exchange = Exchange.VFEX;
  Exchange = Exchange;
  market: Market = Market.REG;
  status: Status = Status.DRAFT;
  Status = Status;
  Market = Market;
  loading: boolean = false;
  dtOptions: DataTables.Settings = {};
  isZse: boolean = false;

  get exchangeName(): string {
    return `${this.exchange === Exchange.ZSE ? "Zimbabwe" : "Victoria Falls"} Stock Exchange`;
  }

  get reportTitle(): string {
    return `Weekly Trading Report`;
  }

  constructor(
    private readonly wtrService: WtrService,
    private readonly route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.exchange = params["exchange"] || Exchange.VFEX;
      this.isZse = params["exchange"] === Exchange.ZSE;
    });

    this.dtOptions = {
      dom: "B",
      pageLength: -1,
    };

    this.getDataByDate();
  }

  getMissingComponentsMessage(): string {
    if (!this.missingComponents || this.missingComponents.length === 0) {
      return "No components are missing.";
    }
    return this.missingComponents.join(", ");
  }

  loadWtrData() {
    this.loading = true;
    this.wtrService
      .getWtrByDate(this.date, this.exchange, this.status)
      .subscribe(
        (res: {
          data: {
            data: WtrResponse;
            availableStatus: number;
          };
        }) => {
          this.wtr = res?.data?.data?.wtr;
          this.wtrContent = res?.data?.data?.wtr?.content
            ? JSON.parse(res?.data?.data?.wtr?.content)
            : {};

          this.missingComponents = res?.data?.data?.missingComponents;
          this.statsDate = res?.data?.data?.wtr?.statsDate;

          const statsDate = res?.data?.data?.wtr?.statsDate
            ? new Date(res?.data?.data?.wtr?.statsDate)
            : null;

          // Calculate last week date
          if (statsDate) {
            this.lastWeekDate = new Date(statsDate);
            this.lastWeekDate.setDate(statsDate.getDate() - 7);
          } else {
            this.lastWeekDate = null;
          }

          changeStatusByAvailableStatus(
            res?.data?.availableStatus,
            this.setStatus.bind(this),
          );

          this.loading = false;
        },
        (error: any) => {
          this.loading = false;
          Swal.fire(
            "Error",
            "An error occurred while fetching wtr data.",
            "error",
          );
        },
      );
  }

  async approveWtr(status: Status) {
    this.loading = true;
    const payload: ChangeATSProductStatusRequest = {
      exchange: this.exchange,
      date: this.date,
      status: status,
      productType: ProductLinkTable.WTR,
    };

    (await this.wtrService.changeStatus(payload)).subscribe({
      next: () => this.setStatus(status),
      error: (error: any) => {
        handleError(error);
        this.loading = false;
      },
    });
  }

  setStatus(status: Status) {
    if (this.status !== status) {
      // Avoid redundant status changes
      this.status = status;
      this.loadWtrData();
    }
  }

  setMarket(market: Market) {
    this.market = market;
    this.loadWtrData();
  }

  isAllowedDate(dateString: string): boolean {
    const date = new Date(dateString);
    return this.exchange === Exchange.ZSE
      ? date.getDay() === 5
      : date.getDay() === 4;
  }

  getDataByDate() {
    if (!this.isAllowedDate(this.date)) {
      Swal.fire(
        "",
        this.exchange === Exchange.ZSE
          ? "Please select a Friday."
          : "Please select a Thursday.",
        "warning",
      );

      this.date = "";
    } else {
      this.loadWtrData();
    }
  }

  exportData() {
    this.wtrService.exportData({
      date: this.date,
      exchange: this.exchange,
      reportTitle: this.reportTitle,
      exchangeName: this.exchangeName,
    });
  }
}
