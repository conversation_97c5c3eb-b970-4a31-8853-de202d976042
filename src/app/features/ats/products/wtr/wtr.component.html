<div class="flex flex-col gap-3">
  <app-text variant="title/semibold"
    >{{ exchange }} Weekly Trading Report</app-text
  >

  <div class="flex justify-between">
    <app-button-group>
      <app-button
        (click)="setStatus(Status.DRAFT)"
        size="sm"
        [variant]="status === Status.DRAFT ? 'success' : 'outline-success'"
      >
        DRAFTS
      </app-button>
      <app-button
        (click)="setStatus(Status.APPROVED)"
        size="sm"
        [variant]="status === Status.APPROVED ? 'success' : 'outline-success'"
      >
        APPROVED
      </app-button>
      <app-button
        (click)="setStatus(Status.PUBLISHED)"
        size="sm"
        [variant]="status === Status.PUBLISHED ? 'success' : 'outline-success'"
      >
        PUBLISHED
      </app-button>
    </app-button-group>

    <div class="flex items-center gap-3" style="width: fit-content">
      <span class="text-sm font-medium" style="font-size: small"> DATE </span>
      <input
        name="date"
        type="date"
        [(ngModel)]="date"
        (change)="getDataByDate()"
        [max]="maxDate"
        class="form-control"
        style="width: fit-content"
      />
    </div>
  </div>

  <div>
    @if (loading) {
      <app-spinner />
    }
  </div>

  @if (missingComponents.length > 0 && status === Status.DRAFT) {
    <app-messages
      variant="warning"
      [message]="getMissingComponentsMessage()"
      title="Publish these components inorder to see the Weekly Trading Report:"
    >
    </app-messages>
  }

  @if (missingComponents.length == 0) {
    <div class="flex items-center justify-between gap-2">
      <app-button size="sm" (click)="exportData()"> Export Data </app-button>

      <ng-container *appButtonPermission="'edit'">
        <div class="flex justify-end gap-2">
          @switch (status) {
            @case (Status.DRAFT) {
              <app-button
                size="sm"
                variant="warning"
                (click)="approveWtr(Status.APPROVED)"
              >
                Approve
              </app-button>
            }

            @case (Status.APPROVED) {
              <app-button
                size="sm"
                variant="danger"
                (click)="approveWtr(Status.DRAFT)"
              >
                Convert back to draft
              </app-button>

              <app-button
                size="sm"
                variant="success"
                (click)="approveWtr(Status.PUBLISHED)"
              >
                Publish
              </app-button>
            }

            @case (Status.PUBLISHED) {
              <app-button
                size="sm"
                variant="danger"
                (click)="approveWtr(Status.DRAFT)"
              >
                Convert back to draft
              </app-button>
            }
          }
        </div>
      </ng-container>
    </div>
  }

  <app-card id="exportDiv">
    @if (wtr) {
      <div class="mb-3">
        <app-text variant="regular/semibold" className="text-center"
          >{{ exchange }} Weekly Trading Report</app-text
        >
      </div>
    }

    <div class="table-responsive">
      @if (wtr) {
        <table
          id="wtrTable"
          class="styled-table table-hover text-nowrap table-sm"
        >
          <tr
            [ngClass]="{
              'zse-section-header': isZse,
              'vfex-section-header': !isZse,
            }"
          >
            <td></td>
            <td class="text-end">Week Ended</td>
            <td class="text-end">Week Ended</td>
            <td class="text-end">% Change</td>
          </tr>

          <tr
            [ngClass]="{
              'zse-section-header': isZse,
              'vfex-section-header': !isZse,
            }"
          >
            <td>REG</td>
            <td class="text-end">
              {{ wtr.statsDate | date: "dd/MM/yyyy" }}
            </td>
            <td class="text-end">
              {{ lastWeekDate | date: "dd/MM/yyyy" }}
            </td>
            <td class="text-end"></td>
          </tr>

          <tr>
            <td>Number of Trades</td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.equityMarket.currentWeekTrades < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.equityMarket.currentWeekTrades | number }}
            </td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.equityMarket.lastWeekTrades < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.equityMarket.lastWeekTrades | number }}
            </td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.equityMarket.tradesPercentChange < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{
                wtrContent.equityMarket.tradesPercentChange | number: "1.2-2"
              }}%
            </td>
          </tr>
          <tr>
            <td>Volume</td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.equityMarket.currentWeekVolume < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.equityMarket.currentWeekVolume | number }}
            </td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.equityMarket.lastWeekVolume < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.equityMarket.lastWeekVolume | number }}
            </td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.equityMarket.volumePercentChange < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{
                wtrContent.equityMarket.volumePercentChange | number: "1.2-2"
              }}%
            </td>
          </tr>
          <tr>
            <td>Value {{ isZse ? "(ZWGc)" : "(USD)" }}</td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.equityMarket.currentWeekValue < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.equityMarket.currentWeekValue | number: "1.2-2" }}
            </td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.equityMarket.lastWeekValue < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.equityMarket.lastWeekValue | number: "1.2-2" }}
            </td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.equityMarket.valuePercentChange < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{
                wtrContent.equityMarket.valuePercentChange | number: "1.2-2"
              }}%
            </td>
          </tr>

          @if (exchange === Exchange.ZSE) {
            <tr
              [ngClass]="{
                'zse-section-header': isZse,
                'vfex-section-header': !isZse,
              }"
            >
              <td>ETF</td>
              <td class="text-end"></td>
              <td class="text-end"></td>
              <td class="text-end"></td>
            </tr>

            <tr>
              <td>Number of Trades</td>
              <td
                class="text-end"
                [ngClass]="
                  wtrContent.etfMarket.currentWeekTrades < 0
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                {{ wtrContent.etfMarket.currentWeekTrades | number }}
              </td>
              <td
                class="text-end"
                [ngClass]="
                  wtrContent.etfMarket.lastWeekTrades < 0
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                {{ wtrContent.etfMarket.lastWeekTrades | number }}
              </td>
              <td
                class="text-end"
                [ngClass]="
                  wtrContent.etfMarket.tradesPercentChange < 0
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                {{
                  wtrContent.etfMarket.tradesPercentChange | number: "1.2-2"
                }}%
              </td>
            </tr>
            <tr>
              <td>Volume</td>
              <td
                class="text-end"
                [ngClass]="
                  wtrContent.etfMarket.currentWeekVolume < 0
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                {{ wtrContent.etfMarket.currentWeekVolume | number }}
              </td>
              <td
                class="text-end"
                [ngClass]="
                  wtrContent.etfMarket.lastWeekVolume < 0
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                {{ wtrContent.etfMarket.lastWeekVolume | number }}
              </td>
              <td
                class="text-end"
                [ngClass]="
                  wtrContent.etfMarket.volumePercentChange < 0
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                {{
                  wtrContent.etfMarket.volumePercentChange | number: "1.2-2"
                }}%
              </td>
            </tr>
            <tr>
              <td>Value {{ isZse ? "(ZWGc)" : "(USD)" }}</td>
              <td
                class="text-end"
                [ngClass]="
                  wtrContent.etfMarket.currentWeekValue < 0
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                {{ wtrContent.etfMarket.currentWeekValue | number: "1.2-2" }}
              </td>
              <td
                class="text-end"
                [ngClass]="
                  wtrContent.etfMarket.lastWeekValue < 0
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                {{ wtrContent.etfMarket.lastWeekValue | number: "1.2-2" }}
              </td>
              <td
                class="text-end"
                [ngClass]="
                  wtrContent.etfMarket.valuePercentChange < 0
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                {{ wtrContent.etfMarket.valuePercentChange | number: "1.2-2" }}%
              </td>
            </tr>
          }

          <!-- REITs section for both ZSE and VFEX -->
          <tr
            [ngClass]="{
              'zse-section-header': isZse,
              'vfex-section-header': !isZse,
            }"
          >
            <td>REITs</td>
            <td class="text-end"></td>
            <td class="text-end"></td>
            <td class="text-end"></td>
          </tr>
          <tr>
            <td>Number of Trades</td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.reitsMarket.currentWeekTrades < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.reitsMarket.currentWeekTrades | number }}
            </td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.reitsMarket.lastWeekTrades < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.reitsMarket.lastWeekTrades | number }}
            </td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.reitsMarket.tradesPercentChange < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{
                wtrContent.reitsMarket.tradesPercentChange | number: "1.2-2"
              }}%
            </td>
          </tr>
          <tr>
            <td>Volume</td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.reitsMarket.currentWeekVolume < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.reitsMarket.currentWeekVolume | number }}
            </td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.reitsMarket.lastWeekVolume < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.reitsMarket.lastWeekVolume | number }}
            </td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.reitsMarket.volumePercentChange < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{
                wtrContent.reitsMarket.volumePercentChange | number: "1.2-2"
              }}%
            </td>
          </tr>
          <tr>
            <td>Value {{ isZse ? "(ZWGc)" : "(USD)" }}</td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.reitsMarket.currentWeekValue < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.reitsMarket.currentWeekValue | number: "1.2-2" }}
            </td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.reitsMarket.lastWeekValue < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.reitsMarket.lastWeekValue | number: "1.2-2" }}
            </td>
            <td
              class="text-end"
              [ngClass]="
                wtrContent.reitsMarket.valuePercentChange < 0
                  ? 'text-danger'
                  : 'text-success'
              "
            >
              {{ wtrContent.reitsMarket.valuePercentChange | number: "1.2-2" }}%
            </td>
          </tr>

          <tr
            [ngClass]="{
              'zse-section-header': isZse,
              'vfex-section-header': !isZse,
            }"
          >
            <td></td>
            <td class="text-end">Week Ended</td>
            <td class="text-end">Week Ended</td>
            <td class="text-end">% Change</td>
          </tr>

          <tr
            [ngClass]="{
              'zse-section-header': isZse,
              'vfex-section-header': !isZse,
            }"
          >
            <td>Foreign Trades</td>
            <td class="text-end">
              {{ wtr.statsDate | date: "dd/MM/yyyy" }}
            </td>
            <td class="text-end">
              {{ lastWeekDate | date: "dd/MM/yyyy" }}
            </td>
            <td class="text-end"></td>
          </tr>

          <tr>
            <td>Purchases {{ isZse ? "(ZWGc)" : "(USD)" }}</td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger':
                  wtrContent.foreignTrades.currentWeekPurchases < 0,
                'text-success':
                  wtrContent.foreignTrades.currentWeekPurchases >= 0,
              }"
            >
              {{
                wtrContent.foreignTrades.currentWeekPurchases | number: "1.2-2"
              }}
            </td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger': wtrContent.foreignTrades.lastWeekPurchases < 0,
                'text-success': wtrContent.foreignTrades.lastWeekPurchases >= 0,
              }"
            >
              {{ wtrContent.foreignTrades.lastWeekPurchases | number: "1.2-2" }}
            </td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger':
                  wtrContent.foreignTrades.purchasesPercentChange < 0,
                'text-success':
                  wtrContent.foreignTrades.purchasesPercentChange >= 0,
              }"
            >
              {{
                wtrContent.foreignTrades.purchasesPercentChange
                  | number: "1.2-2"
              }}%
            </td>
          </tr>
          <tr>
            <td>Sales {{ isZse ? "(ZWGc)" : "(USD)" }}</td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger': wtrContent.foreignTrades.currentWeekSales < 0,
                'text-success': wtrContent.foreignTrades.currentWeekSales >= 0,
              }"
            >
              {{ wtrContent.foreignTrades.currentWeekSales | number: "1.2-2" }}
            </td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger': wtrContent.foreignTrades.lastWeekSales < 0,
                'text-success': wtrContent.foreignTrades.lastWeekSales >= 0,
              }"
            >
              {{ wtrContent.foreignTrades.lastWeekSales | number: "1.2-2" }}
            </td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger': wtrContent.foreignTrades.salesPercentChange < 0,
                'text-success':
                  wtrContent.foreignTrades.salesPercentChange >= 0,
              }"
            >
              {{
                wtrContent.foreignTrades.salesPercentChange | number: "1.2-2"
              }}%
            </td>
          </tr>
          <tr>
            <td>Net Sales / Purchases</td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger': wtrContent.foreignTrades.netSalesOrPurchases < 0,
                'text-success':
                  wtrContent.foreignTrades.netSalesOrPurchases >= 0,
              }"
            >
              {{
                wtrContent.foreignTrades.netSalesOrPurchases | number: "1.2-2"
              }}
            </td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger':
                  wtrContent.foreignTrades.lastNetSalesOrPurchases < 0,
                'text-success':
                  wtrContent.foreignTrades.lastNetSalesOrPurchases >= 0,
              }"
            >
              {{
                wtrContent.foreignTrades.lastNetSalesOrPurchases
                  | number: "1.2-2"
              }}
            </td>
            <td class="text-end"></td>
          </tr>

          <tr
            [ngClass]="{
              'zse-section-header': isZse,
              'vfex-section-header': !isZse,
            }"
          >
            <td></td>
            <td class="text-end">Week Ended</td>
            <td class="text-end">Week Ended</td>
            <td class="text-end">% Change</td>
          </tr>

          <tr
            [ngClass]="{
              'zse-section-header': isZse,
              'vfex-section-header': !isZse,
            }"
          >
            <td>Index Movements</td>
            <td class="text-end">
              {{ wtr.statsDate | date: "dd/MM/yyyy" }}
            </td>
            <td class="text-end">
              {{ lastWeekDate | date: "dd/MM/yyyy" }}
            </td>
            <td class="text-end"></td>
          </tr>

          <tr>
            <td>All Share</td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger':
                  wtrContent.indexMovements.allShare.currentWeek < 0,
                'text-success':
                  wtrContent.indexMovements.allShare.currentWeek >= 0,
              }"
            >
              {{
                wtrContent.indexMovements.allShare.currentWeek | number: "1.2-2"
              }}
            </td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger': wtrContent.indexMovements.allShare.lastWeek < 0,
                'text-success':
                  wtrContent.indexMovements.allShare.lastWeek >= 0,
              }"
            >
              {{
                wtrContent.indexMovements.allShare.lastWeek | number: "1.2-2"
              }}
            </td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger':
                  wtrContent.indexMovements.allShare.percentChange < 0,
                'text-success':
                  wtrContent.indexMovements.allShare.percentChange >= 0,
              }"
            >
              {{
                wtrContent.indexMovements.allShare.percentChange
                  | number: "1.2-2"
              }}%
            </td>
          </tr>
          @if (exchange !== Exchange.VFEX) {
            <tr>
              <td>Top 10</td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger':
                    wtrContent.indexMovements.top10.currentWeek < 0,
                  'text-success':
                    wtrContent.indexMovements.top10.currentWeek >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.top10.currentWeek | number: "1.2-2"
                }}
              </td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger': wtrContent.indexMovements.top10.lastWeek < 0,
                  'text-success': wtrContent.indexMovements.top10.lastWeek >= 0,
                }"
              >
                {{ wtrContent.indexMovements.top10.lastWeek | number: "1.2-2" }}
              </td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger':
                    wtrContent.indexMovements.top10.percentChange < 0,
                  'text-success':
                    wtrContent.indexMovements.top10.percentChange >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.top10.percentChange
                    | number: "1.2-2"
                }}%
              </td>
            </tr>
            <tr>
              <td>Top 15</td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger':
                    wtrContent.indexMovements.top15.currentWeek < 0,
                  'text-success':
                    wtrContent.indexMovements.top15.currentWeek >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.top15.currentWeek | number: "1.2-2"
                }}
              </td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger': wtrContent.indexMovements.top15.lastWeek < 0,
                  'text-success': wtrContent.indexMovements.top15.lastWeek >= 0,
                }"
              >
                {{ wtrContent.indexMovements.top15.lastWeek | number: "1.2-2" }}
              </td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger':
                    wtrContent.indexMovements.top15.percentChange < 0,
                  'text-success':
                    wtrContent.indexMovements.top15.percentChange >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.top15.percentChange
                    | number: "1.2-2"
                }}%
              </td>
            </tr>
            <tr>
              <td>Small Cap</td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger':
                    wtrContent.indexMovements.smallCap.currentWeek < 0,
                  'text-success':
                    wtrContent.indexMovements.smallCap.currentWeek >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.smallCap.currentWeek
                    | number: "1.2-2"
                }}
              </td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger':
                    wtrContent.indexMovements.smallCap.lastWeek < 0,
                  'text-success':
                    wtrContent.indexMovements.smallCap.lastWeek >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.smallCap.lastWeek | number: "1.2-2"
                }}
              </td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger':
                    wtrContent.indexMovements.smallCap.percentChange < 0,
                  'text-success':
                    wtrContent.indexMovements.smallCap.percentChange >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.smallCap.percentChange
                    | number: "1.2-2"
                }}%
              </td>
            </tr>
            <tr>
              <td>Medium Cap</td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger':
                    wtrContent.indexMovements.mediumCap.currentWeek < 0,
                  'text-success':
                    wtrContent.indexMovements.mediumCap.currentWeek >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.mediumCap.currentWeek
                    | number: "1.2-2"
                }}
              </td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger':
                    wtrContent.indexMovements.mediumCap.lastWeek < 0,
                  'text-success':
                    wtrContent.indexMovements.mediumCap.lastWeek >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.mediumCap.lastWeek | number: "1.2-2"
                }}
              </td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger':
                    wtrContent.indexMovements.mediumCap.percentChange < 0,
                  'text-success':
                    wtrContent.indexMovements.mediumCap.percentChange >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.mediumCap.percentChange
                    | number: "1.2-2"
                }}%
              </td>
            </tr>
            <tr>
              <td>Mining</td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger':
                    wtrContent.indexMovements.mining.currentWeek < 0,
                  'text-success':
                    wtrContent.indexMovements.mining.currentWeek >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.mining.currentWeek | number: "1.2-2"
                }}
              </td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger': wtrContent.indexMovements.mining.lastWeek < 0,
                  'text-success':
                    wtrContent.indexMovements.mining.lastWeek >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.mining.lastWeek | number: "1.2-2"
                }}
              </td>
              <td
                class="text-end"
                [ngClass]="{
                  'text-danger':
                    wtrContent.indexMovements.mining.percentChange < 0,
                  'text-success':
                    wtrContent.indexMovements.mining.percentChange >= 0,
                }"
              >
                {{
                  wtrContent.indexMovements.mining.percentChange
                    | number: "1.2-2"
                }}%
              </td>
            </tr>
          }

          <tr
            [ngClass]="{
              'zse-section-header': isZse,
              'vfex-section-header': !isZse,
            }"
          >
            <td></td>
            <td class="text-end">Week Ended</td>
            <td class="text-end">Week Ended</td>
            <td class="text-end">% Change</td>
          </tr>

          <tr
            [ngClass]="{
              'zse-section-header': isZse,
              'vfex-section-header': !isZse,
            }"
          >
            <td>Market Cap</td>
            <td class="text-end">
              {{ wtr.statsDate | date: "dd/MM/yyyy" }}
            </td>
            <td class="text-end">
              {{ lastWeekDate | date: "dd/MM/yyyy" }}
            </td>
            <td class="text-end"></td>
          </tr>

          <tr>
            <td>Total Market Cap {{ isZse ? "(ZWGc)" : "(USD)" }}</td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger':
                  wtrContent.marketCapitalisation.currentWeekValue < 0,
                'text-success':
                  wtrContent.marketCapitalisation.currentWeekValue >= 0,
              }"
            >
              {{
                wtrContent.marketCapitalisation.currentWeekValue
                  | number: "1.2-2"
              }}
            </td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger':
                  wtrContent.marketCapitalisation.lastWeekValue < 0,
                'text-success':
                  wtrContent.marketCapitalisation.lastWeekValue >= 0,
              }"
            >
              {{
                wtrContent.marketCapitalisation.lastWeekValue | number: "1.2-2"
              }}
            </td>
            <td
              class="text-end"
              [ngClass]="{
                'text-danger':
                  wtrContent.marketCapitalisation.valuePercentChange < 0,
                'text-success':
                  wtrContent.marketCapitalisation.valuePercentChange >= 0,
              }"
            >
              {{
                wtrContent.marketCapitalisation.valuePercentChange
                  | number: "1.2-2"
              }}%
            </td>
          </tr>
        </table>
      } @else {
        <div class="p-3 bg-body-secondary rounded-3">
          <app-text variant="small/medium" class="text-center fs-6"
            >No Data found</app-text
          >
        </div>
      }
    </div>
  </app-card>
</div>
