table {
  width: 100%;
  border-collapse: collapse;
  font-family: Arial, sans-serif;
}
th,
td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}
th {
  background-color: #4caf50;
  color: white;
}
.zse-section-header {
  background-color: #538dd5;
  color: white;
  font-weight: bold;
}

.vfex-section-header {
  background-color: #00b0f0;
  color: white;
  font-weight: bold;
}

/* Styling the table borders */
.styled-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 16px;
  text-align: left;
}

/* Styling the borders */
.styled-table th,
.styled-table td {
  border: 1px solid rgb(255, 255, 255, 0.5); /* Solid border */
  padding: 0px 16px; /* Padding inside cells */
}

/* Add a border around the entire table */
.styled-table {
  border: 3px solid #444; /* Darker outer border */
}

/* Add zebra striping to table rows */
.styled-table tbody tr:nth-of-type(even) {
  background-color: #f3f3f3;
}

/* Hover effect on rows */
.styled-table tbody tr:hover {
  background-color: #e0e0e0;
}
