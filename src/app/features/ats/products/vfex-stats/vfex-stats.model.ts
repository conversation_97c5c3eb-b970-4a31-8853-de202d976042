import { Status } from "@/app/core/enums/status.enum";

export interface VfexStatsResponse {
  vfexStats: VfexStats;
  availableStatus: number;
  missingComponents: string[];
}

export interface VfexStats {
  volume: number;
  turnoverValue: number;
  numberOfTrades: number;
  totalMarketCapitalisation: number;

  id: string;
  status: Status;
  createdDate: string;
  modifiedDate: string;
  createdUser: string;
  modifiedUser: string;
  approvedBy: string | null;
  dateApproved: string | null;
  statsDate: string;
  exchangeId: number;
}

export interface UpdateVfexStatsPayload {
  id: string;
  status: Status;
}
