import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { Market } from "@/app/core/enums/market.enum";
import { ProductLinkTable } from "@/app/core/enums/product-link-table.enum";
import { Status } from "@/app/core/enums/status.enum";
import { BillionFormatPipe } from "@/app/core/pipes/billion.pipe";
import { changeStatusByAvailableStatus } from "@/app/core/utils/change-status.util";
import { handleError } from "@/app/core/utils/error-handler.util";
import { DatePipe, DecimalPipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import Swal from "sweetalert2";
import { ButtonGroupComponent } from "../../../../shared/ui/button/button-group/button-group.component";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { MessagesComponent } from "../../../../shared/ui/messages/messages.component";
import { SpinnerComponent } from "../../../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { ChangeATSProductStatusRequest } from "../products.model";
import { VfexStats, VfexStatsResponse } from "./vfex-stats.model";
import { VfexStatsService } from "./vfex-stats.service";

@Component({
  selector: "app-vfex-stats",
  templateUrl: "./vfex-stats.component.html",
  styleUrl: "./vfex-stats.component.css",
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    DatePipe,
    DecimalPipe,
    BillionFormatPipe,
    SpinnerComponent,
    TextComponent,
    ButtonGroupComponent,
    ButtonComponent,
    MessagesComponent,
    CardComponent,
    ButtonPermissionDirective,
  ],
})
export class ATSVFEXStatsComponent implements OnInit {
  vfexStats!: VfexStats;
  missingComponents: string[] = [];
  today: string = new Date().toISOString().split("T")[0];
  date: string = this.today;
  maxDate: string = this.today;
  exchange: Exchange = Exchange.VFEX;
  Exchange = Exchange;
  status: Status = Status.DRAFT;
  Status = Status;
  Market = Market;
  loading: boolean = true;

  constructor(
    private readonly vfexStatsService: VfexStatsService,
    private readonly route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.exchange = params["exchange"] || Exchange.VFEX;
    });

    this.loadVfexStatsData();
  }

  loadVfexStatsData() {
    this.loading = true;
    this.vfexStatsService
      .getVfexStatsByDate(this.date, this.exchange, this.status)
      .subscribe(
        (res: { data: VfexStatsResponse }) => {
          this.vfexStats = res?.data?.vfexStats;
          this.missingComponents = res?.data?.missingComponents;

          changeStatusByAvailableStatus(
            res?.data?.availableStatus,
            this.setStatus.bind(this),
          );
          this.loading = false;
        },

        (error: any) => {
          this.loading = false;
          Swal.fire(
            "Error",
            "An error occurred while fetching vfexStats data.",
            "error",
          );
        },
      );
  }

  getMissingComponentsMessage(): string {
    if (!this.missingComponents || this.missingComponents.length === 0) {
      return "No components are missing.";
    }
    return this.missingComponents.join(", ");
  }

  approveVfexStats(newStatus: Status) {
    this.loading = true;
    let message: string;
    let item: string = "VFEX Stats";

    switch (newStatus) {
      case Status.DRAFT:
        message = `convert ${item} to draft?`;
        break;
      case Status.APPROVED:
        message = `approve ${item}?`;
        break;
      case Status.PUBLISHED:
        message = `publish ${item}?`;
        break;
      default:
        message = "perform this action?";
        break;
    }

    Swal.fire({
      title: "Warning",
      text: `Are you sure you want to ${message}`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
    }).then(async (result) => {
      if (result.isConfirmed) {
        const payload: ChangeATSProductStatusRequest = {
          exchange: this.exchange,
          date: this.vfexStats.statsDate,
          status: newStatus,
          productType: ProductLinkTable.VFEX_STATS,
        };

        (await this.vfexStatsService.changeStatus(payload)).subscribe({
          next: () => this.setStatus(newStatus),
          error: (error: any) => {
            handleError(error);
            this.loading = false;
          },
        });
      } else {
        this.loading = false;
      }
    });
  }

  setStatus(status: Status) {
    if (this.status !== status) {
      // Avoid redundant status changes
      this.status = status;
      this.loadVfexStatsData();
    }
  }

  getDataByDate() {
    this.loadVfexStatsData();
  }

  exportToDocx() {
    this.vfexStatsService.exportTableToDocx({
      exchange: this.exchange,
      reportTitle: "VFEX Stats",
      exchangeName: Exchange[this.exchange],
      date: this.date,
    });
  }
}
