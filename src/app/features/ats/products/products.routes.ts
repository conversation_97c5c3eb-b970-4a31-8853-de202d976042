import { Routes } from "@angular/router";

export const ATS_PRODUCTS_ROUTES: Routes = [
  {
    path: "",
    loadComponent: () =>
      import("./products.component").then((m) => m.ATSProductsComponent),
  },
  {
    path: "market-cap",
    loadComponent: () =>
      import("./market-capitalisation/market-capitalisation.component").then(
        (m) => m.ATSMarketCapitalisationComponent,
      ),
  },
  {
    path: "indices",
    loadComponent: () =>
      import("./indices/indices.component").then((m) => m.ATSIndicesComponent),
  },
  {
    path: "reits-stats",
    loadComponent: () =>
      import("./reitsStats/reits-stats.component").then(
        (m) => m.ATSREITSStatsComponent,
      ),
  },
  {
    path: "vfex-stats",
    loadComponent: () =>
      import("./vfex-stats/vfex-stats.component").then(
        (m) => m.ATSVFEXStatsComponent,
      ),
  },
  // VFEX REITs Stats now uses the same component as ZSE REITs Stats
  {
    path: "foreign-trades",
    loadComponent: () =>
      import("./foreign-trades/foreign-trades.component").then(
        (m) => m.ATSForeignTradesComponent,
      ),
  },
  {
    path: "dtr",
    loadComponent: () =>
      import("./dtr/dtr.component").then((m) => m.ATSDtrComponent),
  },
  {
    path: "wtr",
    loadComponent: () =>
      import("./wtr/wtr.component").then((m) => m.ATSWtrComponent),
  },
  {
    path: "price-sheet",
    loadComponent: () =>
      import("./price-sheet/price-sheet.component").then(
        (m) => m.ATSPriceSheetComponent,
      ),
  },
  {
    path: "market-commentary",
    loadComponent: () =>
      import("./market-commentary/market-commentary.component").then(
        (m) => m.AtsMarketCommentaryComponent,
      ),
  },
];
