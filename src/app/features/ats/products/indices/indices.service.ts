import { Exchange } from "@/app/core/enums/exchange.enum";
import { Status } from "@/app/core/enums/status.enum";
import {
  ExcelExportOptions,
  ExcelHeaderOptions,
} from "@/app/core/interfaces/export.interface";
import { DateUtilsService } from "@/app/core/services/date-utils.service";
import { ExcelExportService } from "@/app/core/services/excel-export.service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable, map } from "rxjs";
import { environment } from "src/environments/environment.development";
import { ChangeATSProductStatusRequest } from "../products.model";
import { Index } from "./indices.model";

@Injectable({
  providedIn: "root",
})
export class ATSIndicesService {
  private readonly apiUrl = `${environment.apiBaseUrl}/ats-products`;

  constructor(
    private readonly http: HttpClient,
    private readonly excelExportService: ExcelExportService,
    private readonly dateUtilsService: DateUtilsService,
  ) {}

  public getCommonIndices(date: any, exchange: String): Observable<Index[]> {
    return this.http.get<Index[]>(
      `${this.apiUrl}/indices/common?date=${date}&exchange=${exchange}`,
    );
  }

  getIndices(
    date: string,
    exchange: Exchange,
    status: Status,
  ): Observable<any> {
    const params = new HttpParams()
      .set("date", date)
      .set("exchange", exchange)
      .set("status", status);

    return this.http
      .get<{
        data: { data: any; availableStatus: any };
      }>(`${this.apiUrl}/indices`, { params })
      .pipe(
        map((response) => ({
          data: this.mapAndSortIndices(response.data?.data || []),
          availableStatus: response.data?.availableStatus || null,
        })),
      );
  }

  updateIndices(indices: Index[]): Observable<any> {
    return this.http.put(`${this.apiUrl}/indices`, indices);
  }

  public async changeStatus(
    request: ChangeATSProductStatusRequest,
    indices: Index[],
  ) {
    const { status, market, exchange, date } = request;
    const formattedDate = this.dateUtilsService.formatDateToISO(date);
    const formData = new FormData();

    const baseFormData = {
      exchange: exchange,
      ...(market && { market: market }),
      date: formattedDate,
      status,
      productType: request.productType,
    };

    Object.entries(baseFormData).forEach(([key, value]) => {
      formData.append(key, value);
    });

    if (status === Status.PUBLISHED) {
      try {
        await this.exportData({
          indices,
          exchange,
          exchangeName: Exchange[exchange],
          date,
          shouldSaveFile: false,
        });

        const file = await this.excelExportService.getLastGeneratedFile();

        if (!file) {
          throw new Error("No file generated to save");
        }

        formData.append("file", file);
        formData.append("extension", "xlsx");
      } catch (error) {
        handleError(error);
        throw error;
      }
    }

    return this.http.patch<unknown[]>(`${this.apiUrl}/change-status`, formData);
  }

  private mapAndSortIndices(data: any[]): Index[] {
    const customOrder = [
      "ALL SHARE",
      "ZSE TOP 10",
      "ZSE TOP 15",
      "SMALL CAP INDEX",
      "MID CAP INDEX",
      "MINING INDEX",
    ];

    return data.sort((a, b) => {
      const indexA = customOrder.indexOf(a.index);
      const indexB = customOrder.indexOf(b.index);
      if (indexA === -1 && indexB === -1) {
        return a.index.localeCompare(b.index);
      }
      return (
        (indexA === -1 ? Infinity : indexA) -
        (indexB === -1 ? Infinity : indexB)
      );
    });
  }

  // Export to Excel
  async exportData({
    indices,
    exchange,
    exchangeName,
    date,
    shouldSaveFile,
  }: {
    indices: Index[];
    exchange: Exchange;
    exchangeName: string;
    date: string;
    shouldSaveFile: boolean;
  }) {
    // Define headers based on the indices table columns
    const headers: ExcelHeaderOptions<any>[] = [
      { key: "index", header: "Index" },
      { key: "close", header: "Closing (Points)", alignment: "right" },
      { key: "open", header: "Previous (Points)", alignment: "right" },
      { key: "pointChange", header: "Change (Points)", alignment: "right" },
      {
        key: "percentageChange",
        header: "Price Change(%)",
        alignment: "right",
      },
    ];

    // Define export options
    const options: ExcelExportOptions = {
      fileName: `Indices_Report_${date}`,
      sheetName: "Indices Report",
      reportTitle: "Indices Report",
      exchangeName: exchangeName,
      exchange: exchange,
    };

    // Prepare data for export
    const data = indices.map((item) => ({
      index: item.index,
      close: Number(Number(item.close).toFixed(4)), // formatted as per your table
      open: Number(Number(item.open).toFixed(4)),
      pointChange: Number(parseFloat(item.pointChange).toFixed(4)),
      percentageChange: Number(parseFloat(item.percentageChange).toFixed(4)),
    }));

    const formatedDate = this.dateUtilsService.formatDateToISO(date);
    // Trigger export using the service
    await this.excelExportService.exportToExcel(
      data, // Export the prepared data
      headers, // Use the indices headers defined above
      options,
      exchange,
      formatedDate,
      5, // starting column for address
      shouldSaveFile,
    );
  }
}
