import { Status } from "@/app/core/enums/status.enum";

export interface Index {
  id: string;
  status: Status;
  createDated: string;
  modifiedDate: string;
  createdUser: string;
  modifiedUser: string;
  systemField: string;
  approvedBy: string;
  dateApproved: string;
  index: string;
  latestDate: string;
  totalCount: string;
  open: string;
  high: string;
  low: string;
  close: string;
  percentageChange: string;
  pointChange: string;
}

export interface IndicesResponse {
  data: Index[];
  availableStatus: number;
}
