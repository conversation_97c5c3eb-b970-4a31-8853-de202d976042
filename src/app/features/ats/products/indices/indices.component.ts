import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { ProductLinkTable } from "@/app/core/enums/product-link-table.enum";
import { Status } from "@/app/core/enums/status.enum";
import { changeStatusByAvailableStatus } from "@/app/core/utils/change-status.util";
import { handleError } from "@/app/core/utils/error-handler.util";
import { CardComponent } from "@/app/shared/ui/card/card.component";
import { DatePipe, DecimalPipe, NgClass } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import Swal from "sweetalert2";
import { ButtonGroupComponent } from "../../../../shared/ui/button/button-group/button-group.component";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { SpinnerComponent } from "../../../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { ChangeATSProductStatusRequest } from "../products.model";
import { Index } from "./indices.model";
import { ATSIndicesService } from "./indices.service";

@Component({
  selector: "app-indices",
  templateUrl: "./indices.component.html",
  styleUrls: ["./indices.component.css"],
  standalone: true,
  imports: [
    NgClass,
    ReactiveFormsModule,
    FormsModule,
    DecimalPipe,
    SpinnerComponent,
    TextComponent,
    ButtonComponent,
    ButtonGroupComponent,
    CardComponent,
    ButtonPermissionDirective,
    DatePipe,
  ],
  providers: [DatePipe],
})
export class ATSIndicesComponent implements OnInit {
  Status = Status;
  indices: Index[] = [];
  selectedDate: string;
  exchange: Exchange = Exchange.VFEX;
  status: Status = Status.DRAFT;
  loading = true;
  today = new Date().toISOString().split("T")[0];
  maxDate = this.today;

  editedRows: any[] = [];

  get isZse(): boolean {
    return this.exchange === Exchange.ZSE;
  }

  get exchangeName(): string {
    return `${this.exchange === Exchange.ZSE ? "Zimbabwe" : "Victoria Falls"} Stock Exchange`;
  }

  constructor(
    private indicesService: ATSIndicesService,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
  ) {
    this.selectedDate = this.datePipe.transform(new Date(), "yyyy-MM-dd") || "";
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.exchange = params["exchange"] || "VFEX";
      this.loadIndices();
    });
  }

  loadIndices(): void {
    this.loading = true;
    this.indicesService
      .getIndices(this.selectedDate, this.exchange, this.status)
      .subscribe({
        next: (res: any) => {
          this.indices = res?.data;
          changeStatusByAvailableStatus(
            res?.availableStatus,
            this.changeStatus.bind(this),
          );

          this.loading = false;
        },
        error: (error: any) => {
          if (error?.error?.message === "Wait for market to close!") {
            handleError(error, "Market is still open!", "info");
          } else {
            handleError(error);
          }
          this.loading = false;
        },
      });
  }

  updateIndices(): void {
    this.indicesService.updateIndices(this.editedRows).subscribe({
      next: () => {
        Swal.fire("Success", "Indices updated successfully", "success");
        this.loadIndices();
      },
      error: (error: any) => {
        handleError(error);
        this.loading = false;
      },
    });
  }

  markCellAsEdited(event: Event, indexItem: Index): void {
    const target = event.target as HTMLElement;
    const field = target.getAttribute("data-field") as keyof Index;
    const newValue = target.innerText.trim();

    const removeCommas = (value: string): number =>
      parseFloat(value?.replace(/,/g, "")) || 0;

    const parsedNewValue = removeCommas(newValue) as any;

    if (indexItem[field] !== parsedNewValue) {
      const editedRows = [...this.editedRows]; // Create a shallow copy

      // Check if the row has already been edited
      const existingRowIndex = editedRows.findIndex(
        (row) => row.id === indexItem.id,
      );

      if (existingRowIndex > -1) {
        // Merge the new field update with the existing row
        editedRows[existingRowIndex] = {
          ...editedRows[existingRowIndex],
          [field]: parsedNewValue,
        };
      } else {
        // Add a new entry for the edited row
        editedRows.push({
          id: indexItem.id,
          close: indexItem.close,
          open: indexItem.open,
          pointChange: indexItem.pointChange,
          [field]: parsedNewValue,
        });
      }

      this.editedRows = editedRows;
    }
  }

  async changeIndicesStatus(newStatus: Status) {
    let message: string;

    switch (newStatus) {
      case Status.DRAFT:
        message = "convert indices to draft?";
        break;
      case Status.APPROVED:
        message = "approve indices?";
        break;
      case Status.PUBLISHED:
        message = "publish indices?";
        break;
      default:
        message = "perform this action?";
        break;
    }

    Swal.fire({
      title: "Warning",
      text: `Are you sure you want to ${message}`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
    }).then(async (result) => {
      if (result.isConfirmed) {
        const payload: ChangeATSProductStatusRequest = {
          exchange: this.exchange,
          date: this.indices[0].latestDate as string,
          status: newStatus,
          productType: ProductLinkTable.INDICES,
        };

        (
          await this.indicesService.changeStatus(payload, this.indices)
        ).subscribe({
          next: () => this.changeStatus(newStatus),
          error: (error: any) => {
            handleError(error);
            this.loading = false;
          },
        });
      } else {
        this.loading = false;
      }
    });
  }

  onDateChange(): void {
    this.loadIndices();
  }

  changeStatus(status: Status): void {
    if (this.status !== status) {
      // Avoid redundant status changes
      this.status = status;
      this.loadIndices();
    }
  }

  getAbsoluteValue(value: string | null): number {
    const numValue = parseFloat(value || "0");
    return Math.abs(numValue);
  }

  parseFloat(value: string | null): number {
    return value ? parseFloat(value) : 0;
  }

  // Export to Excel
  exportData() {
    this.indicesService.exportData({
      indices: this.indices,
      exchange: this.exchange,
      exchangeName: this.exchangeName,
      date: this.selectedDate,
      shouldSaveFile: true,
    });
  }
}
