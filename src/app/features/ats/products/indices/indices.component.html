<div class="flex flex-col gap-3">
  <app-text variant="title/semibold">{{ exchange }} Indices</app-text>

  <div class="flex justify-between gap-3">
    <app-button-group>
      @for (
        itemStatus of [Status.DRAFT, Status.APPROVED, Status.PUBLISHED];
        track itemStatus
      ) {
        <app-button
          size="sm"
          (click)="changeStatus(itemStatus)"
          [variant]="status === itemStatus ? 'primary' : 'outline-primary'"
        >
          {{ itemStatus }}
        </app-button>
      }
    </app-button-group>

    <div class="flex items-center gap-3" style="width: fit-content">
      <span class="text-sm font-medium">Select Date</span>
      <input
        name="date"
        type="date"
        [max]="maxDate"
        [(ngModel)]="selectedDate"
        (change)="onDateChange()"
        class="form-control"
        style="width: fit-content"
      />
    </div>
  </div>

  @if (loading) {
    <app-spinner />
  } @else {
    @if (indices.length > 0) {
      <div class="flex items-center justify-between gap-3">
        <div class="flex justify-end gap-2">
          <app-button size="sm" variant="success" (click)="exportData()">
            Export Data
          </app-button>
        </div>

        <ng-container *appButtonPermission="'edit'">
          <div class="flex justify-end gap-3">
            @if (status !== Status.PUBLISHED) {
              <app-button size="sm" type="button" (click)="updateIndices()">
                Update Table Data
              </app-button>
            }

            @switch (status) {
              @case (Status.DRAFT) {
                <app-button
                  size="sm"
                  variant="warning"
                  (click)="changeIndicesStatus(Status.APPROVED)"
                >
                  Approve
                </app-button>
              }

              @case (Status.APPROVED) {
                <app-button
                  size="sm"
                  variant="danger"
                  (click)="changeIndicesStatus(Status.DRAFT)"
                >
                  Convert back to draft
                </app-button>

                <app-button
                  size="sm"
                  variant="success"
                  (click)="changeIndicesStatus(Status.PUBLISHED)"
                >
                  Publish
                </app-button>
              }

              @case (Status.PUBLISHED) {
                <app-button
                  size="sm"
                  variant="danger"
                  (click)="changeIndicesStatus(Status.DRAFT)"
                >
                  Convert back to draft
                </app-button>
              }
            }
          </div>
        </ng-container>
      </div>
    }

    <app-card id="exportDiv">
      <div class="pb-6">
        <app-text variant="regular/semibold" class="text-center">
          {{ isZse ? "Zimbabwe" : "Victoria Falls" }} Stock Exchange - Indices
          Report
        </app-text>
        <app-text variant="small/semibold" class="text-center">
          {{ selectedDate | date: "fullDate" }}
        </app-text>
      </div>

      <table
        [cellPadding]="5"
        class="w-full border-dark styled-table"
        id="indicesTable"
      >
        <thead>
          <tr>
            <th>Index</th>
            <th>Closing (Points)</th>
            <th>Previous (Points)</th>
            <th>Change (Points)</th>
            <th>% Change</th>
          </tr>
        </thead>
        <tbody>
          @if (indices.length > 0) {
            @for (item of indices; track item.id) {
              <tr [ngClass]="{ 'striped-row': $even }">
                <td>{{ item.index }}</td>
                <td
                  [attr.contenteditable]="status !== Status.PUBLISHED"
                  [attr.data-field]="'close'"
                  (input)="markCellAsEdited($event, item)"
                >
                  {{ item.close | number: "1.2-2" }}
                </td>
                <td
                  [attr.contenteditable]="status !== Status.PUBLISHED"
                  [attr.data-field]="'open'"
                  (input)="markCellAsEdited($event, item)"
                >
                  {{ item.open | number: "1.2-2" }}
                </td>
                <td
                  [ngClass]="{
                    'text-success': parseFloat(item.pointChange) > 0,
                    'text-danger': parseFloat(item.pointChange) <= 0,
                  }"
                >
                  @if (parseFloat(item.pointChange) > 0) {
                    {{ item.pointChange | number: "1.2-2" }}
                  } @else if (parseFloat(item.pointChange) < 0) {
                    ({{ getAbsoluteValue(item.pointChange) | number: "1.2-2" }})
                  } @else {
                    -
                  }
                </td>
                <td
                  [ngClass]="{
                    'text-success': parseFloat(item.percentageChange) > 0,
                    'text-danger': parseFloat(item.percentageChange) <= 0,
                  }"
                >
                  {{ item.percentageChange | number: "1.2-2" }}
                </td>
              </tr>
            }
          } @else {
            <tr>
              <td colspan="5" class="text-center">
                No {{ status.toLocaleLowerCase() }} Indices found.
              </td>
            </tr>
          }
        </tbody>
      </table>
    </app-card>
  }
</div>
