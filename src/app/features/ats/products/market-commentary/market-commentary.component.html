<div class="flex flex-col gap-3">
  <div class="flex justify-between">
    <app-text variant="title/semibold"
      >{{ exchange }} Market Commentary</app-text
    >
  </div>

  <div class="flex justify-between gap-3">
    <app-button-group>
      <app-button
        size="sm"
        (click)="makeTrue(1)"
        [variant]="drafts ? 'success' : 'outline-success'"
      >
        DRAFTS
      </app-button>
      <app-button
        size="sm"
        (click)="makeTrue(2)"
        [variant]="approved ? 'success' : 'outline-success'"
      >
        APPROVED
      </app-button>
      <app-button
        size="sm"
        (click)="makeTrue(3)"
        [variant]="published ? 'success' : 'outline-success'"
      >
        PUBLISHED
      </app-button>
    </app-button-group>
    <div class="flex items-center gap-3" style="width: fit-content">
      <span class="text-sm font-medium"> DATE </span>
      <input
        name="date"
        type="date"
        [(ngModel)]="selected_date"
        (change)="getMarketData()"
        [max]="maxDate"
        class="form-control sm"
        style="width: fit-content"
      />
    </div>
  </div>
  @if (drafts || approved || published) {
    <div class="flex justify-between gap-3">
      <ng-container *appButtonPermission="'edit'">
        @if (missingComponents.length === 0) {
          <div class="flex items-center gap-2">
            @if (drafts) {
              <app-button
                (click)="regenerateCommentary()"
                size="sm"
                variant="outline"
              >
                ⟳ Re-generate
              </app-button>
              @if (marketCommentary.commentary !== "") {
                <app-button
                  (click)="
                    changeCommentaryStatus(Status.APPROVED, marketCommentary.id)
                  "
                  size="sm"
                  variant="success"
                  >Approve</app-button
                >

                <app-button (click)="exportData()" size="sm" variant="success"
                  >Export
                </app-button>
              }
            }
            @if (approved) {
              <div class="flex gap-2">
                <app-button
                  (click)="
                    changeCommentaryStatus(Status.DRAFT, marketCommentary.id)
                  "
                  size="sm"
                  variant="danger"
                  >Send Back to Draft</app-button
                >
                <app-button
                  (click)="
                    changeCommentaryStatus(
                      Status.PUBLISHED,
                      marketCommentary.id
                    )
                  "
                  size="sm"
                  variant="primary"
                  >Publish</app-button
                >

                <app-button (click)="exportData()" size="sm" variant="success"
                  >Export
                </app-button>
              </div>
            }
            @if (published) {
              <app-button
                (click)="
                  changeCommentaryStatus(Status.DRAFT, marketCommentary.id)
                "
                size="sm"
                variant="danger"
                >Send Back to Draft</app-button
              >

              <app-button (click)="exportData()" size="sm" variant="success"
                >Export
              </app-button>
            }
          </div>
        }
      </ng-container>
    </div>
  }

  <div class="hidden">
    <div id="marketCommentaryExport">
      <table
        class="w-full styled-table border-dark mb-6"
        [cellPadding]="10"
        id="indicesTable"
      >
        <thead>
          <tr>
            <th>Index</th>
            <th>Closing (Points)</th>
            <th>Previous (Points)</th>
            <th>Change (Points)</th>
            <th>% Change</th>
          </tr>
        </thead>
        <tbody>
          @for (item of commonIndices; track item.id; let i = $index) {
            <tr [ngClass]="{ 'striped-row': i % 2 === 0 }">
              <td>{{ exchange === "VFEX" ? "VFEX ALL SHARE" : item.index }}</td>
              <td>{{ item.close | number: "1.2-2" }}</td>
              <td>{{ item.open | number: "1.2-2" }}</td>
              <td
                [ngClass]="{
                  'text-success': parseFloat(item.pointChange) > 0,
                  'text-danger': parseFloat(item.pointChange) <= 0,
                }"
              >
                @if (parseFloat(item.pointChange) > 0) {
                  {{ item.pointChange | number: "1.2-2" }}
                } @else if (parseFloat(item.pointChange) < 0) {
                  ({{ getAbsoluteValue(item.pointChange) | number: "1.2-2" }})
                } @else {
                  -
                }
              </td>
              <td
                [ngClass]="{
                  'text-success': parseFloat(item.percentageChange) > 0,
                  'text-danger': parseFloat(item.percentageChange) <= 0,
                }"
              >
                {{ item.percentageChange | number: "1.2-2" }}
              </td>
            </tr>
          }
        </tbody>
      </table>

      @if (drafts && missingComponents.length === 0) {
        <div [innerHTML]="sanitize(marketCommentary.commentary)"></div>
      }

      @if (approved && missingComponents.length === 0) {
        <div [innerHTML]="sanitize(approvedCommentary.commentary)"></div>
      }

      @if (published && missingComponents.length === 0) {
        <div [innerHTML]="sanitize(publishedCommentary.commentary)"></div>
      }
    </div>
  </div>

  <app-card>
    <div class="flex flex-col gap-3" id="marketCommentaryExport">
      @if (drafts || approved || published) {
        <div>
          @if (loadingIndices || loadingCommentary) {
            <app-spinner />
          }
        </div>

        @if (missingComponents.length > 0) {
          <app-messages
            variant="warning"
            [message]="getMissingComponentsMessage()"
            title="Publish these components in order to generate Market Commentary:"
          >
          </app-messages>
        }

        @if (
          commonIndices !== undefined &&
          commonIndices !== null &&
          !loadingIndices &&
          commonIndices.length > 0
        ) {
          <div class="table-responsive">
            <div class="pb-6">
              <app-text variant="regular/semibold" class="text-center">
                {{ isZse ? "Zimbabwe" : "Victoria Falls" }} Stock Exchange -
                Market Commentary Report
              </app-text>
              <app-text variant="small/semibold" class="text-center">
                {{ selected_date | date: "fullDate" }}
              </app-text>
            </div>

            <table
              class="w-full styled-table border-dark"
              [cellPadding]="10"
              id="indicesTable"
            >
              <thead>
                <tr>
                  <th>Index</th>
                  <th>Closing (Points)</th>
                  <th>Previous (Points)</th>
                  <th>Change (Points)</th>
                  <th>% Change</th>
                </tr>
              </thead>
              <tbody>
                @for (item of commonIndices; track item.id; let i = $index) {
                  <tr [ngClass]="{ 'striped-row': i % 2 === 0 }">
                    <td>
                      {{ exchange === "VFEX" ? "VFEX ALL SHARE" : item.index }}
                    </td>
                    <td>{{ item.close | number: "1.2-2" }}</td>
                    <td>{{ item.open | number: "1.2-2" }}</td>
                    <td
                      [ngClass]="{
                        'text-success': parseFloat(item.pointChange) > 0,
                        'text-danger': parseFloat(item.pointChange) <= 0,
                      }"
                    >
                      @if (parseFloat(item.pointChange) > 0) {
                        {{ item.pointChange | number: "1.2-2" }}
                      } @else if (parseFloat(item.pointChange) < 0) {
                        ({{
                          getAbsoluteValue(item.pointChange) | number: "1.2-2"
                        }})
                      } @else {
                        -
                      }
                    </td>
                    <td
                      [ngClass]="{
                        'text-success': parseFloat(item.percentageChange) > 0,
                        'text-danger': parseFloat(item.percentageChange) <= 0,
                      }"
                    >
                      {{ item.percentageChange | number: "1.2-2" }}
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </div>
        }

        @if (drafts && missingComponents.length === 0) {
          <form (submit)="updateCommentary()">
            <div class="card">
              <div class="card-body">
                <div class="NgxEditor__Wrapper">
                  <ngx-editor-menu [editor]="editor"> </ngx-editor-menu>
                  <ngx-editor
                    [editor]="editor"
                    [(ngModel)]="marketCommentary.commentary"
                    name="commentary"
                    [disabled]="false"
                    [contentEditable]="true"
                    [placeholder]="''"
                  >
                  </ngx-editor>
                </div>
                <div class="flex justify-end pt-3">
                  @if (marketCommentary.commentary !== "") {
                    <app-button type="submit" size="sm">
                      Update Commentary
                    </app-button>
                  }
                </div>
              </div>
            </div>
          </form>
        }
        @if (approved && missingComponents.length === 0) {
          <form (submit)="updateCommentary()">
            <div class="card">
              <div class="card-body">
                <div class="NgxEditor__Wrapper">
                  <ngx-editor-menu [editor]="editor"> </ngx-editor-menu>
                  <ngx-editor
                    [editor]="editor"
                    [(ngModel)]="approvedCommentary.commentary"
                    name="commentary"
                    [disabled]="false"
                    [contentEditable]="true"
                    [placeholder]="''"
                  >
                  </ngx-editor>
                </div>
                <div class="flex justify-end">
                  @if (marketCommentary.status !== "PUBLISHED") {
                    <app-button type="submit" class="btn primary">
                      Update Commentary
                    </app-button>
                  }
                </div>
              </div>
            </div>
          </form>
        }
        @if (published && missingComponents.length === 0) {
          <div class="card">
            <div class="card-body">
              <div class="NgxEditor__Wrapper">
                <ngx-editor-menu [editor]="editor"> </ngx-editor-menu>
                <ngx-editor
                  [editor]="editor"
                  [(ngModel)]="publishedCommentary.commentary"
                  name="commentary"
                  disabled
                  [contentEditable]="true"
                  [placeholder]="''"
                >
                </ngx-editor>
              </div>
            </div>
          </div>
        }
      }
    </div>
  </app-card>
</div>
