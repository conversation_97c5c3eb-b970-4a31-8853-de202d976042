import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { ProductLinkTable } from "@/app/core/enums/product-link-table.enum";
import { Status } from "@/app/core/enums/status.enum";
import { AdminUser } from "@/app/core/models/userInfo.model";
import { handleError } from "@/app/core/utils/error-handler.util";
import { AuthService } from "@/app/features/auth/core/services/auth.service";
import { DatePipe, DecimalPipe, NgClass } from "@angular/common";
import { Component, inject } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { DomSanitizer, SafeHtml } from "@angular/platform-browser";
import { ActivatedRoute } from "@angular/router";
import { Editor, NgxEditorModule } from "ngx-editor";
import Swal from "sweetalert2";
import { ButtonGroupComponent } from "../../../../shared/ui/button/button-group/button-group.component";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { MessagesComponent } from "../../../../shared/ui/messages/messages.component";
import { SpinnerComponent } from "../../../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { Index } from "../indices/indices.model";
import { ATSIndicesService } from "../indices/indices.service";
import { ChangeATSProductStatusRequest } from "../products.model";
import { MarketCommentary } from "./market-commentary.model";
import { MarketCommentaryService } from "./market-commentary.service";

@Component({
  selector: "app-ats-market-commentary",
  standalone: true,
  imports: [
    NgxEditorModule,
    FormsModule,
    NgClass,
    DecimalPipe,
    SpinnerComponent,
    ButtonComponent,
    ButtonGroupComponent,
    TextComponent,
    MessagesComponent,
    CardComponent,
    DatePipe,
    ButtonPermissionDirective,
  ],
  templateUrl: "./market-commentary.component.html",
  styleUrl: "./market-commentary.component.css",
})
export class AtsMarketCommentaryComponent {
  commonIndices: Index[] | null = null;
  loadingIndices: boolean = false;
  loadingCommentary: boolean = false;
  drafts: boolean = true;
  approved: boolean = false;
  published: boolean = false;
  selected_date: any;
  missingComponents: string[] = [];
  commentaryId: string = "";
  Status = Status;

  private readonly sanitizer = inject(DomSanitizer);

  exchange: Exchange = Exchange.VFEX;
  exchangeId: any;
  user?: AdminUser | null;

  editor: Editor | any;
  html: string = "";
  date: string = new Date().toLocaleDateString();
  time: string = new Date().toLocaleTimeString();

  marketCommentary: MarketCommentary = {
    id: "",
    status: "DRAFT",
    createdDate: "",
    modifiedDate: "",
    createdUser: "",
    modifiedUser: "",
    systemField: "",
    commentary: "",
    approvedBy: "",
    dateApproved: "",
    statsDate: "",
    exchangeId: 0,
  };

  approvedCommentary: MarketCommentary = {
    id: "",
    status: "DRAFT",
    createdDate: "",
    modifiedDate: "",
    createdUser: "",
    modifiedUser: "",
    systemField: "",
    commentary: "",
    approvedBy: "",
    dateApproved: "",
    statsDate: "",
    exchangeId: 0,
  };

  publishedCommentary: MarketCommentary = {
    id: "",
    status: "DRAFT",
    createdDate: "",
    modifiedDate: "",
    createdUser: "",
    modifiedUser: "",
    systemField: "",
    commentary: "",
    approvedBy: "",
    dateApproved: "",
    statsDate: "",
    exchangeId: 0,
  };

  commentaries: MarketCommentary[] = [];
  draftCommentaries: MarketCommentary[] = [];
  approvedCommentaries: MarketCommentary[] = [];
  publishedCommentaries: MarketCommentary[] = [];

  maxDate: string;

  tempCommentary: any;
  availableStatus: number = 1;

  parseFloat(value: string | null): number {
    return value ? parseFloat(value) : 0;
  }

  active: string = "";
  activeText: string = "text-dark";

  get isZse(): boolean {
    return this.exchange === Exchange.ZSE;
  }

  constructor(
    private marketCommentaryService: MarketCommentaryService,
    private route: ActivatedRoute,
    private datePipe: DatePipe,
    private authService: AuthService,
    private indicesService: ATSIndicesService,
  ) {
    this.selected_date = this.datePipe.transform(new Date(), "yyyy-MM-dd");
    this.maxDate = this.selected_date;
    this.user = this.authService.currentUser;
  }
  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.exchange = params["exchange"];
    });

    this.editor = new Editor();
    this.getCommentary();
    this.getCommonIndices();
  }
  ngOnDestroy(): void {
    this.editor.destroy();
  }

  sanitize(html: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  getMissingComponentsMessage(): string {
    if (!this.missingComponents || this.missingComponents.length === 0) {
      return "No components are missing.";
    }
    return this.missingComponents.join(", ");
  }

  putCommentaryInStatusList(commentary: any) {
    if (commentary.status == "DRAFT") {
      this.marketCommentary.commentary = commentary.commentary;
      this.makeTrue(1);
    }

    if (commentary.status == "APPROVED") {
      this.approvedCommentary.commentary = commentary.commentary;
      this.makeTrue(2);
    }

    if (commentary.status == "PUBLISHED") {
      this.publishedCommentary.commentary = commentary.commentary;
      this.makeTrue(3);
    }
  }

  changeCommentaryStatus(newStatus: Status, commentaryId: string): void {
    this.loadingCommentary = true;

    let message: string;
    let item: string = "commentary";

    switch (newStatus) {
      case Status.DRAFT:
        message = `convert ${item} to draft?`;
        break;
      case Status.APPROVED:
        message = `approve ${item}?`;
        break;
      case Status.PUBLISHED:
        message = `publish ${item}?`;
        break;
      default:
        message = "perform this action?";
        break;
    }

    Swal.fire({
      title: "Warning",
      text: `Are you sure you want to ${message}`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
    }).then(async (result) => {
      if (result.isConfirmed) {
        const payload: ChangeATSProductStatusRequest = {
          exchange: this.exchange,
          date: this.selected_date,
          status: newStatus,
          productType: ProductLinkTable.MARKET_COMMENTARY,
        };

        (await this.marketCommentaryService.changeStatus(payload)).subscribe({
          next: () => this.getCommentary(),
          error: (error: any) => {
            handleError(error);
            this.loadingCommentary = false;
          },
        });
      } else {
        this.loadingCommentary = false;
      }
    });
  }

  makeTrue(tab: number) {
    if (tab == 1) {
      this.drafts = true;
      this.approved = false;
      this.published = false;
    }

    if (tab == 2) {
      this.drafts = false;
      this.approved = true;
      this.published = false;
    }

    if (tab == 3) {
      this.drafts = false;
      this.approved = false;
      this.published = true;
    }
  }

  getAbsoluteValue(value: string | null): number {
    const numValue = parseFloat(value || "0");
    return Math.abs(numValue);
  }

  regenerateCommentary() {
    Swal.fire({
      title: "Are you sure?",
      text: `If you regenerate market commentary it will overide the saved draft.`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes",
      cancelButtonText: "Cancel",
    }).then((result) => {
      this.getGenerateMarketCommentary();
    });
  }

  getCommonIndices() {
    this.loadingIndices = true;
    this.indicesService
      .getCommonIndices(this.selected_date, this.exchange)
      .subscribe((res: any) => {
        this.commonIndices = res.data;
        this.loadingIndices = false;
      });
  }

  getCommentary() {
    this.loadingCommentary = true;
    this.resetCommentaryFields();

    this.marketCommentaryService
      .getMarketCommentary(this.selected_date, this.exchange)
      .subscribe(
        (response: any) => {
          this.missingComponents = response.data?.missingComponents;
          this.commentaryId = response?.data?.marketCommentary?.id;
          this.availableStatus =
            response?.data?.marketCommentary?.availableStatus;

          if (response.data.marketCommentary === null) {
            this.getGenerateMarketCommentary();
          } else {
            this.putCommentaryInStatusList(response.data.marketCommentary);
          }

          this.marketCommentary.statsDate = this.selected_date;

          this.loadingCommentary = false;
        },
        (error) => {
          console.error(error);
        },
      );
  }

  resetCommentaryFields() {
    this.marketCommentary.commentary = "";
    this.approvedCommentary.commentary = "";
    this.publishedCommentary.commentary = "";
  }

  getGenerateMarketCommentary() {
    this.marketCommentaryService
      .getGeneratedMarketCommentary(this.selected_date, this.exchange)
      .subscribe(
        (response: any) => {
          this.marketCommentary.commentary =
            response.data.marketCommentary.commentary;
          this.marketCommentary.statsDate = this.selected_date;
          this.commentaryId = response?.data?.marketCommentary?.id;
        },
        (error) => {
          console.error(error);
        },
      );
  }

  updateCommentary() {
    const payload = {
      id: this.commentaryId,
      commentary:
        this.marketCommentary.commentary !== ""
          ? this.marketCommentary.commentary
          : this.approvedCommentary.commentary,
    };

    this.marketCommentaryService.updateCommentary(payload).subscribe((res) => {
      Swal.fire("Success", "Commentary has been saved successfully", "success");
    });
  }

  getMarketData() {
    this.getCommentary();
    this.getCommonIndices();
  }

  exportData() {
    this.marketCommentaryService.exportData({
      date: this.date,
      exchange: this.exchange,
      reportTitle: "Market Commentary",
      exchangeName: Exchange[this.exchange],
    });
  }
}
