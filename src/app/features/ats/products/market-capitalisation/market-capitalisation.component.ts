import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { Market } from "@/app/core/enums/market.enum";
import { ProductLinkTable } from "@/app/core/enums/product-link-table.enum";
import { Status } from "@/app/core/enums/status.enum";
import { changeStatusByAvailableStatus } from "@/app/core/utils/change-status.util";
import { getMarket, getStatus } from "@/app/core/utils/common.util";
import { handleError } from "@/app/core/utils/error-handler.util";
import {
  MarketCapitalisation,
  MarketCapStats,
} from "@/app/features/ats/products/market-capitalisation/market-capitalisation.model";
import { DatePipe, DecimalPipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { ButtonModule } from "primeng/button";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { Table, TableModule } from "primeng/table";
import Swal from "sweetalert2";
import { ButtonGroupComponent } from "../../../../shared/ui/button/button-group/button-group.component";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { SpinnerComponent } from "../../../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { ChangeATSProductStatusRequest } from "../products.model";
import { MarketCapService } from "./market-cap.service";

@Component({
  selector: "app-market-capitalisation",
  templateUrl: "./market-capitalisation.component.html",
  styleUrl: "./market-capitalisation.component.css",
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    DatePipe,
    DecimalPipe,
    IconFieldModule,
    InputIconModule,
    TableModule,
    ButtonModule,
    TextComponent,
    ButtonComponent,
    ButtonGroupComponent,
    CardComponent,
    SpinnerComponent,
    ButtonPermissionDirective,
  ],
})
export class ATSMarketCapitalisationComponent implements OnInit {
  marketCap: MarketCapitalisation[] = [];
  editedRows: any[] = [];

  searchValue: string | undefined;

  today: string = new Date().toISOString().split("T")[0];
  date: string;
  maxDate: string;
  exchange: Exchange = Exchange.VFEX;
  Exchange = Exchange;
  market: Market = Market.REG;

  status: Status = Status.DRAFT;
  Status = Status;
  Market = Market;
  loading: boolean = true;

  equities: boolean = true;
  etfs: boolean = false;

  stats?: MarketCapStats;
  index!: number;

  isZse: boolean = false;

  get exchangeName(): string {
    return `${this.exchange === Exchange.ZSE ? "Zimbabwe" : "Victoria Falls"} Stock Exchange`;
  }

  get reportTitle(): string {
    return `${this.getMarket(this.market)} Market Capitalisation Report`;
  }

  constructor(
    private readonly marketCapService: MarketCapService,
    private readonly route: ActivatedRoute,
  ) {
    this.date = this.today;
    this.maxDate = this.today;
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.exchange = params["exchange"];
      this.isZse = params["exchange"] === Exchange.ZSE;
    });

    this.loadMarketCapData();
  }

  loadMarketCapData() {
    this.loading = true;
    this.marketCapService
      .getMarketCapitalisationByDate({
        date: this.date,
        exchange: this.exchange,
        market: this.market,
        status: this.status,
      })
      .subscribe(
        (res: any) => {
          this.marketCap = res?.data?.marketCap || [];
          this.stats = res?.data?.stats || {};
          this.index = res?.data?.index || 0;

          changeStatusByAvailableStatus(
            res?.data?.availableStatus,
            this.setStatus.bind(this),
          );

          this.loading = false;
        },
        (error: any) => {
          if (error?.error?.message === "Wait for market to close!") {
            handleError(error, "Market is still open!", "info");
          } else {
            handleError(error);
          }
          this.loading = false;
        },
      );
  }

  getMarketDataByDate() {
    this.loading = true;
    this.loadMarketCapData();
  }

  setStatus(status: Status) {
    if (this.status !== status) {
      // Avoid redundant status changes
      this.status = status;
      this.loadMarketCapData();
    }
  }

  setMarket(market: Market) {
    this.market = market;
    this.loadMarketCapData();
  }

  async changeStatus(newStatus: Status) {
    this.loading = true;
    let message: string;
    let item: string = "market cap";

    switch (newStatus) {
      case Status.DRAFT:
        message = `convert ${item} to draft?`;
        break;
      case Status.APPROVED:
        message = `approve ${item}?`;
        break;
      case Status.PUBLISHED:
        message = `publish ${item}?`;
        break;
      default:
        message = "perform this action?";
        break;
    }

    Swal.fire({
      title: "Warning",
      text: `Are you sure you want to ${message}`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
    }).then(async (result) => {
      if (result.isConfirmed) {
        const payload: ChangeATSProductStatusRequest = {
          market: this.market,
          exchange: this.exchange,
          date: this.marketCap[0].statsDate,
          status: newStatus,
          productType: ProductLinkTable.MARKET_CAPITALISATION,
        };

        (
          await this.marketCapService.changeStatus(payload, this.marketCap)
        ).subscribe({
          next: () => this.setStatus(newStatus),
          error: (error: any) => {
            handleError(error);
            this.loading = false;
          },
        });
      } else {
        this.loading = false;
      }
    });
  }

  updateMarketCap() {
    this.loading = true;
    this.marketCapService.updateMarketCap(this.editedRows).subscribe(
      () => {
        Swal.fire("Success", "Market Capitalization list updated!", "success");
        this.getMarketDataByDate();
      },
      (error: any) => {
        handleError(error);
        this.loading = false;
      },
    );
  }

  onDateChange(): void {
    this.loadMarketCapData();
  }

  markCellAsEdited(event: Event, item: MarketCapitalisation): void {
    const target = event.target as HTMLElement;
    const field = target.getAttribute(
      "data-field",
    ) as keyof MarketCapitalisation;
    const newValue = target.innerText.trim();

    const removeCommas = (value: string): number =>
      parseFloat(value?.replace(/,/g, "")) || 0;

    const parsedNewValue = removeCommas(newValue) as any;

    if (item[field] !== parsedNewValue) {
      const editedRows = [...this.editedRows]; // Create a shallow copy

      // Check if the row has already been edited
      const existingRowIndex = editedRows.findIndex(
        (row) => row.id === item.id,
      );

      if (existingRowIndex > -1) {
        // Merge the new field update with the existing row
        editedRows[existingRowIndex] = {
          ...editedRows[existingRowIndex],
          [field]: parsedNewValue,
        };
      } else {
        // Add a new entry for the edited row
        editedRows.push({
          id: item.id,
          outstandingShares: item.outstandingShares,
          [field]: parsedNewValue,
        });
      }

      this.editedRows = editedRows;
    }
  }

  protected getStatus(status: Status): string {
    return getStatus(status);
  }

  protected getMarket(market: Market): string {
    return getMarket(market);
  }

  exportData() {
    this.marketCapService.exportData({
      marketCap: this.marketCap,
      exchange: this.exchange,
      reportTitle: this.reportTitle,
      exchangeName: this.exchangeName,
      market: this.market,
      date: this.date,
    });
  }

  clear(table: Table) {
    table.clear();
    this.searchValue = "";
  }
}
