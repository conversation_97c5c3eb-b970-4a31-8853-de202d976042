import { Exchange } from "@/app/core/enums/exchange.enum";
import { Market } from "@/app/core/enums/market.enum";
import { ProductLinkTable } from "@/app/core/enums/product-link-table.enum";
import { Status } from "@/app/core/enums/status.enum";

export interface MarketCapitalisation {
  id: string;
  status: string;
  companyStatus: string;
  createdDate: string;
  modifiedDate: string;
  createdUser: string;
  modifiedUser: string;
  systemField: string;
  approvedBy: string;
  dateApproved: string;
  statsDate: string;
  companyName: string;
  symbol: string;
  closingPrice: number;
  outstandingShares: number;
  marketCapitalisation: number;
  exchangeId: number;
  marketId: number;
  index: number;
}

export interface UpdateMarketCapRequest {
  id: Market;
  outStandingShares: number;
}

export interface MarketCapStats {
  issuedShares: number;
  marketCap: number;
  index: number;
}
