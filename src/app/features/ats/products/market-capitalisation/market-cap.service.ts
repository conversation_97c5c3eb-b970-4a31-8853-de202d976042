// market-cap.service.ts
import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";

import { Exchange } from "@/app/core/enums/exchange.enum";
import { Market } from "@/app/core/enums/market.enum";
import { Status } from "@/app/core/enums/status.enum";
import {
  ExcelExportOptions,
  ExcelHeaderOptions,
} from "@/app/core/interfaces/export.interface";
import { DateUtilsService } from "@/app/core/services/date-utils.service";
import { ExcelExportService } from "@/app/core/services/excel-export.service";
import { handleError } from "@/app/core/utils/error-handler.util";
import {
  MarketCapitalisation,
  UpdateMarketCapRequest,
} from "@/app/features/ats/products/market-capitalisation/market-capitalisation.model";
import { environment } from "@/environments/environment.development";
import { ChangeATSProductStatusRequest } from "../products.model";

interface MarketCapQuery {
  date: string;
  exchange: Exchange;
  market: Market;
  status: Status;
}

@Injectable({
  providedIn: "root",
})
export class MarketCapService {
  private readonly API_BASE_URL = `${environment.apiBaseUrl}/ats-products`;

  constructor(
    private readonly httpClient: HttpClient,
    private readonly excelExportService: ExcelExportService,
    private readonly dateUtilsService: DateUtilsService,
  ) {}

  public getMarketCapitalisationByDate({
    date,
    exchange,
    market,
    status,
  }: MarketCapQuery): Observable<MarketCapitalisation[]> {
    const params = new HttpParams()
      .set("date", date)
      .set("exchange", exchange)
      .set("market", market)
      .set("status", status);

    return this.httpClient.get<MarketCapitalisation[]>(
      `${this.API_BASE_URL}/market-capitalisation`,
      {
        params,
      },
    );
  }

  public updateMarketCap(marketCap: UpdateMarketCapRequest[]) {
    return this.httpClient.put<any>(
      `${this.API_BASE_URL}/market-capitalisation`,
      marketCap,
    );
  }

  public async changeStatus(
    request: ChangeATSProductStatusRequest,
    marketCap: MarketCapitalisation[],
  ) {
    const { status, market, exchange, date } = request;
    const formattedDate = this.dateUtilsService.formatDateToISO(date);
    const formData = new FormData();

    const baseFormData = {
      exchange: exchange,
      ...(market && { market: market }),
      date: formattedDate,
      status,
      productType: request.productType,
    };

    Object.entries(baseFormData).forEach(([key, value]) => {
      formData.append(key, value);
    });

    if (status === Status.PUBLISHED) {
      try {
        await this.exportData({
          marketCap,
          exchange,
          reportTitle: `${market} Market Capitalisation Report`,
          exchangeName: Exchange[exchange],
          market: market ?? Market.REG,
          date: formattedDate,
          shouldSaveFile: false,
        });

        const file = await this.excelExportService.getLastGeneratedFile();

        if (!file) {
          throw new Error("No file generated to save");
        }

        formData.append("file", file);
        formData.append("extension", "xlsx");
      } catch (error) {
        handleError(error);
        throw error;
      }
    }

    return this.httpClient.patch<unknown[]>(
      `${this.API_BASE_URL}/change-status`,
      formData,
    );
  }

  public async exportData({
    marketCap,
    exchange,
    reportTitle,
    exchangeName,
    market,
    date,
    shouldSaveFile,
  }: {
    marketCap: MarketCapitalisation[];
    exchange: Exchange;
    reportTitle: string;
    exchangeName: string;
    market: Market;
    date: string;
    shouldSaveFile?: boolean;
  }): Promise<void> {
    const headers = this.buildExcelHeaders(market, exchange);
    const options = this.buildExcelOptions({
      date,
      exchangeName,
      exchange,
      reportTitle,
    });

    const formatedDate = this.dateUtilsService.formatDateForReports(date);

    await this.excelExportService.exportToExcel(
      marketCap,
      headers,
      options,
      exchange,
      formatedDate,
      5,
      shouldSaveFile,
    );
  }

  private buildExcelHeaders(
    market: Market,
    exchange: Exchange,
  ): ExcelHeaderOptions<MarketCapitalisation>[] {
    const headers: ExcelHeaderOptions<MarketCapitalisation>[] = [
      {
        key: "companyName",
        header: market === Market.ETF ? "ETF ISSUER" : "COMPANY",
      },
      { key: "symbol", header: "SHORT NAME" },
      { key: "companyStatus", header: "LISTING STATUS" },
      { key: "outstandingShares", header: "ISSUED SHARES", alignment: "right" },
      { key: "marketCapitalisation", header: "MARKET CAP", alignment: "right" },
    ];

    if (market === Market.ETF && exchange === Exchange.ZSE) {
      headers.splice(4, 0, { key: "index", header: "ZSE ETF INDEX" });
    }

    return headers;
  }

  private buildExcelOptions({
    date,
    exchangeName,
    exchange,
    reportTitle,
  }: {
    date: string;
    exchangeName: string;
    exchange: Exchange;
    reportTitle: string;
  }): ExcelExportOptions {
    return {
      fileName: `Market_Capitalisation_${date}`,
      sheetName: "Market Cap Report",
      exchangeName,
      exchange,
      reportTitle,
      footer: "Confidential - For internal use only",
      author: "Your Company",
    };
  }
}
