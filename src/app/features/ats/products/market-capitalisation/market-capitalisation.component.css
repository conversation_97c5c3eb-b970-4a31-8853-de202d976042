:host ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
  border-bottom: 1px solid #ddd;
  border-top: none;
}

:host ::ng-deep .p-datatable .p-datatable-thead > tr > th {
  border-bottom: 1px solid #ddd;
  border-top: 1px solid #ddd;
}

:host ::ng-deep .p-datatable .p-datatable-tfoot > tr > th {
  border-bottom: 1px solid #ddd;
  border-top: 1px solid #ddd;
  padding: 6px;
}

:host ::ng-deep .p-datatable .p-datatable-header {
  border-bottom: none;
  border-top: 1px solid #ddd;
}

th.p-element.p-sortable-column.p-highlight {
  background: transparent;
}

.border-bottom {
  border-bottom: 1px solid #ddd;
}
