<div class="flex flex-col gap-3">
  <app-text variant="title/semibold"
    >{{ exchange }} Market Capitalisation</app-text
  >
  <div class="flex justify-between gap-3">
    <app-button-group>
      @for (
        itemStatus of [Status.DRAFT, Status.APPROVED, Status.PUBLISHED];
        track itemStatus
      ) {
        <app-button
          size="sm"
          (click)="setStatus(itemStatus)"
          [variant]="status === itemStatus ? 'primary' : 'outline-primary'"
        >
          {{ itemStatus }}
        </app-button>
      }
    </app-button-group>

    <div class="flex items-center gap-3" style="width: fit-content">
      <span class="text-sm font-medium">Select Date</span>
      <input
        name="date"
        type="date"
        [max]="maxDate"
        [(ngModel)]="date"
        (change)="onDateChange()"
        class="form-control"
        style="width: fit-content"
      />
    </div>
  </div>

  @if (loading) {
    <app-spinner />
  } @else {
    <div class="flex justify-between gap-3">
      <app-button-group>
        <app-button
          (click)="setMarket(Market.REG)"
          size="sm"
          [variant]="market === Market.REG ? 'warning' : 'outline-warning'"
        >
          EQUITIES
        </app-button>

        @if (isZse) {
          <app-button
            (click)="setMarket(Market.ETF)"
            size="sm"
            [variant]="market === Market.ETF ? 'warning' : 'outline-warning'"
          >
            ETFS
          </app-button>

          <app-button
            (click)="setMarket(Market.REIT)"
            size="sm"
            [variant]="market === Market.REIT ? 'warning' : 'outline-warning'"
          >
            REITS
          </app-button>
        }

        @if (!isZse) {
          <app-button
            (click)="setMarket(Market.BOND)"
            size="sm"
            [variant]="market === Market.BOND ? 'warning' : 'outline-warning'"
          >
            BOND
          </app-button>

          <app-button
            (click)="setMarket(Market.REIT)"
            size="sm"
            [variant]="market === Market.REIT ? 'warning' : 'outline-warning'"
          >
            REITS
          </app-button>
        }
      </app-button-group>

      <div class="flex justify-end gap-2">
        <ng-container *appButtonPermission="'edit'">
          @if (status !== Status.PUBLISHED) {
            <app-button size="sm" (click)="updateMarketCap()">
              Update Table Data
            </app-button>
          }

          @switch (status) {
            @case (Status.DRAFT) {
              <app-button
                size="sm"
                variant="warning"
                (click)="changeStatus(Status.APPROVED)"
              >
                Approve
              </app-button>
            }

            @case (Status.APPROVED) {
              <app-button
                size="sm"
                variant="danger"
                (click)="changeStatus(Status.DRAFT)"
              >
                Convert back to draft
              </app-button>

              <app-button
                size="sm"
                variant="success"
                (click)="changeStatus(Status.PUBLISHED)"
              >
                Publish
              </app-button>
            }

            @case (Status.PUBLISHED) {
              <app-button
                size="sm"
                variant="danger"
                (click)="changeStatus(Status.DRAFT)"
              >
                Convert back to draft
              </app-button>
            }
          }
        </ng-container>
      </div>
    </div>

    <app-card id="exportDiv">
      <div class="mb-2 border-t">
        <div class="py-3 w-100 border-bottom">
          <app-text variant="regular/semibold" className="text-center">
            {{ isZse ? "Zimbabwe" : "Victoria Falls" }} Stock Exchange -
            {{ getStatus(status) }} {{ getMarket(market) }} Market
            Capitalisation Report
          </app-text>
          <app-text variant="small/semibold" className="text-center">
            {{ date | date: "fullDate" }}
          </app-text>
        </div>

        <div class="flex items-center justify-between pt-2">
          <div class="flex items-center justify-between gap-2">
            <app-button size="sm" class="flex gap-2" (click)="clear(dt)">
              <i class="pi pi-filter-slash"></i>
              <span>Clear</span>
            </app-button>

            @if (marketCap.length > 0) {
              <div class="flex justify-end gap-2">
                <app-button size="sm" variant="success" (click)="exportData()">
                  Export Data
                </app-button>
              </div>
            }
          </div>

          <div>
            <input
              class="form-control"
              pInputText
              type="text"
              [(ngModel)]="searchValue"
              (input)="dt.filterGlobal($any($event.target).value, 'contains')"
              placeholder="Search keyword"
            />
          </div>
        </div>
      </div>

      <p-table
        id="marketCapTable"
        #dt
        [value]="marketCap"
        dataKey="id"
        sortField="companyStatus"
        [rows]="10"
        [rowsPerPageOptions]="[10, 25, 50]"
        [paginator]="false"
        [globalFilterFields]="[
          'symbol',
          'companyName',
          'outstandingShares',
          'marketCapitalisation',
        ]"
        [tableStyle]="{ 'min-width': '50rem' }"
        styleClass="p-datatable-sm"
      >
        <ng-template pTemplate="header">
          <tr
            class="text-xs font-semibold tracking-wider text-gray-600 uppercase bg-gray-100 text-nowrap"
          >
            <th>#</th>
            <th>
              <div
                class="flex items-center gap-2"
                pSortableColumn="companyName"
              >
                <span>{{
                  market === Market.ETF ? "ETF ISSUER" : "COMPANY"
                }}</span>
                <p-sortIcon field="companyName"></p-sortIcon>
              </div>
            </th>
            <th>
              <div class="flex items-center gap-2" pSortableColumn="symbol">
                <span>SHORT NAME</span>
                <p-sortIcon field="symbol"></p-sortIcon>
              </div>
            </th>
            <th>
              <div
                class="flex items-center gap-2"
                pSortableColumn="companyStatus"
              >
                <span>LISTING STATUS</span>
                <p-sortIcon field="companyStatus"></p-sortIcon>
              </div>
            </th>
            <th>
              <div
                class="flex items-center justify-end gap-2"
                pSortableColumn="outstandingShares"
              >
                <span>ISSUED SHARES</span>
                <p-sortIcon field="outstandingShares"></p-sortIcon>
              </div>
            </th>
            <th>
              <div
                class="flex items-center justify-end gap-2"
                pSortableColumn="closingPrice"
              >
                <span>CLOSING PRICE</span>
                <p-sortIcon field="closingPrice"></p-sortIcon>
              </div>
            </th>
            @if (market === Market.ETF && exchange === Exchange.ZSE) {
              <th>
                <div class="flex justify-end">ZSE ETF INDEX</div>
              </th>
            }
            <th>
              <div
                class="flex items-center justify-end gap-2"
                pSortableColumn="marketCapitalisation"
              >
                <span>MARKET CAP</span>
                <p-sortIcon field="marketCapitalisation"></p-sortIcon>
              </div>
            </th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
          <tr>
            <td>{{ rowIndex + 1 }}</td>
            <td>
              {{ item.companyName }}
            </td>
            <td>
              {{ item.symbol }}
            </td>
            <td>
              {{ item.companyStatus }}
            </td>
            <td
              class="text-end"
              contenteditable="true"
              [attr.data-field]="'outstandingShares'"
              (input)="markCellAsEdited($event, item)"
            >
              {{ item.outstandingShares | number: "1.0-0" }}
            </td>
            <td
              class="text-end"
              contenteditable="true"
              [attr.data-field]="'closingPrice'"
              (input)="markCellAsEdited($event, item)"
            >
              {{ item.closingPrice | number: "1.2-2" }}
            </td>
            @if (market === Market.ETF && exchange === Exchange.ZSE) {
              <td class="text-end">
                {{ index | number: "1.2-4" }}
              </td>
            }
            <td class="text-end">
              {{ item.marketCapitalisation | number: "1.0-0" }}
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="footer">
          <tr>
            <th colspan="4" class="text-start">GRAND TOTAL</th>
            <th class="text-end">
              {{ stats?.issuedShares | number: "1.2-2" }}
            </th>
            <th></th>
            @if (market === Market.ETF && exchange === Exchange.ZSE) {
              <th></th>
            }
            <th class="text-end">
              {{ stats?.marketCap | number: "1.2-2" }}
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <tr>
            <td
              [attr.colspan]="market === Market.ETF ? 8 : 7"
              class="text-center"
            >
              No data found
            </td>
          </tr>
        </ng-template>
      </p-table>
    </app-card>
  }
</div>
