<div class="flex items-center justify-between gap-3">
  <app-button-group>
    <app-button
      (click)="setExchange(Exchange.VFEX)"
      [variant]="exchange === Exchange.VFEX ? 'primary' : 'outline-primary'"
    >
      VFEX
    </app-button>

    <app-button
      (click)="setExchange(Exchange.ZSE)"
      [variant]="exchange === Exchange.ZSE ? 'primary' : 'outline-primary'"
      >ZSE</app-button
    >
  </app-button-group>

  @if (exchange === Exchange.VFEX) {
    <app-text variant="title/semibold" className="text-primary">
      {{ exchange }} Market Products
    </app-text>
  } @else {
    <app-text variant="title/semibold" className="text-primary">
      {{ exchange }} Market Products
    </app-text>
  }
</div>

@if (isRegenerating) {
  <app-spinner size="md" />
} @else {
  <div class="flex flex-col gap-6 mt-6">
    @if (exchange === Exchange.VFEX) {
      <div
        class="grid grid-cols-1 gap-3 lg:gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
      >
        @for (product of vfexProducts; track product) {
          <a
            [routerLink]="[product.link]"
            [queryParams]="{ exchange: exchange }"
          >
            <app-card
              className="hover:bg-primary transition-transform duration-300 transform group rounded-xl"
            >
              <app-text
                variant="regular/semibold"
                className="text-primary group-hover:text-background transition-transform duration-300 transform"
              >
                {{ product.title }}
              </app-text>
            </app-card>
          </a>
        }
      </div>
    } @else {
      <div
        class="grid grid-cols-1 gap-3 lg:gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
      >
        @for (product of zseProducts; track product) {
          <a
            [routerLink]="[product.link]"
            [queryParams]="{ exchange: exchange }"
          >
            <app-card
              className="hover:bg-primary transition-transform duration-300 transform group rounded-xl"
            >
              <app-text
                variant="regular/semibold"
                className="text-primary group-hover:text-background transition-transform duration-300 transform"
              >
                {{ product.title }}
              </app-text>
            </app-card>
          </a>
        }
      </div>
    }
  </div>

  <!-- ATS Notification Summary -->
  <app-ats-notification></app-ats-notification>
}

<app-button
  (click)="onRegenerateClick()"
  [disabled]="isRegenerating"
  variant="outline"
  size="sm"
>
  @if (isRegenerating) {
    Regenerating...
  } @else {
    ⟳ Regenerate {{ exchange }} Market Data
  }
</app-button>

<!-- Regenerate Confirmation Modal -->
<app-modal
  [isVisible]="isRegenerateModalVisible"
  [config]="{
    title: 'Regenerate Market Data',
    cancelText: 'Cancel',
    confirmText: isRegenerating
      ? 'Regenerating...'
      : isRegenerateFormValid
        ? 'Regenerate Data'
        : 'Complete Form to Continue',
    confirmDisabled: !isRegenerateFormValid || isRegenerating,
  }"
  (visibilityChange)="isRegenerateModalVisible = $event"
  (cancel)="onCloseRegenerateModal()"
  (confirm)="onConfirmRegenerate()"
  size="md"
>
  <form [formGroup]="regenerateForm" class="flex flex-col gap-6">
    <!-- Warning Message -->
    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg
            class="h-5 w-5 text-yellow-400"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-yellow-800">
            Warning: Data Regeneration
          </h3>
          <div class="mt-2 text-sm text-yellow-700">
            <p>
              This action will regenerate all DRAFT {{ exchange }} market data
              for
              @if (regenerateForm.get("date")?.value) {
                <strong>{{
                  formatDateToHumanReadable(regenerateForm.get("date")?.value)
                }}</strong>
              } @else {
                the selected date
              }
              .
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Date Field -->
    <app-form-field
      label="Date"
      type="date"
      [control]="regenerateForm.controls['date']"
      [required]="true"
      placeholder="Select date for regeneration"
    >
    </app-form-field>

    <!-- Confirmation Phrase Field -->
    <div class="space-y-2">
      <div class="relative">
        <label
          for="confirmationPhraseInput"
          class="block mb-2 text-sm font-medium text-foreground"
        >
          To confirm, type "{{ CONFIRMATION_PHRASE }}" in the box below
          <span class="ml-1 text-danger">*</span>
        </label>
        <input
          id="confirmationPhraseInput"
          type="text"
          [formControl]="$any(regenerateForm.controls['confirmationPhrase'])"
          autocomplete="off"
          spellcheck="false"
          class="block w-full px-4 py-2 text-sm transition-colors duration-200 ease-in-out border rounded-lg h-9 text-foreground bg-background border-input placeholder:text-muted focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 disabled:bg-light disabled:cursor-not-allowed"
          [class.border-danger]="
            regenerateForm.controls['confirmationPhrase'].touched &&
            regenerateForm.controls['confirmationPhrase'].invalid
          "
          (paste)="$event.preventDefault()"
          (drop)="$event.preventDefault()"
          (dragover)="$event.preventDefault()"
          (contextmenu)="$event.preventDefault()"
          (keydown)="onConfirmationPhraseKeydown($event)"
        />
        @if (
          regenerateForm.controls["confirmationPhrase"].touched &&
          regenerateForm.controls["confirmationPhrase"].invalid
        ) {
          <div class="mt-1 text-sm text-red-600">
            @if (
              regenerateForm.controls["confirmationPhrase"].hasError("required")
            ) {
              Confirmation phrase is required
            }
            @if (
              regenerateForm.controls["confirmationPhrase"].hasError(
                "invalidPhrase"
              )
            ) {
              Confirmation phrase does not match
            }
          </div>
        }
      </div>
    </div>

    <!-- Form Validation Errors -->
    @if (
      regenerateForm.get("confirmationPhrase")?.hasError("invalidPhrase") &&
      regenerateForm.get("confirmationPhrase")?.touched
    ) {
      <div class="text-red-600 text-sm">
        Confirmation phrase does not match. Please type exactly:
        {{ CONFIRMATION_PHRASE }}
      </div>
    }
  </form>
</app-modal>
