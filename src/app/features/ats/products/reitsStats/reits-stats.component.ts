import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { Market } from "@/app/core/enums/market.enum";
import { ProductLinkTable } from "@/app/core/enums/product-link-table.enum";
import { Status } from "@/app/core/enums/status.enum";
import { changeStatusByAvailableStatus } from "@/app/core/utils/change-status.util";
import { handleError } from "@/app/core/utils/error-handler.util";
import { DatePipe, DecimalPipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import Swal from "sweetalert2";
import { ButtonGroupComponent } from "../../../../shared/ui/button/button-group/button-group.component";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { MessagesComponent } from "../../../../shared/ui/messages/messages.component";
import { SpinnerComponent } from "../../../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { ChangeATSProductStatusRequest } from "../products.model";
import { ReitsStats, ReitsStatsResponse } from "./reits-stats.model";
import { ReitsStatsService } from "./reits-stats.service";

@Component({
  selector: "app-reits-stats",
  templateUrl: "./reits-stats.component.html",
  styleUrl: "./reits-stats.component.css",
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    DatePipe,
    DecimalPipe,
    SpinnerComponent,
    CardComponent,
    TextComponent,
    ButtonComponent,
    MessagesComponent,
    ButtonGroupComponent,
    ButtonPermissionDirective,
  ],
})
export class ATSREITSStatsComponent implements OnInit {
  reitsStats!: ReitsStats;
  missingComponents: string[] = [];
  today: string = new Date().toISOString().split("T")[0];
  date: string = this.today;
  maxDate: string = this.today;
  exchange: Exchange = Exchange.VFEX;
  Exchange = Exchange;
  market: Market = Market.REG;
  status: Status = Status.DRAFT;
  Status = Status;
  Market = Market;
  loading: boolean = true;

  constructor(
    private reitsStatsService: ReitsStatsService,
    private route: ActivatedRoute,
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.exchange = params["exchange"] || Exchange.VFEX;
    });

    this.loadReitsStatsData();
  }

  loadReitsStatsData() {
    this.loading = true;
    this.reitsStatsService
      .getReitsStatsByDate(this.date, this.exchange, this.status)
      .subscribe(
        (res: { data: ReitsStatsResponse }) => {
          this.reitsStats = res?.data?.reitsStats;
          this.missingComponents = res?.data?.missingComponents;

          changeStatusByAvailableStatus(
            res?.data?.availableStatus,
            this.setStatus.bind(this),
          );
          this.loading = false;
        },

        (error: any) => {
          this.loading = false;
          Swal.fire(
            "Error",
            "An error occurred while fetching reitsStats data.",
            "error",
          );
        },
      );
  }

  getMissingComponentsMessage(): string {
    if (!this.missingComponents || this.missingComponents.length === 0) {
      return "No components are missing.";
    }
    return this.missingComponents.join(", ");
  }

  approveReitsStats(newStatus: Status) {
    this.loading = true;
    let message: string;
    let item: string = "REITS Stats";

    switch (newStatus) {
      case Status.DRAFT:
        message = `convert ${item} to draft?`;
        break;
      case Status.APPROVED:
        message = `approve ${item}?`;
        break;
      case Status.PUBLISHED:
        message = `publish ${item}?`;
        break;
      default:
        message = "perform this action?";
        break;
    }

    Swal.fire({
      title: "Warning",
      text: `Are you sure you want to ${message}`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
    }).then(async (result) => {
      if (result.isConfirmed) {
        const payload: ChangeATSProductStatusRequest = {
          exchange: this.exchange,
          date: this.reitsStats.statsDate,
          status: newStatus,
          productType: ProductLinkTable.REITS_STATS,
        };

        (await this.reitsStatsService.changeStatus(payload)).subscribe({
          next: () => this.setStatus(newStatus),
          error: (error: any) => {
            handleError(error);
            this.loading = false;
          },
        });
      } else {
        this.loading = false;
      }
    });
  }

  setStatus(status: Status) {
    if (this.status !== status) {
      // Avoid redundant status changes
      this.status = status;
      this.loadReitsStatsData();
    }
  }

  setMarket(market: Market) {
    this.market = market;
    this.loadReitsStatsData();
  }

  getDataByDate() {
    this.loadReitsStatsData();
  }

  exportToDocx() {
    this.reitsStatsService.exportTableToDocx({
      exchange: this.exchange,
      reportTitle: "REITs Stats",
      exchangeName: Exchange[this.exchange],
      date: this.date,
    });
  }
}
