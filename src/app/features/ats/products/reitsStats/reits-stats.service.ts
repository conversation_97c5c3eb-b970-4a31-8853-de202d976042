import { Exchange } from "@/app/core/enums/exchange.enum";
import { Status } from "@/app/core/enums/status.enum";
import { UtilityService } from "@/app/core/services/utility.service";
import { formatDate } from "@/app/core/utils/format.util";
import { HttpClient } from "@angular/common/http";
import { inject, Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { ReitsStats, UpdateReitsStatsPayload } from "./reits-stats.model";

import { saveAs } from "file-saver";

import { DateUtilsService } from "@/app/core/services/date-utils.service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { asBlob } from "html-docx-js-typescript";
import { ChangeATSProductStatusRequest } from "../products.model";

@Injectable({
  providedIn: "root",
})
export class ReitsStatsService {
  private readonly baseUrl = `${environment.apiBaseUrl}`;
  private readonly utilityService = inject(UtilityService);
  private readonly http = inject(HttpClient);
  private readonly dateUtilsService = inject(DateUtilsService);

  private lastGeneratedFile: Blob | null = null; // Store the last generated file

  public getReitsStatsByDate(
    date: string,
    exchange: Exchange,
    status: Status,
  ): any {
    return this.http.get<ReitsStats[]>(
      `${this.baseUrl}/ats-products/reits-stats?date=${date}&exchange=${exchange}&status=${status}`,
    );
  }

  public updateReitsStats(payload: UpdateReitsStatsPayload): Observable<any[]> {
    return this.http.put<any>(
      `${this.baseUrl}/ats-products/reits-stats`,
      payload,
    );
  }

  public async changeStatus(request: ChangeATSProductStatusRequest) {
    const { status, market, exchange, date } = request;
    const formattedDate = this.dateUtilsService.formatDateToISO(date);
    const formData = new FormData();

    const baseFormData = {
      exchange: exchange,
      ...(market && { market: market }),
      date: formattedDate,
      status,
      productType: request.productType,
    };

    Object.entries(baseFormData).forEach(([key, value]) => {
      formData.append(key, value);
    });

    if (status === Status.PUBLISHED) {
      try {
        await this.exportTableToDocx({
          exchange,
          reportTitle: "REITs Stats",
          exchangeName: Exchange[exchange],
          date: formattedDate,
          shouldSaveFile: false,
        });

        const file = await this.getLastGeneratedFile();

        if (!file) {
          throw new Error("No file generated to save");
        }

        formData.append("file", file);
        formData.append("extension", "docx");
      } catch (error) {
        handleError(error);
        throw error;
      }
    }

    return this.http.patch<unknown[]>(
      `${this.baseUrl}/ats-products/change-status`,
      formData,
    );
  }

  public getLastGeneratedFile(): Blob | null {
    return this.lastGeneratedFile;
  }

  async exportTableToDocx({
    date,
    exchange,
    reportTitle,
    exchangeName,
    shouldSaveFile = true,
  }: {
    date: string;
    exchange: Exchange;
    reportTitle: string;
    exchangeName: string;
    shouldSaveFile?: boolean;
  }) {
    const tableElement = document.getElementById("reitsStatsTable");
    const formattedDate = formatDate(date);

    const logoUrl = await this.utilityService.convertImageToBase64(
      exchange === Exchange.ZSE
        ? "images/logos/zselogo.png"
        : "images/logos/vfexlogo.png",
    );

    if (tableElement) {
      // Define Word-compatible styles
      const cssStyles = `
        <style>
          @page { size: landscape; margin: 1in; }
          body { font-family: Arial, sans-serif; margin: 0; padding: 0; }
          table { border-collapse: collapse; width: 100%; margin-bottom: 10pt; }
          td { padding: 5pt; vertical-align: top; }
          .header-table { margin-bottom: 20pt; width: 100%; table-layout: fixed; }
          .logo-cell { width: 30%; padding: 10pt; vertical-align: top; }
          .logo-container { width: 150px; height: 60px; overflow: hidden; text-align: left; }
          .logo-image { max-width: 150px; height: auto; display: block; }
          .contact-info { font-size: 11pt; text-align: right; width: 70%; }
          .data-table { border: 1pt solid #ddd; font-size: 10pt; }
          .data-table td, .data-table th { border: 1pt solid #ddd; padding: 2pt 4pt 0 4pt; }
          .data-table th { font-weight: bold; }
        </style>
      `;

      const headerHTML = `
        <table class="header-table">
          <tr>
            <td class="logo-cell">
              <div class="logo-container">
                <img src="${logoUrl}" alt="Logo" class="logo-image" style="width: 150px; height: auto;" />
              </div>
            </td>
            <td style="width: 70%;">
              <div class="contact-info">
                <p style="margin: 0;">Zimbabwe Stock Exchange Limited<br>
                44 Ridgeway North, Highlands, Harare<br>
                P O Box CY 2231, Causeway<br>
                <strong>Tel:</strong> (263-4) 886830-5<br>
                <strong>Website:</strong> www.zse.co.zw</p>
              </div>
            </td>
          </tr>
        </table>
      `;

      const titleHtml = `
        <table style="margin-bottom: 15pt;">
          <tr>
            <td style="text-align: center;">
              <p style="font-size: 14pt; font-weight: bold; margin: 0;">${reportTitle}</p>
              <p style="font-size: 12pt; margin: 5pt 0 0 0;">${formattedDate}</p>
            </td>
          </tr>
        </table>
      `;

      // Clone and modify the table element
      const tableClone = tableElement.cloneNode(true) as HTMLTableElement;
      tableClone.className = "data-table";

      const tableHtml = `
        <div style="margin-top: 15pt;">
          ${tableClone.outerHTML}
        </div>
      `;

      const htmlContent = `
        <!DOCTYPE html>
        <html>
          <head>
            ${cssStyles}
          </head>
          <body>
            ${headerHTML}
            ${titleHtml}
            ${tableHtml}
          </body>
        </html>
      `;

      const options = {
        orientation: "landscape" as const,
        margins: {
          top: "1in",
          right: "1in",
          bottom: "1in",
          left: "1in",
        },
      };

      try {
        const result = await asBlob(htmlContent, options as any);
        if (result instanceof Blob) {
          this.lastGeneratedFile = result;

          if (shouldSaveFile) {
            saveAs(result, `REITs_STATS_${date}.docx`);
          }
        } else {
          console.error("Expected a Blob, but received a different format.");
        }
      } catch (error) {
        console.error("Error converting to DOCX:", error);
      }
    } else {
      console.error("Table not found!");
    }
  }
}
