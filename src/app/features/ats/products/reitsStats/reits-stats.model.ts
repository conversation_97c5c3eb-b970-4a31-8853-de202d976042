import { Status } from "@/app/core/enums/status.enum";

export interface ReitsStatsResponse {
  reitsStats: ReitsStats;
  availableStatus: number;
  missingComponents: string[];
}

export interface ReitsStats {
  volume: number;
  turnoverValue: number;
  numberOfTrades: number;
  totalMarketCapitalisation: number;

  id: string;
  status: Status;
  createdDate: string;
  modifiedDate: string;
  createdUser: string;
  modifiedUser: string;
  approvedBy: string | null;
  dateApproved: string | null;
  statsDate: string;
  exchangeId: number;
}

export interface UpdateReitsStatsPayload {
  id: string;
  status: Status;
}
