<div class="flex flex-col gap-3">
  <app-text variant="title/semibold">{{ exchange }} Reits Stats</app-text>

  <div class="flex justify-between gap-3">
    <app-button-group>
      <app-button
        (click)="setStatus(Status.DRAFT)"
        size="sm"
        [variant]="status === Status.DRAFT ? 'primary' : 'outline-primary'"
      >
        DRAFTS
      </app-button>
      <app-button
        (click)="setStatus(Status.APPROVED)"
        size="sm"
        [variant]="status === Status.APPROVED ? 'primary' : 'outline-primary'"
      >
        APPROVED
      </app-button>
      <app-button
        (click)="setStatus(Status.PUBLISHED)"
        size="sm"
        [variant]="status === Status.PUBLISHED ? 'primary' : 'outline-primary'"
      >
        PUBLISHED
      </app-button>
    </app-button-group>

    <div class="flex items-center gap-3" style="width: fit-content">
      <span class="text-sm font-medium" style="font-size: small"> DATE </span>
      <input
        name="date"
        type="date"
        [(ngModel)]="date"
        (change)="getDataByDate()"
        [max]="maxDate"
        class="form-control"
        style="width: fit-content"
      />
    </div>
  </div>

  <div>
    @if (loading) {
      <app-spinner />
    }
  </div>

  @if (missingComponents.length > 0 && status === Status.DRAFT) {
    <app-messages
      variant="warning"
      [message]="getMissingComponentsMessage()"
      title="Publish these components inorder to see the REITs Stats:"
    >
    </app-messages>
  }

  @if (reitsStats !== null) {
    <div class="flex items-center justify-between gap-2">
      <div class="flex justify-end gap-2">
        <app-button size="sm" (click)="exportToDocx()"> Export </app-button>
      </div>

      <ng-container *appButtonPermission="'edit'">
        <div class="flex justify-end gap-2">
          @switch (status) {
            @case (Status.DRAFT) {
              <app-button
                size="sm"
                variant="warning"
                (click)="approveReitsStats(Status.APPROVED)"
              >
                Approve
              </app-button>
            }

            @case (Status.APPROVED) {
              <app-button
                size="sm"
                variant="danger"
                (click)="approveReitsStats(Status.DRAFT)"
              >
                Convert back to draft
              </app-button>

              <app-button
                size="sm"
                variant="success"
                (click)="approveReitsStats(Status.PUBLISHED)"
              >
                Publish
              </app-button>
            }

            @case (Status.PUBLISHED) {
              <app-button
                size="sm"
                variant="danger"
                (click)="approveReitsStats(Status.DRAFT)"
              >
                Convert back to draft
              </app-button>
            }
          }
        </div>
      </ng-container>
    </div>
  }

  <app-card id="exportDiv">
    @if (reitsStats) {
      <table id="reitsStatsTable" class="styled-table text-nowrap table-sm">
        <thead>
          <tr>
            <th>Date</th>
            <th>Turnover</th>
            <th>Volume</th>
            <th>Number of Trades</th>
            <th>Market Cap</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>{{ reitsStats.statsDate | date: "dd/MM/yyyy" }}</td>
            <td>
              {{ exchange === Exchange.ZSE ? "ZWG" : "USD" }}
              {{ reitsStats.turnoverValue | number: "1.2-2" }}
            </td>
            <td>{{ reitsStats.volume | number }}</td>
            <td>{{ reitsStats.numberOfTrades | number }}</td>
            <td>
              {{ exchange === Exchange.ZSE ? "ZWG" : "USD" }}
              {{ reitsStats.totalMarketCapitalisation | number: "1.2-2" }}
            </td>
          </tr>
        </tbody>
      </table>
    } @else {
      <div class="p-3 bg-body-secondary rounded-3">
        <app-text variant="small/medium" class="text-center fs-6"
          >No Data found</app-text
        >
      </div>
    }
  </app-card>
</div>
