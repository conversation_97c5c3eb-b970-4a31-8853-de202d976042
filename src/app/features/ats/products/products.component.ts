import {
  AtsProduct,
  VFEX_ATS_PRODUCTS,
  ZSE_ATS_PRODUCTS,
} from "@/app/core/constants/data";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { FormFieldComponent } from "@/app/shared/ui/form-field/form-field.component";
import { ModalComponent } from "@/app/shared/ui/modal/modal.component";
import { Component, OnInit } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router, RouterLink } from "@angular/router";
import { ButtonGroupComponent } from "../../../shared/ui/button/button-group/button-group.component";
import { ButtonComponent } from "../../../shared/ui/button/button.component";
import { CardComponent } from "../../../shared/ui/card/card.component";
import { TextComponent } from "../../../shared/ui/text/text.component";
import { AtsNotificationComponent } from "../../dashboard/components/ats-notification/ats-notification.component";
import { ATSProductsService } from "./ats-products.service";
import { SpinnerComponent } from "../../../shared/ui/spinner/spinner.component";

@Component({
  selector: "app-products",
  templateUrl: "./products.component.html",
  styleUrls: ["./products.component.css"],
  standalone: true,
  imports: [
    RouterLink,
    ButtonComponent,
    ButtonGroupComponent,
    CardComponent,
    TextComponent,
    AtsNotificationComponent,
    ReactiveFormsModule,
    ModalComponent,
    FormFieldComponent,
    SpinnerComponent,
  ],
})
export class ATSProductsComponent implements OnInit {
  exchange: Exchange = Exchange.VFEX;
  Exchange = Exchange;
  vfexProducts: AtsProduct[] = VFEX_ATS_PRODUCTS;
  zseProducts: AtsProduct[] = ZSE_ATS_PRODUCTS;

  // Modal and form properties
  isRegenerateModalVisible = false;
  isRegenerating = false;
  regenerateForm: FormGroup;
  readonly CONFIRMATION_PHRASE = "REGENERATE ALL MARKET DATA";

  /**
   * Check if the regenerate form is valid and confirmation phrase matches
   */
  get isRegenerateFormValid(): boolean {
    if (!this.regenerateForm) return false;

    const confirmationPhrase =
      this.regenerateForm.get("confirmationPhrase")?.value;
    const isFormValid = this.regenerateForm.valid;
    const isPhraseMatch = confirmationPhrase === this.CONFIRMATION_PHRASE;

    return isFormValid && isPhraseMatch;
  }

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private atsProductsService: ATSProductsService,
  ) {
    // Initialize the regenerate form
    this.regenerateForm = this.fb.group({
      date: [this.getCurrentDate(), [Validators.required]],
      confirmationPhrase: [
        "",
        [Validators.required, this.confirmationPhraseValidator.bind(this)],
      ],
    });
  }

  ngOnInit(): void {
    // Check if exchange exists in URL params first
    this.route.queryParams.subscribe((params) => {
      const exchangeParam = params["exchange"];
      if (!exchangeParam) {
        // If no exchange in URL, set the default one
        this.setExchange(this.exchange);
      } else {
        this.exchange = exchangeParam as Exchange;
      }
    });
  }

  setExchange(exchange: Exchange) {
    this.exchange = exchange;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { exchange: exchange },
      queryParamsHandling: "merge",
    });
  }

  /**
   * Open the regenerate confirmation modal
   */
  onRegenerateClick(): void {
    this.isRegenerateModalVisible = true;
    // Reset form when opening modal
    this.regenerateForm.patchValue({
      date: this.getCurrentDate(),
      confirmationPhrase: "",
    });
  }

  /**
   * Close the regenerate modal
   */
  onCloseRegenerateModal(): void {
    this.isRegenerateModalVisible = false;
    this.regenerateForm.reset();
  }

  /**
   * Handle the regenerate confirmation
   */
  onConfirmRegenerate(): void {
    if (this.regenerateForm.invalid) {
      this.regenerateForm.markAllAsTouched();
      return;
    }

    const { date, confirmationPhrase } = this.regenerateForm.value;

    if (confirmationPhrase !== this.CONFIRMATION_PHRASE) {
      showToast({
        message:
          "Confirmation phrase does not match. Please type exactly: " +
          this.CONFIRMATION_PHRASE,
        type: "error",
      });
      return;
    }

    this.isRegenerating = true;

    this.atsProductsService.regenerateATSData(date, this.exchange).subscribe({
      next: () => {
        showToast({
          message: `${this.exchange} market data regenerated successfully!`,
          type: "success",
        });
        this.isRegenerating = false;
        this.onCloseRegenerateModal();
      },
      error: (error) => {
        handleError(error, "Failed to regenerate market data");
        this.isRegenerating = false;
      },
    });
  }

  /**
   * Custom validator for confirmation phrase
   */
  confirmationPhraseValidator(control: any) {
    const value = control.value;
    if (value && value !== this.CONFIRMATION_PHRASE) {
      return { invalidPhrase: true };
    }
    return null;
  }

  /**
   * Handle keydown events on confirmation phrase input to prevent certain shortcuts
   */
  onConfirmationPhraseKeydown(event: KeyboardEvent): void {
    // Prevent Ctrl+V (paste), Ctrl+A (select all), Ctrl+C (copy), Ctrl+X (cut)
    if (
      event.ctrlKey &&
      ["v", "a", "c", "x"].includes(event.key.toLowerCase())
    ) {
      event.preventDefault();
      return;
    }

    // Prevent F12 (developer tools), Ctrl+Shift+I (developer tools)
    if (
      event.key === "F12" ||
      (event.ctrlKey && event.shiftKey && event.key === "I")
    ) {
      event.preventDefault();
      return;
    }
  }

  /**
   * Get current date in YYYY-MM-DD format for input field
   */
  private getCurrentDate(): string {
    const today = new Date();
    return today.toISOString().split("T")[0];
  }

  /**
   * Format date to human readable format (e.g., "3 June 2025")
   */
  formatDateToHumanReadable(dateString: string): string {
    if (!dateString) return "";

    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      day: "numeric",
      month: "long",
      year: "numeric",
    };

    return date.toLocaleDateString("en-GB", options);
  }
}
