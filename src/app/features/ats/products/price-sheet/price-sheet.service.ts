import { Exchange } from "@/app/core/enums/exchange.enum";
import { Market } from "@/app/core/enums/market.enum";
import { Status } from "@/app/core/enums/status.enum";
import {
  PriceSheet,
  PriceSheetStats,
} from "@/app/features/ats/products/price-sheet/price-sheet.model";
import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";

import {
  ExcelExportOptions,
  ExcelHeaderOptions,
} from "@/app/core/interfaces/export.interface";
import { ApiResponse } from "@/app/core/models/apiResponse";
import { DateUtilsService } from "@/app/core/services/date-utils.service";
import { ExcelExportService } from "@/app/core/services/excel-export.service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { ChangeATSProductStatusRequest } from "../products.model";

interface ExportDataParams {
  stats: PriceSheetStats;
  priceSheet: PriceSheet[];
  exchange: Exchange;
  exchangeName: string;
  market: Market;
  date: string;
  reportTitle: string;
  shouldSaveFile?: boolean;
}

@Injectable({
  providedIn: "root",
})
export class PriceSheetService {
  private readonly apiServerUrl = environment.apiBaseUrl;
  constructor(
    private readonly http: HttpClient,
    private readonly excelExportService: ExcelExportService,
    private readonly dateUtilsService: DateUtilsService,
  ) {}

  public getPriceSheet(): Observable<PriceSheet[]> {
    return this.http.get<PriceSheet[]>(
      `${this.apiServerUrl}/ats-products/price-sheet`,
    );
  }

  public getPriceSheetByDate(
    date: string,
    exchange: Exchange,
    market: Market,
    status: Status,
  ): Observable<PriceSheet[]> {
    return this.http.get<PriceSheet[]>(
      `${this.apiServerUrl}/ats-products/price-sheet?date=${date}&exchange=${exchange}&market=${market}&status=${status}`,
    );
  }

  public updatePriceSheet(market_cap: PriceSheet[]): Observable<any[]> {
    return this.http.put<any>(
      `${this.apiServerUrl}/ats-products/price-sheet`,
      market_cap,
    );
  }

  public addPriceSheetCommentary(priceSheetCommentary: any): Observable<any> {
    return this.http.post<any>(
      `${this.apiServerUrl}/ats-products/price-sheet/add-commentary`,
      priceSheetCommentary,
    );
  }

  public getPriceSheetStats(exchange: any, date: any): Observable<any> {
    return this.http.get<ApiResponse>(
      `${this.apiServerUrl}/ats-products/price-sheet/commentaries?exchangeId=` +
        exchange +
        `&statsDate=` +
        date,
    );
  }

  public async changeStatus(
    request: ChangeATSProductStatusRequest,
    priceSheet: PriceSheet[],
    stats: PriceSheetStats,
  ) {
    const { status, market, exchange, date } = request;
    const formattedDate = this.dateUtilsService.formatDateToISO(date);
    const formData = new FormData();

    const baseFormData = {
      exchange: exchange,
      ...(market && { market: market }),
      date: formattedDate,
      status,
      productType: request.productType,
    };

    Object.entries(baseFormData).forEach(([key, value]) => {
      formData.append(key, value);
    });

    if (status === Status.PUBLISHED) {
      try {
        await this.exportData({
          stats,
          priceSheet,
          exchange,
          reportTitle: `${market} Market Capitalisation Report`,
          exchangeName: Exchange[exchange],
          market: market ?? Market.REG,
          date: formattedDate,
          shouldSaveFile: false,
        });

        const file = await this.excelExportService.getLastGeneratedFile();

        if (!file) {
          throw new Error("No file generated to save");
        }

        formData.append("file", file);
        formData.append("extension", "xlsx");
      } catch (error) {
        handleError(error);
        throw error;
      }
    }

    return this.http.patch<unknown[]>(
      `${this.apiServerUrl}/ats-products/change-status`,
      formData,
    );
  }

  async exportData({
    stats,
    priceSheet,
    exchange,
    reportTitle,
    exchangeName,
    market,
    date,
    shouldSaveFile,
  }: ExportDataParams) {
    const headers: ExcelHeaderOptions<any>[] = [
      {
        key: "name",
        header: "Company Name",
        footerItem: { value: "SUBTOTAL" },
      },
      { key: "isin", header: "ISIN" },
      { key: "highPrice", header: "Year High", alignment: "right" },
      { key: "lowPrice", header: "Year Low", alignment: "right" },
      {
        key: "prevClosingPrice",
        header: "Prev. Closing Price",
        alignment: "right",
      },
      { key: "openPrice", header: "Opening Price", alignment: "right" },
      {
        key: "lastTransPrice",
        header: "Last Trans. Price",
        alignment: "right",
      },
      { key: "closePrice", header: "Closing Price", alignment: "right" },
      { key: "changePrice", header: "Price Change", alignment: "right" },
      {
        key: "percentageChange",
        header: "Price Change(%)",
        alignment: "right",
      },
      {
        key: "tradesCount",
        header: "Total Trade Count",
        alignment: "right",
      },
      {
        key: "turnover",
        header: "Total Traded Volume",
        alignment: "right",
        footerItem: { value: stats.turnover },
      },
      {
        key: "turnoverValue",
        header: "Total Traded Value",
        alignment: "right",
        footerItem: { value: stats.turnoverValue },
      },
    ];

    // Define export options
    const options: ExcelExportOptions = {
      fileName: `${market}_Price_Sheet_Report_${date}`,
      sheetName: "Price Sheet Report",
      exchangeName: exchangeName,
      exchange: exchange,
      reportTitle: "Price Sheet Report",
    };

    const data = priceSheet.map((item) => ({
      openPrice: Number(item.openPrice?.toFixed(4)),
      closePrice: Number(item.closePrice?.toFixed(4)),
      lowPrice: Number(item.lowPrice?.toFixed(4)),
      highPrice: Number(item.highPrice?.toFixed(4)),
      turnover: item.turnover,
      turnoverValue: Number(item.turnoverValue?.toFixed(4)),
      tradesCount: Number(item.tradesCount?.toFixed(4)),
      changePrice: Number(item.changePrice?.toFixed(4)),
      percentageChange: Number(item.percentageChange?.toFixed(4)),
      lastTransPrice: Number(item.lastTransPrice?.toFixed(4)),
      prevClosingPrice: Number(item.prevClosingPrice?.toFixed(4)),
      name: item.name,
      symbol: item.symbol,
      isin: item.isin,
    }));

    const formatedDate = this.dateUtilsService.formatDateForReports(date);

    // Trigger export using the service
    await this.excelExportService.exportToExcel(
      data, // Data from p-table
      headers,
      options,
      exchange,
      formatedDate,
      9, // starting column for address
      shouldSaveFile,
    );
  }
}
