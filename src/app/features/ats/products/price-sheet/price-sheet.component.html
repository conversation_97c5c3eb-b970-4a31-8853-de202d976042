<div class="flex flex-col gap-3">
  <app-text variant="titleLg/semibold">{{ exchange }} Price Sheet</app-text>

  <div class="flex justify-between gap-3">
    <app-button-group>
      @for (
        itemStatus of [Status.DRAFT, Status.APPROVED, Status.PUBLISHED];
        track itemStatus
      ) {
        <app-button
          size="sm"
          (click)="setStatus(itemStatus)"
          [variant]="status === itemStatus ? 'primary' : 'outline-primary'"
        >
          {{ itemStatus }}
        </app-button>
      }
    </app-button-group>

    <div class="flex items-center gap-3" style="width: fit-content">
      <span class="text-sm font-medium">Select Date</span>
      <input
        name="date"
        type="date"
        [max]="maxDate"
        [(ngModel)]="date"
        (change)="onDateChange()"
        class="form-control"
        style="width: fit-content"
      />
    </div>
  </div>

  @if (loading) {
    <app-spinner></app-spinner>
  } @else {
    <div class="flex justify-between gap-3">
      <app-button-group>
        <app-button
          (click)="setMarket(Market.REG)"
          size="sm"
          [variant]="market === Market.REG ? 'success' : 'outline-success'"
        >
          EQUITIES
        </app-button>

        @if (isZse) {
          <app-button
            (click)="setMarket(Market.ETF)"
            size="sm"
            [variant]="market === Market.ETF ? 'success' : 'outline-success'"
          >
            ETFS
          </app-button>

          <app-button
            (click)="setMarket(Market.REIT)"
            size="sm"
            [variant]="market === Market.REIT ? 'success' : 'outline-success'"
          >
            REITS
          </app-button>
        }

        @if (!isZse) {
          <app-button
            (click)="setMarket(Market.BOND)"
            size="sm"
            [variant]="market === Market.BOND ? 'success' : 'outline-success'"
          >
            BOND
          </app-button>

          <app-button
            (click)="setMarket(Market.REIT)"
            size="sm"
            [variant]="market === Market.REIT ? 'success' : 'outline-success'"
          >
            REITS
          </app-button>
        }
      </app-button-group>

      <div class="flex justify-end gap-3">
        <ng-container *appButtonPermission="'edit'">
          @if (status !== Status.PUBLISHED) {
            <app-button
              size="sm"
              variant="primary"
              (click)="updatePriceSheet()"
            >
              Update Table Data
            </app-button>
          }
        </ng-container>

        <ng-container *appButtonPermission="'edit'">
          @switch (status) {
            @case (Status.DRAFT) {
              <app-button
                size="sm"
                variant="warning"
                (click)="changeStatus(Status.APPROVED)"
              >
                Approve
              </app-button>
            }

            @case (Status.APPROVED) {
              <app-button
                size="sm"
                variant="danger"
                (click)="changeStatus(Status.DRAFT)"
              >
                Convert back to draft
              </app-button>

              <app-button
                size="sm"
                variant="success"
                (click)="changeStatus(Status.PUBLISHED)"
              >
                Publish
              </app-button>
            }

            @case (Status.PUBLISHED) {
              <app-button
                size="sm"
                variant="danger"
                (click)="changeStatus(Status.DRAFT)"
              >
                Convert back to draft
              </app-button>
            }
          }
        </ng-container>
      </div>
    </div>

    <app-card id="exportDiv">
      <div class="mb-2 border-t">
        <div class="py-3 w-100 border-bottom">
          <app-text variant="regular/semibold" className="text-center">
            {{ isZse ? "Zimbabwe" : "Victoria Falls" }} Stock Exchange -
            {{ getStatus(status) }} {{ getMarket(market) }} Price Sheet Report
          </app-text>
          <app-text variant="small/semibold" className="text-center">
            {{ date | date: "fullDate" }}
          </app-text>
        </div>

        <div class="flex items-center justify-between pt-2">
          <div class="flex items-center justify-between gap-2">
            <app-button
              size="sm"
              class="flex items-center gap-2"
              (click)="clear(dt)"
            >
              <i class="pi pi-filter-slash"></i>
              <span>Clear</span>
            </app-button>

            @if (priceSheet.length > 0) {
              <div class="flex justify-end gap-2">
                <app-button variant="success" size="sm" (click)="exportData()">
                  Export Data
                </app-button>
              </div>
            }
          </div>

          <div>
            <input
              class="form-control"
              pInputText
              type="text"
              [(ngModel)]="searchValue"
              (input)="dt.filterGlobal($any($event.target).value, 'contains')"
              placeholder="Search keyword"
            />
          </div>
        </div>
      </div>

      <p-table
        id="priceSheetTable"
        #dt
        [value]="priceSheet"
        dataKey="id"
        sortField="companyStatus"
        [rows]="10"
        [rowsPerPageOptions]="[10, 25, 50]"
        [paginator]="false"
        [globalFilterFields]="[
          'symbol',
          'companyName',
          'outstandingShares',
          'priceSheet',
        ]"
        [tableStyle]="{ 'min-width': '50rem' }"
        styleClass="p-datatable-sm"
      >
        <ng-template pTemplate="header">
          <tr
            class="text-xs font-semibold tracking-wider text-gray-600 uppercase bg-gray-100 text-nowrap"
          >
            <th>#</th>
            <th>
              <div pSortableColumn="name" class="flex items-center">
                Company Name
                <p-sortIcon field="name"></p-sortIcon>
              </div>
            </th>
            <th>ISIN</th>
            <th>Year High</th>
            <th>Year Low</th>
            <th>Prev. Closing Price</th>
            <th>Opening Price</th>
            <th>Last Trans. Price</th>
            <th>Closing Price</th>
            <th>Price Change</th>
            <th>Price Change(%)</th>
            <th>Total Trade Count</th>
            <th>Total Traded Volume</th>
            <th>Total Traded Value</th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
          <tr class="text-nowrap">
            <td>{{ rowIndex + 1 }}</td>
            <td>{{ item.name }}</td>
            <td>{{ item.isin }}</td>
            <td
              class="text-end"
              [attr.contenteditable]="status !== Status.PUBLISHED"
              [attr.data-field]="'highPrice'"
              (input)="markCellAsEdited($event, item)"
            >
              {{ item.highPrice | number: "1.4-4" }}
            </td>
            <td
              class="text-end"
              [attr.contenteditable]="status !== Status.PUBLISHED"
              [attr.data-field]="'lowPrice'"
              (input)="markCellAsEdited($event, item)"
            >
              {{ item.lowPrice | number: "1.4-4" }}
            </td>
            <td
              class="text-end"
              [attr.contenteditable]="status !== Status.PUBLISHED"
              [attr.data-field]="'prevClosingPrice'"
              (input)="markCellAsEdited($event, item)"
            >
              {{ item.prevClosingPrice | number: "1.4-4" }}
            </td>
            <td
              class="text-end"
              [attr.contenteditable]="status !== Status.PUBLISHED"
              [attr.data-field]="'openPrice'"
              (input)="markCellAsEdited($event, item)"
            >
              {{ item.openPrice | number: "1.4-4" }}
            </td>
            <td
              class="text-end"
              [attr.contenteditable]="status !== Status.PUBLISHED"
              [attr.data-field]="'lastTransPrice'"
              (input)="markCellAsEdited($event, item)"
            >
              {{
                item.lastTransPrice === 0
                  ? "-"
                  : (item.lastTransPrice | number: "1.4-4")
              }}
            </td>
            <td
              class="text-end"
              [attr.contenteditable]="status !== Status.PUBLISHED"
              [attr.data-field]="'closePrice'"
              (input)="markCellAsEdited($event, item)"
            >
              {{ item.closePrice | number: "1.4-4" }}
            </td>
            <td
              class="text-end"
              [attr.contenteditable]="status !== Status.PUBLISHED"
              [attr.data-field]="'changePrice'"
              (input)="markCellAsEdited($event, item)"
            >
              {{ item.changePrice | number: "1.4-4" }}
            </td>
            <td
              class="text-end"
              [attr.contenteditable]="status !== Status.PUBLISHED"
              [attr.data-field]="'percentageChange'"
              (input)="markCellAsEdited($event, item)"
            >
              {{ item.percentageChange | number: "1.4-4" }}
            </td>
            <td
              class="text-end"
              [attr.contenteditable]="status !== Status.PUBLISHED"
              [attr.data-field]="'tradesCount'"
              (input)="markCellAsEdited($event, item)"
            >
              {{ item.tradesCount }}
            </td>
            <td
              class="text-end"
              [attr.contenteditable]="status !== Status.PUBLISHED"
              [attr.data-field]="'turnover'"
              (input)="markCellAsEdited($event, item)"
            >
              {{ item.turnover | number }}
            </td>
            <td
              class="text-end"
              [attr.contenteditable]="status !== Status.PUBLISHED"
              [attr.data-field]="'turnoverValue'"
              (input)="markCellAsEdited($event, item)"
            >
              {{ item.turnoverValue | number: "1.4-4" }}
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="footer">
          <tr>
            <th colspan="12" class="text-start">SUB TOTAL</th>
            <th class="text-end">
              {{ stats?.turnover | number }}
            </th>
            <th class="text-end">
              {{ stats?.turnoverValue | number: "1.2-4" }}
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="14" class="text-center">No data found</td>
          </tr>
        </ng-template>
      </p-table>

      @if (priceSheet.length > 0) {
        <form
          (submit)="addPriceSheetCommentary()"
          class="flex flex-col gap-2 mt-4"
        >
          <label for="commentary">{{ getMarket(market) }} Comment</label>
          <textarea
            [disabled]="status === Status.PUBLISHED"
            id="commentary"
            name="commentary"
            [attr.placeholder]="
              status === Status.PUBLISHED
                ? ''
                : 'Comment on the ' + getMarket(market) + ' Price Sheet'
            "
            class="form-control"
            [(ngModel)]="commentary"
          ></textarea>
          @if (status !== Status.PUBLISHED) {
            <app-button size="sm" type="submit"> Add Comment </app-button>
          }
        </form>
      }
    </app-card>
  }
</div>
