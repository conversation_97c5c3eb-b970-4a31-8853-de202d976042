import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { Market } from "@/app/core/enums/market.enum";
import { ProductLinkTable } from "@/app/core/enums/product-link-table.enum";
import { Status } from "@/app/core/enums/status.enum";
import { changeStatusByAvailableStatus } from "@/app/core/utils/change-status.util";
import { getMarket, getStatus } from "@/app/core/utils/common.util";
import { handleError } from "@/app/core/utils/error-handler.util";
import {
  CreatePriceSheetCommentaryRequest,
  PriceSheet,
  PriceSheetStats,
} from "@/app/features/ats/products/price-sheet/price-sheet.model";
import { DatePipe, DecimalPipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { ButtonModule } from "primeng/button";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { Table, TableModule } from "primeng/table";
import Swal from "sweetalert2";
import { ButtonGroupComponent } from "../../../../shared/ui/button/button-group/button-group.component";
import { ButtonComponent } from "../../../../shared/ui/button/button.component";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { SpinnerComponent } from "../../../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { ChangeATSProductStatusRequest } from "../products.model";
import { PriceSheetService } from "./price-sheet.service";

@Component({
  selector: "app-price-sheet",
  templateUrl: "./price-sheet.component.html",
  styleUrl: "./price-sheet.component.css",
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    DatePipe,
    DecimalPipe,
    IconFieldModule,
    InputIconModule,
    TableModule,
    ButtonModule,
    ButtonComponent,
    ButtonGroupComponent,
    TextComponent,
    CardComponent,
    SpinnerComponent,
    ButtonPermissionDirective,
  ],
})
export class ATSPriceSheetComponent implements OnInit {
  priceSheet: PriceSheet[] = [];
  editedRows: any[] = [];

  searchValue: string | undefined;

  today: string = new Date().toISOString().split("T")[0];
  date: string;
  maxDate: string;
  exchange: Exchange = Exchange.VFEX;
  Exchange = Exchange;
  market: Market = Market.REG;

  status: Status = Status.DRAFT;
  Status = Status;
  Market = Market;
  loading: boolean = true;

  equities: boolean = true;
  etfs: boolean = false;

  stats?: PriceSheetStats;
  index!: number;

  isZse: boolean = false;
  commentary: string = "";

  get exchangeName(): string {
    return `${this.exchange === Exchange.ZSE ? "Zimbabwe" : "Victoria Falls"} Stock Exchange`;
  }

  get reportTitle(): string {
    return `${this.getMarket(this.market)} Price Sheet Report`;
  }

  constructor(
    private readonly priceSheetService: PriceSheetService,
    private readonly route: ActivatedRoute,
  ) {
    this.date = this.today;
    this.maxDate = this.today;
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.exchange = params["exchange"];
      this.isZse = params["exchange"] === Exchange.ZSE;
    });

    this.loadPriceSheetData();
  }

  loadPriceSheetData() {
    this.loading = true;
    this.priceSheetService
      .getPriceSheetByDate(this.date, this.exchange, this.market, this.status)
      .subscribe(
        (res: any) => {
          this.priceSheet = res?.data?.priceSheet || [];
          this.stats = res?.data?.stats || {};
          this.index = res?.data?.index || 0;
          this.commentary = res?.data?.commentary;

          changeStatusByAvailableStatus(
            res?.data?.availableStatus,
            this.setStatus.bind(this),
          );

          this.loading = false;
        },
        (error: any) => {
          if (error?.error?.message === "Wait for market to close!") {
            handleError(error, "Market is still open!", "info");
          } else {
            handleError(error);
          }
          this.loading = false;
        },
      );
  }

  getMarketDataByDate() {
    this.loading = true;
    this.loadPriceSheetData();
  }

  setStatus(status: Status) {
    if (this.status !== status) {
      // Avoid redundant status changes
      this.status = status;
      this.loadPriceSheetData();
    }
  }

  setMarket(market: Market) {
    this.market = market;
    this.loadPriceSheetData();
  }

  async changeStatus(newStatus: Status) {
    this.loading = true;
    let message: string;
    let item: string = "price sheet";

    switch (newStatus) {
      case Status.DRAFT:
        message = `convert ${item} to draft?`;
        break;
      case Status.APPROVED:
        message = `approve ${item}?`;
        break;
      case Status.PUBLISHED:
        message = `publish ${item}?`;
        break;
      default:
        message = "perform this action?";
        break;
    }

    Swal.fire({
      title: "Warning",
      text: `Are you sure you want to ${message}`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
    }).then(async (result) => {
      if (result.isConfirmed) {
        const payload: ChangeATSProductStatusRequest = {
          market: this.market,
          exchange: this.exchange,
          date: this.priceSheet[0].statsDate as string,
          status: newStatus,
          productType: ProductLinkTable.PRICE_SHEET,
        };

        (
          await this.priceSheetService.changeStatus(
            payload,
            this.priceSheet,
            this.stats!,
          )
        ).subscribe({
          next: () => this.setStatus(newStatus),
          error: (error: any) => {
            handleError(error);
            this.loading = false;
          },
        });
      } else {
        this.loading = false;
      }
    });
  }

  updatePriceSheet() {
    this.loading = true;
    this.priceSheetService.updatePriceSheet(this.editedRows).subscribe(
      () => {
        Swal.fire("Success", "Price Sheet updated!", "success");
        this.getMarketDataByDate();
      },
      (error: any) => {
        handleError(error);
        this.loading = false;
      },
    );
  }

  onDateChange(): void {
    this.loadPriceSheetData();
  }

  markCellAsEdited(event: Event, item: PriceSheet): void {
    const target = event.target as HTMLElement;
    const field = target.getAttribute("data-field") as keyof PriceSheet;
    const newValue = target.innerText.trim();

    const removeCommas = (value: string): number =>
      parseFloat(value?.replace(/,/g, "")) || 0;

    const parsedNewValue = removeCommas(newValue) as any;

    if (item[field] !== parsedNewValue) {
      const editedRows = [...this.editedRows]; // Create a shallow copy

      // Check if the row has already been edited
      const existingRowIndex = editedRows.findIndex(
        (row) => row.id === item.id,
      );

      if (existingRowIndex > -1) {
        // Merge the new field update with the existing row
        editedRows[existingRowIndex] = {
          ...editedRows[existingRowIndex],
          [field]: parsedNewValue,
        };
      } else {
        // Add a new entry for the edited row, including all fields
        editedRows.push({
          id: item.id,
          openPrice: item.openPrice,
          closePrice: item.closePrice,
          lowPrice: item.lowPrice,
          highPrice: item.highPrice,
          statsDate: item.statsDate,
          turnover: item.turnover,
          turnoverValue: item.turnoverValue,
          tradesCount: item.tradesCount,
          changePrice: item.changePrice,
          percentageChange: item.percentageChange,
          symbolId: item.symbolId,
          symbolTypeId: item.symbolTypeId,
          marketId: item.marketId,
          exchangeId: item.exchangeId,
          lastTransPrice: item.lastTransPrice,
          prevClosingPrice: item.prevClosingPrice,
          [field]: parsedNewValue,
        });
      }

      this.editedRows = editedRows;
    }
  }

  protected getStatus(status: Status): string {
    return getStatus(status);
  }

  protected getMarket(market: Market): string {
    return getMarket(market);
  }

  addPriceSheetCommentary() {
    const payload: CreatePriceSheetCommentaryRequest = {
      commentary: this.commentary,
      market: this.market,
      exchange: this.exchange,
      statsDate: this.date,
    };

    this.priceSheetService.addPriceSheetCommentary(payload).subscribe(
      (res: any) => {
        Swal.fire("Successfull", res.message, "success");

        this.loading = false;
      },
      (error: any) => {
        handleError(error);
        this.loading = false;
      },
    );
  }

  exportData() {
    this.priceSheetService.exportData({
      stats: this.stats!,
      priceSheet: this.priceSheet,
      exchange: this.exchange,
      reportTitle: `${this.getMarket(this.market)} Price Sheet Report`,
      exchangeName: this.exchangeName,
      market: this.market,
      date: this.date,
    });
  }

  clear(table: Table) {
    table.clear();
    this.searchValue = "";
  }
}
