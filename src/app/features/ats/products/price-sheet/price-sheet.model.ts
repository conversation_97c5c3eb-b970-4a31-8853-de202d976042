import { Exchange } from "@/app/core/enums/exchange.enum";
import { Market } from "@/app/core/enums/market.enum";
import { Status } from "@/app/core/enums/status.enum";

export interface PriceSheet {
  id?: string;
  openPrice?: number;
  closePrice?: number;
  lowPrice?: number;
  highPrice?: number;
  statsDate?: Date | string;
  turnover?: number;
  turnoverValue?: number;
  tradesCount?: number;
  changePrice: number | null | undefined;
  percentageChange?: number;
  symbolId?: number;
  symbolTypeId?: number;
  marketId?: number;
  exchangeId?: number;
  lastTransPrice?: number;
  prevClosingPrice?: number;
  name?: string;
  symbol?: string;
  isin?: string;
  status?: any;
  created_date?: Date;
  modified_date?: Date;
  created_user?: string;
  modified_user?: string;
  system_field?: string;
  approved_by?: string;
  date_approved?: Date;
}

export interface PriceSheetStats {
  turnover: number;
  turnoverValue: number;
}

export interface CreatePriceSheetCommentaryRequest {
  commentary: string;
  market: Market;
  exchange: Exchange;
  statsDate: string;
}

export interface UpdatePriceSheetRequest {
  id: Market;
  outStandingShares: number;
}

export interface ChangePriceSheetStatusRequest {
  id: string;
  market: Market;
  exchange: Exchange;
  date: string;
  status: Status;
  file?: File;
  extension?: string;
}
