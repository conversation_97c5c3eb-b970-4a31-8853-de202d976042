import { Exchange } from "@/app/core/enums/exchange.enum";
import { Market } from "@/app/core/enums/market.enum";
import { ProductLinkTable } from "@/app/core/enums/product-link-table.enum";
import { Status } from "@/app/core/enums/status.enum";

export interface ChangeATSProductStatusRequest {
  market?: Market;
  exchange: Exchange;
  date: string;
  status: Status;
  productType: ProductLinkTable;
  file?: File;
  extension?: string;
}
