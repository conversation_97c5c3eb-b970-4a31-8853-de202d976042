import { ApiResponse } from "@/app/core/interfaces/api-response.interface";
import { environment } from "@/environments/environment.development";
import { HttpClient } from "@angular/common/http";
import { Injectable, inject } from "@angular/core";
import { Observable } from "rxjs";
import { Bank, CreateBankRequest, UpdateBankRequest } from "./bank.model";

@Injectable({
  providedIn: "root",
})
export class BankService {
  private readonly http = inject(HttpClient);
  private readonly apiServerUrl = environment.apiBaseUrl;
  private readonly apiUrl = `${this.apiServerUrl}/banks`;

  getBanks() {
    return this.http.get<ApiResponse<Bank[]>>(this.apiUrl);
  }

  getBank(id: string) {
    return this.http.get<ApiResponse<Bank>>(`${this.apiUrl}/${id}`);
  }

  createBank(request: CreateBankRequest): Observable<Bank> {
    return this.http.post<Bank>(this.apiUrl, request);
  }

  updateBank(id: string, request: UpdateBankRequest): Observable<Bank> {
    return this.http.put<Bank>(`${this.apiUrl}/${id}`, request);
  }

  deleteBank(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
