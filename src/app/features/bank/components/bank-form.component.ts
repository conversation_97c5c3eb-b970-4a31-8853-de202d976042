import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";

import { Component, inject, signal } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { ToastModule } from "primeng/toast";
import { ButtonComponent } from "../../../shared/ui/button/button.component";
import { FormFieldComponent } from "../../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../../shared/ui/modal/modal.component";
import { Bank, CreateBankRequest } from "../bank.model";
import { BankService } from "../bank.service";
import { BankDialogService } from "../services/bank-dialog.service";

interface BankFormData {
  id?: string;
  name: string;
  code: string;
  status: string;
}

@Component({
  selector: "app-bank-form",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    ButtonModule,
    DialogModule,
    ToastModule,
    ButtonComponent,
    FormFieldComponent,
    ModalComponent,
  ],
  templateUrl: "./bank-form.component.html",
})
export class BankFormComponent {
  private readonly bankService = inject(BankService);
  private readonly dialogService = inject(BankDialogService);
  private readonly fb = inject(FormBuilder);

  visible = signal(false);
  loading = signal(false);
  isEditMode = signal(false);
  currentBank = signal<Bank | null>(null);

  bankForm = this.fb.nonNullable.group({
    id: [""],
    name: ["", [Validators.maxLength(100)]],
    code: ["", [Validators.maxLength(20)]],
    status: ["ACTIVE"],
  }) as FormGroup<FormControlType<BankFormData>>;

  constructor() {
    // Handle create dialog
    this.dialogService.showCreateDialog$.subscribe(() => {
      this.isEditMode.set(false);
      this.currentBank.set(null);
      this.bankForm.reset({
        id: "",
        name: "",
        code: "",
        status: "ACTIVE",
      });
      this.visible.set(true);
    });

    // Handle edit dialog
    this.dialogService.showEditDialog$.subscribe((bank) => {
      this.isEditMode.set(true);
      this.currentBank.set(bank);
      this.bankForm.patchValue({
        id: bank.id,
        name: bank.name,
        code: bank.code,
        status: bank.status,
      });
      this.visible.set(true);
    });
  }

  hideDialog(): void {
    this.visible.set(false);
    this.bankForm.reset();
  }

  onSubmit(): void {
    this.loading.set(true);
    const formValue = this.bankForm.value;

    if (this.isEditMode()) {
      // For edit mode, only include fields that have values
      const request: any = {};

      if (formValue.name) request.name = formValue.name;
      if (formValue.code) request.code = formValue.code;
      if (formValue.status) request.status = formValue.status;

      // Include branchId if available
      request.branchId =
        this.currentBank()?.branches?.[0]?.id ||
        "********-0000-0000-0000-************";

      // Only proceed with update if there are fields to update
      if (Object.keys(request).length > 0) {
        this.bankService.updateBank(formValue.id!, request).subscribe({
          next: () => {
            showToast({
              message: "Bank has been updated successfully",
              type: "success",
            });
            this.hideDialog();
            // Reload banks list
            window.location.reload();
          },
          error: (error) => {
            handleError(error, "Failed to update bank");
            this.loading.set(false);
          },
        });
      } else {
        // No fields to update
        showToast({
          message: "No changes to update",
          type: "info",
        });
        this.hideDialog();
        this.loading.set(false);
      }
    } else {
      // For create mode, validate required fields
      if (!formValue.name || !formValue.code) {
        markFormGroupTouched(this.bankForm);
        showToast({
          message: "Please fill in all required fields",
          type: "error",
        });
        this.loading.set(false);
        return;
      }

      const request: CreateBankRequest = {
        name: formValue.name!,
        code: formValue.code!,
        branchId: "********-0000-0000-0000-************",
      };

      this.bankService.createBank(request).subscribe({
        next: () => {
          showToast({
            message: "Bank has been created successfully",
            type: "success",
          });
          this.hideDialog();
          // Reload banks list
          window.location.reload();
        },
        error: (error) => {
          handleError(error, "Failed to create bank");
          this.loading.set(false);
        },
      });
    }
  }
}
