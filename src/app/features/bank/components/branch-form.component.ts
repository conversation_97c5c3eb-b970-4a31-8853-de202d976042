import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";

import { Component, inject, signal } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { MessageService } from "primeng/api";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { ToastModule } from "primeng/toast";
import { ButtonComponent } from "../../../shared/ui/button/button.component";
import { FormFieldComponent } from "../../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../../shared/ui/modal/modal.component";
import { Branch } from "../bank.model";
import { BranchDialogService } from "../services/branch-dialog.service";
import {
  BranchService,
  CreateBranchRequest,
  UpdateBranchRequest,
} from "../services/branch.service";

interface BranchFormData {
  id?: string;
  name: string;
  code: string;
  bankId: string;
  status: string;
}

@Component({
  selector: "app-branch-form",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    ButtonModule,
    DialogModule,
    ToastModule,
    ButtonComponent,
    FormFieldComponent,
    ModalComponent,
  ],
  templateUrl: "./branch-form.component.html",
})
export class BranchFormComponent {
  private readonly branchService = inject(BranchService);
  private readonly messageService = inject(MessageService);
  private readonly dialogService = inject(BranchDialogService);
  private readonly fb = inject(FormBuilder);

  visible = signal(false);
  loading = signal(false);
  isEditMode = signal(false);
  currentBranch = signal<Branch | null>(null);
  bankId = signal<string>("");

  branchForm = this.fb.nonNullable.group({
    id: [""],
    name: ["", [Validators.maxLength(100)]],
    code: ["", [Validators.maxLength(20)]],
    bankId: [""],
    status: ["ACTIVE"],
  }) as FormGroup<FormControlType<BranchFormData>>;

  constructor() {
    // Handle create dialog
    this.dialogService.showCreateDialog$.subscribe((bankId) => {
      this.isEditMode.set(false);
      this.currentBranch.set(null);
      this.bankId.set(bankId);
      this.branchForm.reset({
        id: "",
        name: "",
        code: "",
        bankId: bankId,
        status: "ACTIVE",
      });
      this.visible.set(true);
    });

    // Handle edit dialog
    this.dialogService.showEditDialog$.subscribe((branch) => {
      this.isEditMode.set(true);
      this.currentBranch.set(branch);
      this.bankId.set(branch.bankId);
      this.branchForm.patchValue({
        id: branch.id,
        name: branch.name,
        code: branch.code,
        bankId: branch.bankId,
        status: branch.status,
      });
      this.visible.set(true);
    });
  }

  hideDialog(): void {
    this.visible.set(false);
    this.branchForm.reset();
  }

  onSubmit(): void {
    this.loading.set(true);
    const formValue = this.branchForm.value;

    if (this.isEditMode()) {
      // For edit mode, only include fields that have values
      const request: UpdateBranchRequest = {};

      if (formValue.name) request.name = formValue.name;
      if (formValue.code) request.code = formValue.code;
      if (formValue.bankId) request.bankId = formValue.bankId;
      if (formValue.status) request.status = formValue.status;

      // Only proceed with update if there are fields to update
      if (Object.keys(request).length > 0) {
        this.branchService.updateBranch(formValue.id!, request).subscribe({
          next: () => {
            showToast({
              message: "Branch has been updated successfully",
              type: "success",
            });
            this.hideDialog();
            // Reload banks list
            window.location.reload();
          },
          error: (error) => {
            handleError(error, "Failed to update branch");
            this.loading.set(false);
          },
        });
      } else {
        // No fields to update
        showToast({
          message: "No changes to update",
          type: "info",
        });
        this.hideDialog();
      }
    } else {
      // For create mode, validate required fields
      if (!formValue.name || !formValue.code || !formValue.bankId) {
        markFormGroupTouched(this.branchForm);
        showToast({
          message: "Please fill in all required fields",
          type: "error",
        });
        this.loading.set(false);
        return;
      }

      const request: CreateBranchRequest = {
        name: formValue.name!,
        code: formValue.code!,
        bankId: formValue.bankId!,
      };

      this.branchService.createBranch(request).subscribe({
        next: () => {
          showToast({
            message: "Branch has been created successfully",
            type: "success",
          });
          this.hideDialog();
          // Reload banks list
          window.location.reload();
        },
        error: (error) => {
          handleError(error, "Failed to create branch");
          this.loading.set(false);
        },
      });
    }
  }
}
