<app-modal
  [isVisible]="visible()"
  [config]="{ title: isEditMode() ? 'Edit Bank' : 'Create Bank' }"
>
  <form [formGroup]="bankForm" (ngSubmit)="onSubmit()">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <app-form-field
        label="Name"
        type="text"
        [control]="bankForm.get('name')!"
        [required]="!isEditMode()"
      />

      <app-form-field
        label="Code"
        type="text"
        [control]="bankForm.get('code')!"
        [required]="!isEditMode()"
      />

      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
        [control]="bankForm.get('status')!"
        [required]="!isEditMode()"
      />
    </div>

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="hideDialog()" variant="outline"> Cancel </app-button>
      <app-button type="submit" [loading]="loading()">
        {{ isEditMode() ? "Update" : "Create" }}
      </app-button>
    </div>
  </form>
</app-modal>
