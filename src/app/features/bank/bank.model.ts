import { BaseResponse } from "@/app/core/interfaces/common.interface";

export interface Bank extends BaseResponse {
  name: string;
  code: string;
  branches: Branch[];
}

export interface Branch extends BaseResponse {
  name: string;
  code: string;
  id: string;
  bankId: string;
}

export interface CreateBankRequest {
  name: string;
  code: string;
  branchId: string;
}

export interface UpdateBankRequest {
  name?: string;
  code?: string;
  branchId?: string;
}
