<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <ng-container *appButtonPermission="'create'">
        <app-button size="sm" class="!gap-2" (click)="openCreateDialog()">
          <i class="text-sm pi pi-plus"></i>
          New
        </app-button>
      </ng-container>
    </ng-template>

    <ng-template #end>
      <app-button
        size="sm"
        class="!gap-2"
        variant="secondary"
        (click)="reportExportService.exportToExcel(exportData, 'banks.xlsx')"
      >
        <i class="text-sm pi pi-upload"></i>
        Export
      </app-button>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (loading()) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading banks...</span>
      </div>
    </div>
  } @else {
    <!-- Bank Table -->
    <p-table
      [value]="filteredBanks()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
    >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold">Manage Banks</app-text>
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />

            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search banks..."
            />
          </p-iconfield>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>

          <th pSortableColumn="createdDate">
            <div class="flex items-center gap-2">
              Created Date
              <p-sortIcon field="createdDate" />
            </div>
          </th>

          <th pSortableColumn="name">
            <div class="flex items-center justify-between gap-2">
              Name
              <p-sortIcon field="name" />
              <p-columnFilter
                type="text"
                field="name"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="code">
            <div class="flex items-center justify-between gap-2">
              Code
              <p-sortIcon field="code" />
              <p-columnFilter
                type="text"
                field="code"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>

          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-bank let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(bank)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ bank.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ bank.name }}</td>
          <td>
            <span class="px-2 py-1 font-mono text-sm bg-gray-100 rounded">
              {{ bank.code }}
            </span>
          </td>
          <td>
            <span [class]="getStatusClass(bank.status)">
              {{ bank.status }}
            </span>
          </td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="openEditDialog(bank)"
                >
                  <i class="pi pi-pencil"></i>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-bank>
        <tr>
          <td colspan="6">
            <div class="p-4 bg-gray-50 rounded-md">
              <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                <!-- Basic Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">Name</h5>
                  <p class="text-gray-800">{{ bank.name }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">Code</h5>
                  <p class="text-gray-800">{{ bank.code }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">{{ bank.status }}</p>
                </div>

                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ bank.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                      bank.modifiedDate
                        ? (bank.modifiedDate | date: "yyyy-MM-dd HH:mm:ss")
                        : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ bank.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ bank.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Branches Information -->
              <div class="mt-4">
                <div class="flex justify-between items-center mb-2">
                  <h5 class="text-sm font-semibold text-gray-600">Branches</h5>
                  <app-button
                    size="sm"
                    variant="primary"
                    icon="pi pi-plus"
                    (click)="openCreateBranchDialog(bank.id)"
                  >
                    Add Branch
                  </app-button>
                </div>
                <div
                  class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                >
                  @for (branch of bank.branches; track branch.id) {
                    <div class="p-3 rounded-lg bg-gray-100">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                          <i
                            class="text-3xl pi pi-building-columns text-primary/75"
                          ></i>
                          <div>
                            <app-text variant="small/medium">{{
                              branch.name
                            }}</app-text>
                            <app-text variant="xs/medium">{{
                              branch.code
                            }}</app-text>
                          </div>
                        </div>
                        <div class="flex gap-2">
                          <app-button
                            size="icon"
                            variant="success"
                            (click)="openEditBranchDialog(branch)"
                          >
                            <i class="pi pi-pencil"></i>
                          </app-button>
                        </div>
                      </div>
                    </div>
                  }
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  (click)="openEditDialog(bank)"
                >
                  Edit Bank
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="5" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No banks found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<app-bank-form />
<app-branch-form />
<p-toast />
