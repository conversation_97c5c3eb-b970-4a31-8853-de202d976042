import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { DatePipe } from "@angular/common";
import { Component, inject, signal } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { MessageService } from "primeng/api";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import { Bank, Branch } from "./bank.model";
import { BankService } from "./bank.service";
import { BankFormComponent } from "./components/bank-form.component";
import { BranchFormComponent } from "./components/branch-form.component";
import { BankDialogService } from "./services/bank-dialog.service";
import { BranchDialogService } from "./services/branch-dialog.service";

@Component({
  selector: "app-bank-list",
  standalone: true,
  imports: [
    FormsModule,
    TableModule,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    BankFormComponent,
    BranchFormComponent,
    CardComponent,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    ButtonComponent,
    TextComponent,
    ButtonPermissionDirective,
    DatePipe,
  ],
  providers: [MessageService, DatePipe],
  templateUrl: "./bank-list.component.html",
  styleUrl: "./bank-list.component.css",
})
export class BankListComponent {
  private readonly bankService = inject(BankService);
  private readonly messageService = inject(MessageService);
  private readonly dialogService = inject(BankDialogService);
  private readonly branchDialogService = inject(BranchDialogService);
  private readonly datePipe = inject(DatePipe);
  protected readonly reportExportService = inject(ReportExportService);

  banks = signal<Bank[]>([]);
  filteredBanks = signal<Bank[]>([]);
  loading = signal(true);
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  // Export data property for Excel export functionality
  get exportData(): any[] {
    return this.filteredBanks().map((bank) => ({
      "Bank Name": bank.name,
      "Bank Code": bank.code,
      Status: bank.status,
      "Total Branches": bank.branches?.length || 0,
      "Branch Names":
        bank.branches?.map((branch: any) => branch.name).join(", ") || "",
      "Created Date": this.datePipe.transform(
        bank.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": bank.modifiedDate
        ? this.datePipe.transform(bank.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": bank.createdUserName ?? "-",
      "Modified By": bank.modifiedUserName ?? "-",
    }));
  }

  constructor() {
    this.loadBanks();
  }

  toggleRow(bank: Bank): void {
    if (this.expandedRows[bank.id]) {
      delete this.expandedRows[bank.id];
    } else {
      this.expandedRows[bank.id] = true;
    }
    this.expandedRows = { ...this.expandedRows };
  }

  private loadBanks(): void {
    this.bankService.getBanks().subscribe({
      next: (res) => {
        this.banks.set(res.data);
        this.filteredBanks.set(res.data);
        this.loading.set(false);
      },
      error: () => {
        this.messageService.add({
          severity: "error",
          summary: "Error",
          detail: "Failed to load banks",
        });
        this.loading.set(false);
      },
    });
  }

  onSearch(term: string): void {
    if (!term) {
      this.filteredBanks.set(this.banks());
      return;
    }

    const filtered = this.banks().filter(
      (bank) =>
        bank.name.toLowerCase().includes(term.toLowerCase()) ||
        bank.code.toLowerCase().includes(term.toLowerCase()),
    );
    this.filteredBanks.set(filtered);
  }

  openCreateDialog(): void {
    this.dialogService.openCreateDialog();
  }

  openEditDialog(bank: Bank): void {
    this.dialogService.openEditDialog(bank);
  }

  openViewDialog(bank: Bank): void {
    this.dialogService.openViewDialog(bank);
  }

  openCreateBranchDialog(bankId: string): void {
    this.branchDialogService.openCreateDialog(bankId);
  }

  openEditBranchDialog(branch: Branch): void {
    this.branchDialogService.openEditDialog(branch);
  }

  // Delete bank functionality removed as requested

  // Delete branch functionality removed as requested

  getStatusClass(status: string): string {
    return status === "ACTIVE"
      ? "bg-green-100 text-green-800 px-2 py-1 rounded font-mono text-xs font-medium"
      : "bg-red-100 text-red-800 px-2 py-1 rounded font-mono text-xs font-medium";
  }
}
