:host ::ng-deep {
  .p-datatable-elegant {
    /* Table Container */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.3s ease;

    /* Header */
    .p-datatable-header {
      background: linear-gradient(to right, #f9fafb, #f3f4f6);
      border: none;
      padding: 1.25rem 1rem;
    }

    /* Column Headers */
    .p-datatable-thead > tr > th {
      background: #ffffff;
      border-bottom: 2px solid #e5e7eb;
      color: #374151;
      font-weight: 600;
      padding: 1rem;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f9fafb;
      }
    }

    /* Table Body */
    .p-datatable-tbody > tr {
      border-bottom: 1px solid #e5e7eb;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f8fafc;
      }

      > td {
        padding: 1rem;
        color: #4b5563;
      }
    }

    /* Paginator */
    .p-paginator {
      background: linear-gradient(to right, #f9fafb, #f3f4f6);
      border: none;
      padding: 1rem;

      .p-paginator-element {
        border-radius: 0.375rem;
        transition: all 0.2s;

        &:hover {
          background-color: #e5e7eb;
        }
      }
    }

    /* Expansion Icon */
    .expanded {
      transform: rotate(90deg);
      transition: transform 0.2s ease;
    }

    /* Striped Rows */
    .p-datatable-tbody > tr:nth-child(even) {
      background-color: #fafafa;
    }
  }
}
