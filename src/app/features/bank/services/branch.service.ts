import { ApiResponse } from "@/app/core/interfaces/api-response.interface";
import { environment } from "@/environments/environment.development";
import { HttpClient } from "@angular/common/http";
import { Injectable, inject } from "@angular/core";
import { Observable } from "rxjs";
import { Branch } from "../bank.model";

export interface CreateBranchRequest {
  name: string;
  code: string;
  bankId: string;
}

export interface UpdateBranchRequest {
  name?: string;
  code?: string;
  bankId?: string;
  status?: string;
}

@Injectable({
  providedIn: "root",
})
export class BranchService {
  private readonly http = inject(HttpClient);
  private readonly apiServerUrl = environment.apiBaseUrl;
  private readonly apiUrl = `${this.apiServerUrl}/branches`;

  getBranches() {
    return this.http.get<ApiResponse<Branch[]>>(this.apiUrl);
  }

  getBranchesByBankId(bankId: string) {
    return this.http.get<ApiResponse<Branch[]>>(
      `${this.apiUrl}/bank/${bankId}`,
    );
  }

  getBranch(id: string) {
    return this.http.get<ApiResponse<Branch>>(`${this.apiUrl}/${id}`);
  }

  createBranch(request: CreateBranchRequest): Observable<Branch> {
    return this.http.post<Branch>(this.apiUrl, request);
  }

  updateBranch(id: string, request: UpdateBranchRequest): Observable<Branch> {
    return this.http.put<Branch>(`${this.apiUrl}/${id}`, request);
  }

  deleteBranch(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
