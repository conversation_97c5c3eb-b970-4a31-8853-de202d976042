import { Injectable } from "@angular/core";
import { Subject } from "rxjs";
import { Bank, Branch } from "../bank.model";

@Injectable({
  providedIn: "root",
})
export class BranchDialogService {
  private showCreateDialog = new Subject<string>(); // bankId
  private showEditDialog = new Subject<Branch>();
  private showViewDialog = new Subject<Branch>();

  showCreateDialog$ = this.showCreateDialog.asObservable();
  showEditDialog$ = this.showEditDialog.asObservable();
  showViewDialog$ = this.showViewDialog.asObservable();

  openCreateDialog(bankId: string): void {
    this.showCreateDialog.next(bankId);
  }

  openEditDialog(branch: Branch): void {
    this.showEditDialog.next(branch);
  }

  openViewDialog(branch: Branch): void {
    this.showViewDialog.next(branch);
  }
}
