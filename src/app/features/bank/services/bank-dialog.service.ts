import { Injectable } from "@angular/core";
import { Subject } from "rxjs";
import { Bank } from "../bank.model";

@Injectable({
  providedIn: "root",
})
export class BankDialogService {
  private showCreateDialog = new Subject<void>();
  private showEditDialog = new Subject<Bank>();
  private showViewDialog = new Subject<Bank>();

  showCreateDialog$ = this.showCreateDialog.asObservable();
  showEditDialog$ = this.showEditDialog.asObservable();
  showViewDialog$ = this.showViewDialog.asObservable();

  openCreateDialog(): void {
    this.showCreateDialog.next();
  }

  openEditDialog(bank: Bank): void {
    this.showEditDialog.next(bank);
  }

  openViewDialog(bank: Bank): void {
    this.showViewDialog.next(bank);
  }
}
