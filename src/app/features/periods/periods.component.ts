import { Component, OnInit, inject, signal } from "@angular/core";
import {
  <PERSON><PERSON><PERSON>er,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { TableModule } from "primeng/table";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { DatePipe, DecimalPipe } from "@angular/common";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import {
  CreatePeriodRequest,
  Period,
  UpdatePeriodRequest,
} from "./periods.model";
import { PeriodsService } from "./periods.service";

type ModalKey = "addItem" | "editItem" | "viewItem";

@Component({
  selector: "app-periods",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ModalComponent,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    FormFieldComponent,
    DatePipe,
    FormsModule,
    TableModule,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    ButtonPermissionDirective,
    DecimalPipe,
  ],
  templateUrl: "./periods.component.html",
  providers: [DatePipe],
})
export class PeriodsComponent implements OnInit {
  private periodsService = inject(PeriodsService);
  private fb = inject(FormBuilder);
  private readonly datePipe = inject(DatePipe);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;

  // Signal-based state
  filteredPeriods = signal<Period[]>([]);
  periods = signal<Period[]>([]);

  selectedPeriod: Period | null = null;
  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};
  isDataLoading = false;

  // Data for export
  get exportData(): any[] {
    return this.filteredPeriods().map((period) => ({
      "Period Name": period.name,
      Length: period.length,
      Multiplier: period.multiplier,
      Status: period.status,
      "Is Default": period.isDefault ? "Yes" : "No",
      "Created Date": this.datePipe.transform(
        period.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": period.modifiedDate
        ? this.datePipe.transform(period.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": period.createdUserName ?? "-",
      "Modified By": period.modifiedUserName ?? "-",
    }));
  }

  addPeriodForm = this.fb.nonNullable.group({
    name: [
      "",
      [Validators.required, Validators.minLength(2), Validators.maxLength(50)],
    ],
    length: [1, [Validators.required, Validators.min(1), Validators.max(36)]],
  }) as FormGroup<FormControlType<CreatePeriodRequest>>;

  editPeriodForm = this.fb.nonNullable.group({
    id: [""],
    name: [
      "",
      [Validators.required, Validators.minLength(2), Validators.maxLength(50)],
    ],
    length: [1, [Validators.required, Validators.min(1), Validators.max(36)]],
    multiplier: [1, [Validators.required, Validators.min(1)]],
    status: ["", Validators.required],
  }) as FormGroup<FormControlType<UpdatePeriodRequest>>;

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadData();
    });
  }

  loadData(): void {
    this.isDataLoading = true;
    this.periodsService.getPeriods().subscribe({
      next: (response) => {
        this.periods.set(response.data);
        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);
        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load periods");
        this.isDataLoading = false;
      },
    });
  }

  toggleRow(period: Period): void {
    if (this.expandedRows[period.id]) {
      delete this.expandedRows[period.id];
    } else {
      this.expandedRows[period.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.periods();

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (period) =>
          period?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (period: any) =>
          period.name.toLowerCase().includes(term) ||
          period.length.toString().includes(term) ||
          period.multiplier.toString().includes(term) ||
          period.status.toLowerCase().includes(term),
      );
    }

    this.filteredPeriods.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  toggleModal(mode: ModalKey, period?: Period): void {
    this.selectedPeriod = period || null;

    if (mode === "editItem" && period) {
      this.editPeriodForm.patchValue({
        id: period.id,
        name: period.name,
        length: period.length,
        multiplier: period.multiplier,
        status: period.status,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  handlePeriodSubmit(mode: "add" | "update"): void {
    const form = mode === "add" ? this.addPeriodForm : this.editPeriodForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.periodsService.createPeriod(form.value as CreatePeriodRequest)
        : this.periodsService.updatePeriod(form.value as UpdatePeriodRequest);

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Periods has been added successfully`
              : `Periods has been updated successfully`,
          type: "success",
        });
        this.loadData();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(error, `Failed to ${mode} periods: ${error.message}`);
      },
    });
  }
}
