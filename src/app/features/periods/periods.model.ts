import { Status } from "@/app/core/enums/status.enum";
import { BaseResponse } from "@/app/core/interfaces/common.interface";

export interface Period extends BaseResponse {
  name: string;
  length: number;
  multiplier: number;
  isDefault: boolean;
}

export interface CreatePeriodRequest {
  name: string;
  length: number;
}

export interface UpdatePeriodRequest {
  id: string;
  name: string;
  length: number;
  multiplier: number;
  status: Status;
}
