<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter dropdown -->
      <div class="mr-3 flex items-center">
        <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
        <div class="relative flex items-center">
          <i class="pi pi-filter absolute left-2 text-gray-500"></i>
          <select
            [ngModel]="statusFilter"
            (ngModelChange)="updateStatusFilter($event)"
            class="pl-8 pr-4 py-2 border rounded-lg text-sm"
            >
            <option [value]="null">All Statuses</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
        </div>
      </div>
    </ng-template>

    <ng-template #end>
      <div class="flex gap-2">
        <app-button
          size="sm"
          class="!gap-2"
          variant="secondary"
          (click)="
            reportExportService.exportToExcel(exportData, 'periods.xlsx')
          "
          >
          <i class="text-sm pi pi-upload"></i>
          Export
        </app-button>
        <ng-container *appButtonPermission="'create'">
          <app-button size="sm" (click)="toggleModal('addItem')">
            <i class="text-sm pi pi-plus"></i>
            Create
          </app-button>
        </ng-container>
      </div>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isDataLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading periods...</span>
      </div>
    </div>
  } @else {
    <!-- Periods Table -->
    <p-table
      [value]="filteredPeriods()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
      >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold">Manage Periods</app-text>
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />
            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search periods..."
              />
          </p-iconfield>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>

          <th pSortableColumn="createdDate">
            <div class="flex items-center gap-2">
              Created Date
              <p-sortIcon field="createdDate" />
            </div>
          </th>

          <th pSortableColumn="name">
            <div class="flex items-center justify-between gap-2">
              Name
              <p-sortIcon field="name" />
              <p-columnFilter
                type="text"
                field="name"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>

          <th pSortableColumn="length">
            <div class="flex items-center justify-between gap-2">
              Length
              <p-sortIcon field="length" />
              <p-columnFilter
                type="numeric"
                field="length"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>

          <th pSortableColumn="multiplier">
            <div class="flex items-center justify-between gap-2">
              Multiplier
              <p-sortIcon field="multiplier" />
              <p-columnFilter
                type="numeric"
                field="multiplier"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>

          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>

          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-period let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(period)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ period.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ period.name }}</td>
          <td>{{ period.length }}</td>
          <td>{{ period.multiplier | number: "1.2-2" }}</td>
          <td>{{ period.status }}</td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button
                  size="icon"
                  (click)="toggleModal('viewItem', period)"
                  >
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', period)"
                  >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-period>
        <tr>
          <td colspan="7">
            <div class="p-4 bg-gray-50 rounded-md">
              <div
                class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
                >
                <!-- Basic Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">Name</h5>
                  <p class="text-gray-800">{{ period.name }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Length
                  </h5>
                  <p class="text-gray-800">{{ period.length }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Multiplier
                  </h5>
                  <p class="text-gray-800">
                    {{ period.multiplier | number: "1.2-2" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">
                    {{ period.status }}
                  </p>
                </div>

                @if (period.isDefault) {
                  <div>
                    <h5 class="text-sm font-semibold text-gray-600 mb-1">
                      Default Period
                    </h5>
                    <p class="text-gray-800">Yes</p>
                  </div>
                }

                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ period.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                    period.modifiedDate
                    ? (period.modifiedDate | date: "yyyy-MM-dd HH:mm:ss")
                    : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ period.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ period.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  (click)="toggleModal('editItem', period)"
                  >
                  Edit
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="7" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No periods found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- Add Period Modal -->
<app-modal [isVisible]="modals.addItem" [config]="{ title: 'Add Period' }">
  <form
    [formGroup]="addPeriodForm"
    (ngSubmit)="handlePeriodSubmit('add')"
    class="flex flex-col gap-6"
    >
    <div class="grid gap-3 md:grid-cols-2">
      <app-form-field
        label="Name"
        type="select"
        [options]="[
          { value: 'DAY', label: 'Day' },
          { value: 'WEEK', label: 'Week' },
          { value: 'MONTH', label: 'Month' },
          { value: 'YEAR', label: 'Year' },
        ]"
        [control]="addPeriodForm.controls.name"
        [required]="true"
        />

      <app-form-field
        label="Length"
        placeholder="Length"
        type="number"
        min="1"
        max="36"
        [control]="addPeriodForm.controls.length"
        [required]="true"
        />
    </div>

    <div class="flex items-center justify-end gap-3">
      <app-button (click)="toggleModal('addItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- Edit Period Modal -->
<app-modal [isVisible]="modals.editItem" [config]="{ title: 'Edit Period' }">
  <form [formGroup]="editPeriodForm" (ngSubmit)="handlePeriodSubmit('update')">
    <div class="flex flex-col gap-3">
      <app-form-field
        label="Name"
        type="text"
        [control]="editPeriodForm.get('name')!"
        [required]="true"
        />

      <app-form-field
        label="Length"
        type="number"
        [control]="editPeriodForm.get('length')!"
        [required]="true"
        />

      <app-form-field
        label="Multiplier"
        type="number"
        [control]="editPeriodForm.get('multiplier')!"
        [required]="true"
        />

      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
        [control]="editPeriodForm.get('status')!"
        [required]="true"
        />
    </div>

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="toggleModal('editItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- View Period Modal -->
<app-modal [isVisible]="modals.viewItem" [config]="{ title: 'View Period' }">
  <div class="flex flex-col gap-3">
    <div>
      <label class="form-label">Name</label>
      <input
        type="text"
        [value]="selectedPeriod?.name"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Length</label>
      <input
        type="text"
        [value]="selectedPeriod?.length"
        class="form-control"
        disabled
        />
    </div>
    <div>
      <label class="form-label">Multiplier</label>
      <input
        type="text"
        [value]="selectedPeriod?.multiplier"
        class="form-control"
        disabled
        />
    </div>

    <div>
      <label class="form-label">Status</label>
      <input
        type="text"
        [value]="selectedPeriod?.status"
        class="form-control"
        disabled
        />
    </div>

    <div class="flex justify-end">
      <app-button (click)="toggleModal('viewItem')">Close</app-button>
    </div>
  </div>
</app-modal>

<p-toast />
