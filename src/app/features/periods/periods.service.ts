import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { CreatePeriodRequest, UpdatePeriodRequest } from "./periods.model";

@Injectable({
  providedIn: "root",
})
export class PeriodsService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getPeriods(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/periods`);
  }

  public createPeriod(periodData: CreatePeriodRequest): Observable<any> {
    return this.http.post<any>(`${this.apiServerUrl}/periods`, periodData);
  }

  public updatePeriod(periodData: UpdatePeriodRequest): Observable<any> {
    return this.http.put<any>(`${this.apiServerUrl}/periods`, periodData);
  }
}
