import { DatePipe, TitleCasePipe } from "@angular/common";
import { Component, inject, OnInit, signal } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye, faShield } from "@fortawesome/free-solid-svg-icons";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { AdminUser } from "@/app/core/models/userInfo.model";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { Option } from "@/app/core/utils/map-to-options.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import {
  customEmailValidator,
  onlyLettersAndHyphensValidator,
} from "@/app/core/validators/custom.validators";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import Swal from "sweetalert2";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import { AuthService } from "../auth/core/services/auth.service";
import { Department } from "../department/department.model";
import { DepartmentsService } from "../department/department.service";
import { Role } from "../role/role.model";
import { RolesService } from "../role/role.service";
import { Admin, CreateAdminRequest, UpdateAdminRequest } from "./admins.model";
import { AdminsService } from "./admins.service";

type ModalKey = "addItem" | "editItem" | "viewItem";

@Component({
  selector: "app-admins",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ModalComponent,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    FormFieldComponent,
    ButtonPermissionDirective,
    DatePipe,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule
],
  templateUrl: "./admins.component.html",
  providers: [TitleCasePipe, DatePipe],
})
export class AdminsComponent implements OnInit {
  private adminsService = inject(AdminsService);
  private departmentsService = inject(DepartmentsService);
  private rolesService = inject(RolesService);
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private titleCasePipe = inject(TitleCasePipe);
  private readonly datePipe = inject(DatePipe);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;
  readonly passwordIcon = faShield;

  filteredAdmins = signal<Admin[]>([]);
  admins = signal<Admin[]>([]);
  departments: Department[] = [];
  roles: Role[] = [];
  adminTables: any[] = [];
  selectedAdmin: Admin | null = null;
  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  get exportData(): any[] {
    return this.filteredAdmins().map((admin: any) => ({
      "First Name": admin.firstName,
      "Last Name": admin.lastName,
      Email: admin.email,
      Department: admin.department.department,
      Role: this.titleCasePipe.transform(
        admin.role.role
          ?.replaceAll("ROLE_", "")
          ?.toLowerCase()
          ?.replaceAll("_", " "),
      ),
      "Created Date": this.datePipe.transform(
        admin.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": admin.modifiedDate
        ? this.datePipe.transform(admin.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": admin.createdUserName ?? "-",
      "Modified By": admin.modifiedUserName ?? "-",
      Status: admin.status,
    }));
  }

  get adminTableOptions(): Option[] {
    return this.adminTables.map((table) => ({
      value: table,
      label: table,
    }));
  }

  get exchangeOptions(): Option[] {
    return [
      { value: Exchange.VFEX, label: Exchange.VFEX },
      { value: Exchange.ZSE, label: Exchange.ZSE },
    ];
  }

  get departmentOptions(): Option[] {
    return this.departments.map((item) => ({
      label: item.department,
      value: item.id,
    }));
  }

  get roleOptions(): Option[] {
    return this.roles.map((item) => ({
      label: this.titleCasePipe.transform(
        item.role?.replaceAll("ROLE_", "")?.toLowerCase()?.replaceAll("_", " "),
      ),
      value: item.id,
    }));
  }

  get currentUser(): AdminUser | null {
    return this.authService.currentUser;
  }

  addAdminForm = this.fb.nonNullable.group({
    firstName: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    lastName: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    email: ["", [Validators.required, customEmailValidator()]],
    departmentId: ["", [Validators.required, Validators.minLength(2)]],
    roleId: ["", [Validators.required, Validators.minLength(2)]],
  }) as FormGroup<FormControlType<CreateAdminRequest>>;

  editAdminForm = this.fb.nonNullable.group({
    id: [""],
    firstName: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    lastName: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    email: [
      "",
      [Validators.required, Validators.minLength(2), customEmailValidator()],
    ],
    departmentId: ["", [Validators.required, Validators.minLength(2)]],
    roleId: ["", [Validators.required, Validators.minLength(2)]],
    status: ["", Validators.required],
  }) as FormGroup<FormControlType<UpdateAdminRequest>>;

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  isDataLoading = false;
  isDepartmentLoading = false;
  isRoleLoading = false;

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadData();
    });
  }

  loadData(): void {
    this.loadAdmins();
    this.loadDepartments();
    this.loadRoles();
  }

  toggleRow(admin: Admin): void {
    if (this.expandedRows[admin.id]) {
      delete this.expandedRows[admin.id];
    } else {
      this.expandedRows[admin.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.admins();

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (admin) => admin?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (admin) =>
          admin.firstName.toLowerCase().includes(term) ||
          admin.lastName.toLowerCase().includes(term) ||
          admin.email.toLowerCase().includes(term) ||
          admin.department.department.toLowerCase().includes(term) ||
          (admin.role.role?.toLowerCase() || "").includes(term),
      );
    }

    this.filteredAdmins.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  loadAdmins(): void {
    this.isDataLoading = true;
    this.adminsService.getAdmins().subscribe({
      next: (response) => {
        this.admins.set(response.data);

        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);

        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load admins");
        this.isDataLoading = false;
      },
    });
  }

  loadDepartments() {
    this.isDepartmentLoading = true;
    this.departmentsService.getActiveDepartments().subscribe({
      next: (response) => {
        this.departments = response.data;
        this.isDepartmentLoading = false;
      },
    });
  }

  loadRoles() {
    this.isRoleLoading = true;
    this.rolesService.getActiveRoles().subscribe({
      next: (response) => {
        this.roles = response.data;
        this.isRoleLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load roles");
        this.isRoleLoading = false;
      },
    });
  }

  toggleModal(mode: ModalKey, admin?: Admin): void {
    this.selectedAdmin = admin || null;

    if (mode === "editItem" && admin) {
      this.editAdminForm.patchValue({
        id: admin.id,
        firstName: admin.firstName,
        lastName: admin.lastName,
        email: admin.email,
        departmentId: admin.department.id,
        roleId: admin.role.id,
        status: admin.status,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  resetPassword(admin?: Admin): void {
    if (this.currentUser?.id == admin?.id) {
      Swal.fire(
        "Denied",
        `You can't reset your own password. Please contact the admin to reset it for you`,
        "info",
      );
    } else {
      if (!admin) return;

      Swal.fire({
        title: "Are you sure?",
        text: `Do you really want to reset ${admin.firstName} ${admin.lastName}'s password?`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, reset it!",
        cancelButtonText: "No, cancel",
      }).then((result) => {
        if (result.isConfirmed) {
          this.selectedAdmin = admin;

          this.editAdminForm.patchValue({
            id: admin.id,
            firstName: admin.firstName,
            lastName: admin.lastName,
            email: admin.email,
            departmentId: admin.department.id,
            roleId: admin.role.id,
            status: admin.status,
          });

          this.adminsService
            .resetAdminPassword(this.editAdminForm.value as UpdateAdminRequest)
            .subscribe({
              next: () => {
                Swal.fire(
                  "Reset!",
                  `${admin.firstName} ${admin.lastName}'s password has been reset.`,
                  "success",
                );
                this.loadAdmins();
              },
              error: (error) => {
                Swal.fire(
                  "Error!",
                  `Failed to reset ${admin.firstName} ${admin.lastName}'s password.`,
                  "error",
                );
                console.error(error);
              },
            });
        }
      });
    }
  }

  handleAdminSubmit(mode: "add" | "update"): void {
    const form = mode === "add" ? this.addAdminForm : this.editAdminForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.adminsService.createAdmin(form.value as CreateAdminRequest)
        : this.adminsService.updateAdmin(form.value as UpdateAdminRequest);

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Admin has been added successfully`
              : `Admin has been updated successfully`,
          type: "success",
        });
        this.loadAdmins();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(error, `Failed to ${mode} admins`);
      },
    });
  }
}
