import { Status } from "@/app/core/enums/status.enum";
import { BaseResponse } from "@/app/core/interfaces/common.interface";
import { Department } from "../department/department.model";
import { Role } from "../role/role.model";

export interface Admin extends BaseResponse {
  firstName: string;
  lastName: string;
  email: string;
  department: Department;
  role: Role;
}

export interface CreateAdminRequest {
  firstName: string;
  lastName: string;
  email: string;
  departmentId: string;
  roleId: string;
}

export interface UpdateAdminRequest {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  departmentId: string;
  roleId: string;
  status: Status;
}
