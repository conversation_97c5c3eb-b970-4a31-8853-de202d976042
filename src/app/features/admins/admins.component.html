<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <!-- Status filter dropdown -->
      <div class="mr-3 flex items-center">
        <span class="mr-2 text-sm font-medium text-gray-700">Status:</span>
        <div class="relative flex items-center">
          <i class="pi pi-filter absolute left-2 text-gray-500"></i>
          <select
            [(ngModel)]="statusFilter"
            (ngModelChange)="updateStatusFilter($event)"
            class="pl-8 pr-4 py-2 border rounded-lg text-sm"
          >
            <option [value]="null">All Statuses</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
          </select>
        </div>
      </div>
    </ng-template>

    <ng-template #end>
      <div class="flex gap-2">
        <ng-container *appButtonPermission="'create'">
          <app-button
            size="sm"
            variant="primary"
            class="!gap-2"
            (click)="toggleModal('addItem')"
          >
            <i class="text-sm pi pi-plus"></i>
            Create
          </app-button>
        </ng-container>
        <app-button
          size="sm"
          class="!gap-2"
          variant="secondary"
          (click)="reportExportService.exportToExcel(exportData, 'admins.xlsx')"
        >
          <i class="text-sm pi pi-upload"></i>
          Export
        </app-button>
      </div>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isDataLoading || isDepartmentLoading || isRoleLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading admins...</span>
      </div>
    </div>
  } @else {
    <!-- Admins Table -->
    <p-table
      [value]="filteredAdmins()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
    >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold">Manage Admins</app-text>
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />
            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search admins..."
            />
          </p-iconfield>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>
          <th pSortableColumn="firstName">
            <div class="flex items-center justify-between gap-2">
              First Name
              <p-sortIcon field="firstName" />
              <p-columnFilter
                type="text"
                field="firstName"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="lastName">
            <div class="flex items-center justify-between gap-2">
              Last Name
              <p-sortIcon field="lastName" />
              <p-columnFilter
                type="text"
                field="lastName"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="email">
            <div class="flex items-center justify-between gap-2">
              Email
              <p-sortIcon field="email" />
              <p-columnFilter
                type="text"
                field="email"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="department.department">
            <div class="flex items-center justify-between gap-2">
              Department
              <p-sortIcon field="department.department" />
              <p-columnFilter
                type="text"
                field="department.department"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="role.role">
            <div class="flex items-center justify-between gap-2">
              Role
              <p-sortIcon field="role.role" />
              <p-columnFilter
                type="text"
                field="role.role"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th pSortableColumn="createdDate">
            <div class="flex items-center gap-2">
              Created Date
              <p-sortIcon field="createdDate" />
            </div>
          </th>
          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
              />
            </div>
          </th>
          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-admin let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(admin)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ admin.firstName }}</td>
          <td>{{ admin.lastName }}</td>
          <td>{{ admin.email }}</td>
          <td>{{ admin.department.department }}</td>
          <td class="capitalize">
            {{
              admin.role.role
                ?.replaceAll("ROLE_", "")
                ?.toLowerCase()
                ?.replaceAll("_", " ")
            }}
          </td>
          <td>{{ admin.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ admin.status }}</td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button
                  size="icon"
                  (click)="toggleModal('viewItem', admin)"
                >
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', admin)"
                >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="warning"
                  size="icon"
                  (click)="resetPassword(admin)"
                >
                  <fa-icon [icon]="passwordIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-admin>
        <tr>
          <td colspan="9">
            <div class="p-4 bg-gray-50 rounded-md">
              <div
                class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
              >
                <!-- Basic Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    First Name
                  </h5>
                  <p class="text-gray-800">{{ admin.firstName }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Last Name
                  </h5>
                  <p class="text-gray-800">{{ admin.lastName }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Email
                  </h5>
                  <p class="text-gray-800">{{ admin.email }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Department
                  </h5>
                  <p class="text-gray-800">{{ admin.department.department }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">Role</h5>
                  <p class="text-gray-800 capitalize">
                    {{
                      admin.role.role
                        ?.replaceAll("ROLE_", "")
                        ?.toLowerCase()
                        ?.replaceAll("_", " ")
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">{{ admin.status }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ admin.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                      admin.modifiedDate
                        ? (admin.modifiedDate | date: "yyyy-MM-dd HH:mm:ss")
                        : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ admin.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ admin.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  (click)="toggleModal('editItem', admin)"
                >
                  Edit
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="9" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No admins found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- Add Admin Modal -->
<app-modal [isVisible]="modals.addItem" [config]="{ title: 'Add Admin' }">
  <form
    [formGroup]="addAdminForm"
    (ngSubmit)="handleAdminSubmit('add')"
    class="flex flex-col gap-6"
  >
    <div class="grid gap-3 md:grid-cols-2">
      <app-form-field
        label="First Name"
        type="text"
        [control]="addAdminForm.controls.firstName"
        [required]="true"
        placeholder="First Name"
      />
      <app-form-field
        label="Last Name"
        type="text"
        [control]="addAdminForm.controls.lastName"
        [required]="true"
        placeholder="Last Name"
      />
      <app-form-field
        label="Email"
        type="text"
        [control]="addAdminForm.controls.email"
        [required]="true"
        placeholder="Email"
      />
      <app-form-field
        label="Department"
        type="select"
        [options]="departmentOptions"
        [control]="addAdminForm.controls.departmentId"
        [required]="true"
      />
      <app-form-field
        label="Role"
        type="select"
        [options]="roleOptions"
        [control]="addAdminForm.controls.roleId"
        [required]="true"
      />
    </div>

    <div class="flex items-center justify-end gap-3">
      <app-button (click)="toggleModal('addItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- Edit Admin Modal -->
<app-modal [isVisible]="modals.editItem" [config]="{ title: 'Edit Admin' }">
  <form [formGroup]="editAdminForm" (ngSubmit)="handleAdminSubmit('update')">
    <div class="flex flex-col gap-3">
      <app-form-field
        label="First Name"
        type="text"
        [control]="editAdminForm.controls.firstName!"
        [required]="true"
      />
      <app-form-field
        label="Last Name"
        type="text"
        [control]="editAdminForm.controls.lastName!"
        [required]="true"
      />
      <app-form-field
        label="Email"
        type="text"
        [control]="editAdminForm.controls.email!"
        [required]="true"
      />
      <app-form-field
        label="Department"
        type="select"
        [options]="departmentOptions"
        [control]="editAdminForm.controls.departmentId!"
        [required]="true"
      />
      <app-form-field
        inputClass="capitalize"
        label="Role"
        type="select"
        [options]="roleOptions"
        [control]="editAdminForm.controls.roleId!"
        [required]="true"
      />

      <app-form-field
        label="Status"
        type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
        [control]="editAdminForm.get('status')!"
        [required]="true"
      />
    </div>

    <div class="flex items-center justify-end gap-3 mt-4">
      <app-button (click)="toggleModal('editItem')" variant="outline">
        Cancel
      </app-button>
      <app-button type="submit">Submit</app-button>
    </div>
  </form>
</app-modal>

<!-- View Admin Modal -->
<app-modal [isVisible]="modals.viewItem" [config]="{ title: 'View Admin' }">
  <div class="flex flex-col gap-3">
    <div class="flex flex-col gap-3">
      <div>
        <label class="form-label">First Name</label>
        <input
          type="text"
          [value]="selectedAdmin?.firstName"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Last Name</label>
        <input
          type="text"
          [value]="selectedAdmin?.lastName"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Email</label>
        <input
          type="text"
          [value]="selectedAdmin?.email"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Department</label>
        <input
          type="text"
          [value]="selectedAdmin?.department?.department"
          class="form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Role</label>
        <input
          type="text"
          [value]="
            selectedAdmin?.role?.role
              ?.replaceAll('ROLE_', '')
              ?.toLowerCase()
              ?.replaceAll('_', ' ')
          "
          class="capitalize form-control"
          disabled
        />
      </div>
      <div>
        <label class="form-label">Status</label>
        <input
          type="text"
          [value]="selectedAdmin?.status"
          class="form-control"
          disabled
        />
      </div>
    </div>

    <div class="flex justify-end">
      <app-button (click)="toggleModal('viewItem')">Close</app-button>
    </div>
  </div>
</app-modal>

<p-toast />
