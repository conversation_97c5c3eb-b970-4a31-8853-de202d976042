import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { CreateAdminRequest, UpdateAdminRequest } from "./admins.model";

@Injectable({
  providedIn: "root",
})
export class AdminsService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getAdmins(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/admin/admin-users`);
  }

  public createAdmin(adminData: CreateAdminRequest): Observable<any> {
    return this.http.post<any>(
      `${this.apiServerUrl}/admin/admin-users`,
      adminData,
    );
  }

  public updateAdmin(adminData: UpdateAdminRequest): Observable<any> {
    return this.http.put<any>(
      `${this.apiServerUrl}/admin/admin-users`,
      adminData,
    );
  }

  public resetAdminPassword(adminData: UpdateAdminRequest): Observable<any> {
    return this.http.put<any>(
      `${this.apiServerUrl}/admin/admin-users/reset-password`,
      adminData,
    );
  }
}
