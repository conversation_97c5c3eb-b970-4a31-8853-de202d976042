<div class="flex items-center gap-6">
  <div
    class="{{ bgColor }} size-16 flex justify-center items-center rounded-lg"
  >
    <fa-icon [icon]="icon" class="text-2xl {{ color }}"></fa-icon>
  </div>

  <div className="">
    <div class="flex items-center gap-2">
      <app-text variant="small/semibold">{{ title }}</app-text>

      @if (subtitle) {
        <span class="text-xs font-semibold"> ({{ subtitle }}) </span>
      }
    </div>
    <app-text variant="title/semibold">
      {{ value | currency: "USD" : "symbol-narrow" }}
    </app-text>
  </div>
</div>
