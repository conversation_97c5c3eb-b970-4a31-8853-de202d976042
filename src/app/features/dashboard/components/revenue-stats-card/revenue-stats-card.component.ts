import {
  TailwindBgColorClass,
  TailwindTextColorClass,
} from "@/app/core/types/tailwind-types";
import { CommonModule } from "@angular/common";
import { Component, Input } from "@angular/core";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { IconDefinition } from "@fortawesome/free-solid-svg-icons";
import { TextComponent } from "../../../../shared/ui/text/text.component";

interface StatItem {
  label: string;
  value: number;
  color: string;
  prefix?: string;
}

@Component({
  selector: "app-revenue-stats-card",
  standalone: true,
  imports: [CommonModule, TextComponent, FontAwesomeModule],
  templateUrl: "./revenue-stats-card.component.html",
})
export class RevenueStatsCardComponent {
  @Input() title!: string;
  @Input() subtitle?: string;
  @Input() value!: string | number;
  @Input() color!: TailwindTextColorClass;
  @Input() bgColor!: TailwindBgColorClass;
  @Input() icon!: IconDefinition;
}
