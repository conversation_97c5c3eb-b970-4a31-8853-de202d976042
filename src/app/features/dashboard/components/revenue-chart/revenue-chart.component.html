@if (isYearlyRevenueStatsLoading()) {
  <div class="flex justify-center items-center w-full">
    <app-spinner></app-spinner>
  </div>
} @else {
  <app-card>
    <div class="flex flex-col gap-6">
      <div class="flex justify-between gap-3">
        <app-text variant="title/semibold">
          Yearly Subscriptions vs Revenue generated
        </app-text>

        <div class="w-14">
          <p-datepicker
            inputStyleClass="border-none shadow-none"
            size="small"
            variant="filled"
            [(ngModel)]="date"
            view="year"
            dateFormat="yy"
            [maxDate]="maxDate"
            [readonlyInput]="true"
            (ngModelChange)="loadYearlyRevenueStats()"
          />
        </div>
      </div>

      <!-- Revenue Chart -->
      <div class="border-b pb-4">
        <app-text variant="small/medium" class="mb-2 text-amber-500"
          >Revenue Trend</app-text
        >
        <p-chart
          type="line"
          [data]="revenueData"
          [options]="revenueOptions"
          class="h-40"
        />
      </div>

      <!-- Subscriptions Chart -->
      <div>
        <app-text variant="small/medium" class="mb-2 text-teal-700"
          >Subscription Trend</app-text
        >
        <p-chart
          type="bar"
          [data]="subscriptionData"
          [options]="subscriptionOptions"
          class="h-40"
        />
      </div>
    </div>
  </app-card>
}
