import { ApiResponse } from "@/app/core/interfaces/api-response.interface";
import { isPlatformBrowser } from "@angular/common";
import {
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
  PLATFORM_ID,
  signal,
} from "@angular/core";
import { FormsModule } from "@angular/forms";
import { ChartModule } from "primeng/chart";
import { DatePicker } from "primeng/datepicker";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { SpinnerComponent } from "../../../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { YearlyRevenueStats } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";

@Component({
  selector: "dash-revenue-chart",
  templateUrl: "./revenue-chart.component.html",
  standalone: true,
  imports: [
    ChartModule,
    CardComponent,
    TextComponent,
    DatePicker,
    FormsModule,
    SpinnerComponent,
  ],
})
export class RevenueChartComponent implements OnInit {
  // Data for revenue chart
  revenueData: any;
  revenueOptions: any;

  // Data for subscription chart
  subscriptionData: any;
  subscriptionOptions: any;

  // Original data from API
  yearlyRevenueStats: YearlyRevenueStats | null = null;

  date: Date = new Date();
  maxDate: Date | undefined;

  platformId = inject(PLATFORM_ID);
  dashboardService = inject(DashboardService);

  isYearlyRevenueStatsLoading = signal(true);

  constructor(private cd: ChangeDetectorRef) {}

  ngOnInit() {
    this.maxDate = new Date();
    this.loadYearlyRevenueStats();

    if (isPlatformBrowser(this.platformId)) {
      window.addEventListener("resize", this.onResize.bind(this));
    }
  }

  ngOnDestroy() {
    if (isPlatformBrowser(this.platformId)) {
      window.removeEventListener("resize", this.onResize.bind(this));
    }
  }

  initCharts() {
    const documentStyle = getComputedStyle(document.documentElement);
    const textColorSecondary = documentStyle.getPropertyValue(
      "--p-text-muted-color",
    );
    const surfaceBorder = documentStyle.getPropertyValue(
      "--p-content-border-color",
    );
    const revenueColor = documentStyle.getPropertyValue("--p-amber-500");
    const subscriptionColor = documentStyle.getPropertyValue("--p-teal-700");

    // Shared configuration for both charts
    const sharedConfig = {
      scales: {
        x: {
          ticks: {
            color: textColorSecondary,
          },
          grid: {
            display: false,
          },
          border: {
            display: false,
          },
        },
      },
      interaction: {
        mode: "index",
        intersect: false,
      },
      plugins: {
        legend: {
          display: false, // Hide legend for small multiples to save space
        },
      },
      maintainAspectRatio: false,
      responsive: true,
    };

    // Initialize Revenue Chart
    this.revenueData = {
      labels: this.yearlyRevenueStats?.monthlyLabels,
      datasets: [
        {
          label: "Revenue",
          data: this.yearlyRevenueStats?.revenue,
          borderColor: revenueColor,
          backgroundColor: (context: any) => {
            const ctx = context.chart.ctx;
            const gradient = ctx.createLinearGradient(
              0,
              0,
              0,
              context.chart.height,
            );
            gradient.addColorStop(0, "rgba(251, 191, 36, 0.1)");
            gradient.addColorStop(0.7, "rgba(251, 191, 36, 0.0)");
            gradient.addColorStop(1, "rgba(251, 191, 36, 0.0)");
            return gradient;
          },
          borderWidth: 2,
          tension: 0.4,
          fill: true,
          pointRadius: 0,
          borderCapStyle: "round",
          borderJoinStyle: "round",
          cubicInterpolationMode: "monotone",
        },
      ],
    };

    this.revenueOptions = {
      ...sharedConfig,
      scales: {
        ...sharedConfig.scales,
        y: {
          beginAtZero: true,
          ticks: {
            color: revenueColor,
            font: {
              weight: "bold",
            },
            callback: function (value: any) {
              // Format as currency with K/M suffixes for readability
              if (value >= 1000000) {
                return "$" + (value / 1000000).toFixed(1) + "M";
              } else if (value >= 1000) {
                return "$" + (value / 1000).toFixed(1) + "K";
              }
              return "$" + value;
            },
          },
          grid: {
            color: surfaceBorder,
            drawTicks: false,
            dash: [3, 3],
          },
          border: {
            dash: [3, 3],
            display: false,
          },
        },
      },
      plugins: {
        ...sharedConfig.plugins,
        tooltip: {
          callbacks: {
            label: (tooltipItem: any) => {
              const value = tooltipItem.raw;
              return `Revenue: ${value.toLocaleString("en-US", {
                style: "currency",
                currency: "USD",
                minimumFractionDigits: 2,
              })}`;
            },
          },
        },
      },
    };

    // Initialize Subscriptions Chart
    this.subscriptionData = {
      labels: this.yearlyRevenueStats?.monthlyLabels,
      datasets: [
        {
          label: "Subscriptions",
          data: this.yearlyRevenueStats?.subscriptions,
          backgroundColor: subscriptionColor,
          borderRadius: 4,
          barThickness: this.getBarThickness(),
          maxBarThickness: 30,
        },
      ],
    };

    this.subscriptionOptions = {
      ...sharedConfig,
      scales: {
        ...sharedConfig.scales,
        y: {
          ticks: {
            color: textColorSecondary,
          },
          border: {
            dash: [3, 3],
            display: false,
          },
          grid: {
            color: surfaceBorder,
            drawOnChartArea: true,
            drawTicks: false,
          },
          beginAtZero: true,
        },
      },
      plugins: {
        ...sharedConfig.plugins,
        tooltip: {
          callbacks: {
            label: (tooltipItem: any) => {
              const value = tooltipItem.raw;
              return `Subscriptions: ${value.toLocaleString("en-US")}`;
            },
          },
        },
      },
    };

    this.cd.markForCheck();
  }

  onResize() {
    if (
      this.subscriptionData &&
      this.subscriptionData.datasets &&
      this.subscriptionData.datasets.length > 0
    ) {
      this.subscriptionData.datasets[0].barThickness = this.getBarThickness();
      this.cd.markForCheck();
    }
  }

  getBarThickness(): number {
    if (!isPlatformBrowser(this.platformId)) return 20;

    const screenWidth = window.innerWidth;

    if (screenWidth <= 600) {
      return 10; // Smaller bar width for small screens (mobile)
    } else if (screenWidth <= 1200) {
      return 15; // Medium bar width for tablets
    } else {
      return 20; // Default bar width for larger screens (desktop)
    }
  }

  loadYearlyRevenueStats() {
    this.isYearlyRevenueStatsLoading.set(true);
    this.dashboardService
      .getYearlyRevenueStats(this.date.getFullYear())
      .subscribe({
        next: (stats: ApiResponse<YearlyRevenueStats>) => {
          this.yearlyRevenueStats = stats.data;
          this.initCharts();
          this.isYearlyRevenueStatsLoading.set(false);
        },
        error: (error) => {
          this.isYearlyRevenueStatsLoading.set(false);
          console.error("Failed to load yearly revenue stats:", error);
        },
      });
  }
}
