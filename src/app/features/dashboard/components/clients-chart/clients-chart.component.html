<app-card>
  <div class="flex flex-col gap-6">
    <div class="flex items-center justify-between gap-3">
      <app-text variant="small/semibold"> {{ title }} </app-text>

      <div class="w-[86px]">
        <p-datepicker
          inputStyleClass="border-none shadow-none"
          size="small"
          variant="filled"
          [(ngModel)]="date"
          view="month"
          dateFormat="M/yy"
          [maxDate]="maxDate"
          [readonlyInput]="true"
        />
      </div>
    </div>

    <div class="px-12 sm:px-16 md:px-12 xl:px-6">
      <p-chart type="doughnut" [data]="data" [options]="options" />
    </div>

    <table class="w-full">
      <tbody>
        @for (label of chartLabels; track label; let i = $index) {
          <tr class="{{ i !== chartLabels.length - 1 ? 'border-b' : '' }}">
            <td class="flex items-center gap-3 py-3">
              <div
                class="rounded-full size-3"
                [style.backgroundColor]="backgroundColors[i]"
              ></div>
              <app-text variant="small/medium">{{ label }}</app-text>
            </td>
            <td class="py-3 text-right">
              <app-text variant="small/medium">
                {{ chartValues[i] }}
              </app-text>
            </td>
            <td class="py-3 text-right">
              <app-text variant="small/medium">
                {{ calculatePercentage(i) }}%
              </app-text>
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
</app-card>
