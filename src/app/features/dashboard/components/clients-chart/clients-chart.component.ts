import { isPlat<PERSON><PERSON>rowser } from "@angular/common";
import {
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  OnInit,
  PLATFORM_ID,
} from "@angular/core";
import { ChartModule } from "primeng/chart";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { DashboardService } from "../../dashboard.service";
import { DatePicker } from "primeng/datepicker";
import { FormsModule } from "@angular/forms";

@Component({
  selector: "dash-clients-chart",
  templateUrl: "./clients-chart.component.html",
  standalone: true,
  imports: [ChartModule, CardComponent, TextComponent, DatePicker, FormsModule],
})
export class ClientsChartComponent implements OnInit {
  @Input({ required: true }) chartData!: Record<string, number>;
  @Input({ required: true }) backgroundColors!: string[];
  @Input({ required: true }) title!: string;

  data: any;
  options: any;

  platformId = inject(PLATFORM_ID);
  dashboardService = inject(DashboardService);
  documentStyle = getComputedStyle(document.documentElement);

  date: Date = new Date();
  maxDate: Date | undefined;

  get chartValues() {
    return Object.values(this.chartData);
  }

  get chartLabels() {
    return Object.keys(this.chartData);
  }

  constructor(private cd: ChangeDetectorRef) {}

  ngOnInit() {
    this.maxDate = new Date();
    this.initChart();
  }

  initChart() {
    if (isPlatformBrowser(this.platformId)) {
      this.data = {
        datasets: [
          {
            type: "doughnut",
            backgroundColor: this.backgroundColors,
            data: this.chartValues,
            borderColor: "white",
            borderWidth: 2,
            cutout: "75%",
          },
        ],
      };

      this.options = {
        interaction: {
          mode: "nearest",
          axis: "x",
          intersect: false,
        },
        plugins: {
          tooltip: {
            mode: "nearest",
            intersect: false,
            callbacks: {
              // Customize tooltip label
              label: (context: any) => {
                const label = this.chartLabels[context.dataIndex] || ""; // Get the label from chartLabels
                const value = context.raw;
                const percentage = this.calculatePercentage(context.dataIndex);
                return `${label}: ${value} (${percentage}%)`;
              },
            },
          },
          legend: {
            display: false,
          },
        },
        scales: {
          x: {
            display: false,
          },
          y: {
            display: false,
          },
        },
      };
      this.cd.markForCheck();
    }
  }

  calculatePercentage(index: number): number {
    const total = this.chartValues.reduce((a, b) => a + b, 0);
    return Math.round((this.chartValues[index] / total) * 100);
  }
}
