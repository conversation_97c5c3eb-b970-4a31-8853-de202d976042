
import { Component, Input } from "@angular/core";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";

interface VotItem {
  label: string;
  value: number;
  color: string;
}

@Component({
  selector: "app-vot-tracker",
  standalone: true,
  imports: [CardComponent, TextComponent],
  templateUrl: "./vot-tracker.component.html",
})
export class VotTrackerComponent {
  @Input() votStats!: { items: VotItem[] };
}
