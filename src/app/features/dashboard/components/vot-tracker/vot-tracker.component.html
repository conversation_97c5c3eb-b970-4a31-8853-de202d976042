<app-card>
  <div class="flex flex-col gap-6">
    <app-text variant="title/semibold"> Daily ATS Products Stats </app-text>

    <div class="grid grid-cols-2 gap-4 md:grid-cols-3">
      @for (item of votStats.items; track item.label) {
        <div class="p-4 rounded-lg bg-light">
          <h3 class="text-sm font-medium text-muted">{{ item.label }}</h3>
          <div class="flex items-center mt-2">
            <div class="w-full bg-secondary/20 rounded-full h-2.5">
              <div
                [class]="item.color"
                class="h-2.5 rounded-full transition-all duration-300"
                [style.width.%]="item.value"
              ></div>
            </div>
            <span class="ml-2 text-sm font-medium text-foreground">
              {{ item.value }}%
            </span>
          </div>
        </div>
      }
    </div>
  </div>
</app-card>
