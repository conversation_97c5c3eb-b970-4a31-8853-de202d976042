<app-card>
  <h2 class="mb-4 text-xl font-semibold text-foreground">{{ title }}</h2>
  <div class="space-y-4">
    @for (item of stats.items; track item.label) {
      <div class="flex items-center justify-between">
        <span class="text-muted">{{ item.label }}</span>
        <span class="text-2xl font-bold {{ item.color }}">
          {{ item.prefix }}{{ item.value | number }}
        </span>
      </div>
    }
  </div>
</app-card>
