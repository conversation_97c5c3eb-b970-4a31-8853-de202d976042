
import { Component, Input } from "@angular/core";
import { CardComponent } from "../../../../shared/ui/card/card.component";

interface StatItem {
  label: string;
  value: number;
  color: string;
  prefix?: string;
}

@Component({
  selector: "app-stats-card",
  standalone: true,
  imports: [CardComponent],
  templateUrl: "./stats-card.component.html",
})
export class StatsCardComponent {
  @Input() title!: string;
  @Input() stats!: { items: StatItem[] };
}
