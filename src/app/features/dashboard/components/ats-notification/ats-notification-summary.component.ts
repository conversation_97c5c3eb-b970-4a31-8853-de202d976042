import { CommonModule } from "@angular/common";
import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  inject,
} from "@angular/core";
import { Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import {
  faCheckCircle,
  faChevronRight,
  faExclamationTriangle,
} from "@fortawesome/free-solid-svg-icons";
import { TextComponent } from "../../../../shared/ui/text/text.component";

@Component({
  selector: "app-ats-notification-summary",
  standalone: true,
  imports: [CommonModule, FontAwesomeModule, TextComponent],
  template: `
    <div
      (click)="navigateToSection()"
      class="relative overflow-hidden rounded-xl shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer group"
    >
      <!-- Background with soft gradient -->
      <div
        [ngClass]="
          isComplete
            ? 'from-green-50 to-green-100'
            : 'from-amber-50 to-amber-100'
        "
        class="absolute inset-0 bg-gradient-to-r opacity-50 z-0"
      ></div>

      <!-- Card content -->
      <div class="relative z-10 p-5">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <!-- Status icon with animated pulse effect -->
            <div
              [ngClass]="
                isComplete
                  ? 'bg-green-100 text-green-600'
                  : 'bg-amber-100 text-amber-600'
              "
              class="flex items-center justify-center w-10 h-10 rounded-full"
            >
              <fa-icon
                [icon]="isComplete ? icons.check : icons.warning"
                class="text-lg"
                [ngClass]="!isComplete ? 'animate-pulse' : ''"
              ></fa-icon>
            </div>

            <!-- Content -->
            <div>
              <app-text variant="small/semibold" class="text-gray-800"
                >ATS Report Status</app-text
              >
              <div
                [ngClass]="isComplete ? 'text-green-700' : 'text-amber-700'"
                class="text-sm font-medium"
              >
                {{
                  isComplete
                    ? "All reports published"
                    : pendingCount + " reports pending"
                }}
              </div>
            </div>
          </div>

          <!-- Right arrow with transition effect -->
          <div
            class="bg-white bg-opacity-60 rounded-full w-8 h-8 flex items-center justify-center transform group-hover:translate-x-1 transition-transform"
          >
            <fa-icon [icon]="icons.chevron" class="text-gray-500"></fa-icon>
          </div>
        </div>
      </div>

      <!-- Decorative side indicator -->
      <div
        [ngClass]="isComplete ? 'bg-green-500' : 'bg-amber-500'"
        class="absolute left-0 top-0 w-1 h-full"
      ></div>
    </div>
  `,
})
export class AtsNotificationSummaryComponent implements OnInit {
  @Input() isComplete = false;
  @Input() pendingCount = 0;
  @Output() navigate = new EventEmitter<void>();

  icons = {
    check: faCheckCircle,
    warning: faExclamationTriangle,
    chevron: faChevronRight,
  };

  private router = inject(Router);

  ngOnInit() {}

  navigateToSection() {
    // Emit event to parent component
    this.navigate.emit();
  }
}
