<app-card
  class="overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
  >
  <div class="flex flex-col gap-6">
    <div class="flex justify-between items-center">
      <div class="flex items-center gap-2">
        <div class="w-1 h-8 bg-primary rounded-full"></div>
        <app-text variant="title/semibold" class="text-gray-800"
          >{{ productName }} Details</app-text
          >
        </div>
        <p class="text-sm text-muted bg-gray-50 px-3 py-1 rounded-full">
          {{ date | date: "MMM dd, yyyy" }}
        </p>
      </div>

      <!-- Loading Spinner -->
      @if (isLoading) {
        <div class="flex justify-center items-center w-full py-12">
          <app-spinner></app-spinner>
        </div>
      } @else if (productDetails) {
        <div class="rounded-xl overflow-hidden border border-gray-100 shadow-sm">
          <div class="bg-gray-50 p-4 flex justify-between items-center">
            <div class="flex items-center gap-2">
              <app-text variant="small/semibold">Publication Status</app-text>
              <span class="text-xs text-gray-500">Last updated: Today</span>
            </div>
            <div
              class="flex items-center gap-2 px-3 py-1 rounded-full text-sm"
            [ngClass]="
              productDetails.isFullyPublished
                ? 'bg-green-100 text-green-700'
                : 'bg-amber-100 text-amber-700'
            "
              >
              <fa-icon
              [icon]="
                productDetails.isFullyPublished ? icons.check : icons.warning
              "
                class="text-sm"
              ></fa-icon>
              <span>
                {{
                productDetails.isFullyPublished
                ? "Fully Published"
                : "Publication Pending"
                }}
              </span>
            </div>
          </div>

          <!-- Exchange & Market Status Table -->
          <div class="overflow-x-auto p-4">
            <table class="w-full">
              <thead>
                <tr>
                  <th
                    class="py-3 px-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider border-b"
                    >
                    Exchange
                  </th>
                  <th
                    class="py-3 px-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider border-b"
                    >
                    Market
                  </th>
                  <th
                    class="py-3 px-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider border-b"
                    >
                    Status
                  </th>
                  <th
                    class="py-3 px-4 text-right text-xs font-semibold text-gray-500 uppercase tracking-wider border-b"
                    >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-100">
                <!-- ZSE Exchange -->
                @if (
                  productDetails.publishedExchangeMarkets["ZSE"] &&
                  productDetails.publishedExchangeMarkets["ZSE"].length > 0
                  ) {
                  @for (
                    market of productDetails.publishedExchangeMarkets["ZSE"];
                    track market
                    ) {
                    <tr class="hover:bg-gray-50">
                      <td class="py-3 px-4 whitespace-nowrap">
                        <div class="flex items-center gap-2">
                          <span class="w-2 h-2 rounded-full bg-blue-500"></span>
                          <span class="font-medium text-gray-800">ZSE</span>
                        </div>

                        <div
                          class="flex items-center gap-2 px-3 py-1 rounded-full text-sm"
                        [ngClass]="
                          productDetails.isFullyPublished
                            ? 'bg-green-100 text-green-700'
                            : 'bg-amber-100 text-amber-700'
                        "
                          >
                          <fa-icon
                          [icon]="
                            productDetails.isFullyPublished
                              ? icons.check
                              : icons.warning
                          "
                            class="text-sm"
                          ></fa-icon>
                          <span>
                            {{
                            productDetails.isFullyPublished
                            ? "Fully Published"
                            : "Publication Pending"
                            }}
                          </span>
                        </div>
                        @if (productDetails.specialNote) {
                          <span
                            class="ml-2 text-xs px-2 py-1 bg-blue-50 text-blue-700 rounded-full"
                            >
                            {{ productDetails.specialNote }}
                          </span>
                        }
                      </td>
                      <td class="py-3 px-4 whitespace-nowrap text-gray-700">
                        {{ getMarketDisplayName(market) }}
                      </td>
                      <td class="py-3 px-4 whitespace-nowrap">
                        <div class="flex items-center gap-1">
                          <span
                            [ngClass]="getStatusDisplay('PUBLISHED').cssClass"
                            class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                            >
                            <fa-icon
                              [icon]="getStatusDisplay('PUBLISHED').icon"
                              class="mr-1"
                            ></fa-icon>
                            {{ getStatusDisplay("PUBLISHED").text }}
                          </span>
                        </div>
                      </td>
                      <td class="py-3 px-4 whitespace-nowrap text-right">
                        <p-dropdown
                          [options]="statusOptions"
                          (ngModel)="('PUBLISHED')"
                          (onChange)="updateStatus('ZSE', market, $event.value)"
                          styleClass="text-xs w-32 shadow-sm"
                          placeholder="Update"
                        ></p-dropdown>
                      </td>
                    </tr>
                  }
                }

                @if (
                  productDetails.pendingExchangeMarkets["ZSE"] &&
                  productDetails.pendingExchangeMarkets["ZSE"].length > 0
                  ) {
                  @for (
                    market of productDetails.pendingExchangeMarkets["ZSE"];
                    track market
                    ) {
                    <tr class="hover:bg-gray-50">
                      <td class="py-3 px-4 whitespace-nowrap">
                        <div class="flex items-center gap-2">
                          <span class="w-2 h-2 rounded-full bg-blue-500"></span>
                          <span class="font-medium text-gray-800">ZSE</span>
                        </div>
                      </td>
                      <td class="py-3 px-4 whitespace-nowrap text-gray-700">
                        {{ market }}
                      </td>
                      <td class="py-3 px-4 whitespace-nowrap">
                        <div class="flex items-center gap-1">
                          <span
                            class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-amber-100 text-amber-800"
                            >
                            <fa-icon
                              [icon]="icons.warning"
                              class="mr-1"
                            ></fa-icon>
                            Pending
                          </span>
                        </div>
                      </td>
                      <td class="py-3 px-4 whitespace-nowrap text-right">
                        <div class="flex gap-2 justify-end items-center">
                          <button
                            class="flex items-center gap-1 px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded-md text-xs transition-colors shadow-sm"
                            (click)="publishProduct('ZSE', market)"
                            >
                            <fa-icon
                              [icon]="icons.check"
                              class="text-xs"
                            ></fa-icon>
                            Publish
                          </button>
                          <p-dropdown
                            [options]="statusOptions"
                            (ngModel)="('DRAFT')"
                            (onChange)="updateStatus('ZSE', market, $event.value)"
                            styleClass="text-xs w-32 shadow-sm"
                            placeholder="Update"
                          ></p-dropdown>
                        </div>
                      </td>
                    </tr>
                  }
                }

                <!-- VFEX Exchange -->
                @if (
                  productDetails.publishedExchangeMarkets["VFEX"] &&
                  productDetails.publishedExchangeMarkets["VFEX"].length > 0
                  ) {
                  @for (
                    market of productDetails.publishedExchangeMarkets["VFEX"];
                    track market
                    ) {
                    <tr class="hover:bg-gray-50">
                      <td class="py-3 px-4 whitespace-nowrap">
                        <div class="flex items-center gap-2">
                          <span class="w-2 h-2 rounded-full bg-purple-500"></span>
                          <span class="font-medium text-gray-800">VFEX</span>
                        </div>
                      </td>
                      <td class="py-3 px-4 whitespace-nowrap text-gray-700">
                        {{ market }}
                      </td>
                      <td class="py-3 px-4 whitespace-nowrap">
                        <div class="flex items-center gap-1">
                          <span
                            class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800"
                            >
                            <fa-icon [icon]="icons.check" class="mr-1"></fa-icon>
                            Published
                          </span>
                        </div>
                      </td>
                      <td class="py-3 px-4 whitespace-nowrap text-right">
                        <p-dropdown
                          [options]="statusOptions"
                          (ngModel)="('PUBLISHED')"
                          (onChange)="updateStatus('VFEX', market, $event.value)"
                          styleClass="text-xs w-32 shadow-sm"
                          placeholder="Update"
                        ></p-dropdown>
                      </td>
                    </tr>
                  }
                }

                @if (
                  productDetails.pendingExchangeMarkets["VFEX"] &&
                  productDetails.pendingExchangeMarkets["VFEX"].length > 0
                  ) {
                  @for (
                    market of productDetails.pendingExchangeMarkets["VFEX"];
                    track market
                    ) {
                    <tr class="hover:bg-gray-50">
                      <td class="py-3 px-4 whitespace-nowrap">
                        <div class="flex items-center gap-2">
                          <span class="w-2 h-2 rounded-full bg-purple-500"></span>
                          <span class="font-medium text-gray-800">VFEX</span>
                        </div>
                      </td>
                      <td class="py-3 px-4 whitespace-nowrap text-gray-700">
                        {{ market }}
                      </td>
                      <td class="py-3 px-4 whitespace-nowrap">
                        <div class="flex items-center gap-1">
                          <span
                            class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-amber-100 text-amber-800"
                            >
                            <fa-icon
                              [icon]="icons.warning"
                              class="mr-1"
                            ></fa-icon>
                            Pending
                          </span>
                        </div>
                      </td>
                      <td class="py-3 px-4 whitespace-nowrap text-right">
                        <div class="flex gap-2 justify-end items-center">
                          <button
                            class="flex items-center gap-1 px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded-md text-xs transition-colors shadow-sm"
                            (click)="publishProduct('VFEX', market)"
                            >
                            <fa-icon
                              [icon]="icons.check"
                              class="text-xs"
                            ></fa-icon>
                            Publish
                          </button>
                          <p-dropdown
                            [options]="statusOptions"
                            (ngModel)="('DRAFT')"
                          (onChange)="
                            updateStatus('VFEX', market, $event.value)
                          "
                            styleClass="text-xs w-32 shadow-sm"
                            placeholder="Update"
                          ></p-dropdown>
                        </div>
                      </td>
                    </tr>
                  }
                }
              </tbody>
            </table>
          </div>
        </div>
      } @else {
        <div
          class="flex flex-col items-center justify-center py-12 bg-gray-50 rounded-xl border border-dashed border-gray-200"
          >
          <fa-icon
            [icon]="icons.warning"
            class="text-gray-400 text-3xl mb-3"
          ></fa-icon>
          <p class="text-gray-500 mb-1">
            No details available for {{ productName }}
          </p>
          <p class="text-xs text-gray-400">
            Try selecting a different date or product
          </p>
        </div>
      }
    </div>
  </app-card>

  <p-toast position="top-right"></p-toast>
  <p-confirmDialog
    [style]="{ width: '450px' }"
    styleClass="shadow-lg rounded-lg"
  ></p-confirmDialog>
