/* Remove most custom CSS and rely on Tailwind utility classes */

/* For any edge cases where we need custom styling */
:host {
  display: block;
}

/* Responsive table adjustments */
.responsive-table {
  @apply w-full overflow-x-auto;
}

/* PrimeNG specific overrides - these are necessary */
:host ::ng-deep {
  /* Chart customizations */
  .p-chart {
    @apply h-full w-full;
  }

  /* Dialog customizations */
  .p-dialog .p-dialog-content {
    @apply p-0 overflow-hidden;
  }

  /* Dropdown customizations */
  .p-dropdown {
    @apply shadow-sm text-sm;
  }

  .p-dropdown-panel .p-dropdown-items .p-dropdown-item {
    @apply text-sm py-2;
  }

  /* Date picker customizations */
  .p-datepicker {
    @apply shadow-lg rounded-lg overflow-hidden border border-gray-100;
  }

  .p-datepicker table td > span.p-highlight {
    @apply bg-primary text-white;
  }

  /* Toast customizations */
  .p-toast .p-toast-message {
    @apply rounded-lg shadow-lg;
  }

  .p-toast .p-toast-message-success {
    @apply bg-green-50 border-green-500;
  }

  .p-toast .p-toast-message-error {
    @apply bg-red-50 border-red-500;
  }
}

/* Animation for status indicators */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom table cell hover effect */
tbody tr:hover {
  @apply bg-gray-50 transition-colors duration-150;
}

/* Transitions for progress bars */
.transition-width {
  transition: width 0.5s ease;
}
