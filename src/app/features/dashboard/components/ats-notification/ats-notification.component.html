<!-- Full Updated ats-notification.component.html -->

<app-card class="shadow-lg rounded-2xl overflow-hidden border border-gray-100">
  <div class="flex flex-col gap-8">
    <!-- Header Section with Gradient -->
    <div
      class="bg-gradient-to-r from-green-50 to-blue-50 -mx-6 -mt-6 px-6 pt-6 pb-4 border-b border-gray-100"
    >
      <div class="flex justify-between items-center mb-2">
        <div class="flex items-center gap-3">
          <div class="w-1.5 h-8 bg-primary rounded-full"></div>
          <app-text variant="title/semibold" class="text-gray-800"
            >ATS Report Notifications</app-text
          >
        </div>

        <span class="flex items-center gap-2">
          <label for="fromDate">Date</label>

          <p-datepicker
            size="small"
            [(ngModel)]="date"
            dateFormat="dd/mm/yy"
            [maxDate]="maxDate"
            [readonlyInput]="true"
            (ngModelChange)="onDateChange()"
            inputStyleClass="w-36 py-1"
            showIcon="true"
          />
        </span>
      </div>
    </div>

    <!-- Loading Spinner -->
    @if (isLoading()) {
      <div class="flex justify-center items-center w-full py-16">
        <app-spinner></app-spinner>
      </div>
    } @else {
      <!-- Status Summary Cards -->
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Overall Status Card -->
        <div class="lg:col-span-1">
          <div
            class="rounded-xl overflow-hidden border border-gray-100 shadow-sm h-full"
          >
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-100">
              <div class="flex items-center gap-2">
                <app-text variant="small/semibold" class="text-gray-700"
                  >Overall Status</app-text
                >
                <span class="text-xs text-gray-400">Today</span>
              </div>
            </div>

            <div class="p-5 flex flex-col h-[calc(100%-48px)]">
              <div class="flex items-center justify-between mb-5">
                <div>
                  <div class="flex items-center gap-2">
                    <span
                      [ngClass]="
                        canSendEmails() || selectedExemptedProducts.length > 0
                          ? 'bg-green-500'
                          : 'bg-amber-500'
                      "
                      class="w-3 h-3 rounded-full relative"
                    >
                      <span
                        [ngClass]="
                          canSendEmails() || selectedExemptedProducts.length > 0
                            ? 'bg-green-500'
                            : 'bg-amber-500'
                        "
                        class="absolute inset-0 rounded-full animate-ping opacity-75"
                      ></span>
                    </span>
                    <app-text
                      variant="small/semibold"
                      [ngClass]="
                        canSendEmails() || selectedExemptedProducts.length > 0
                          ? 'text-green-600'
                          : 'text-amber-600'
                      "
                    >
                      {{
                        canSendEmails()
                          ? "Ready to Send"
                          : selectedExemptedProducts.length > 0
                            ? "Ready with Exemptions"
                            : "Pending Publication"
                      }}
                    </app-text>
                  </div>
                </div>
                <fa-icon
                  [icon]="
                    canSendEmails() || selectedExemptedProducts.length > 0
                      ? icons.check
                      : icons.clock
                  "
                  [ngClass]="
                    canSendEmails() || selectedExemptedProducts.length > 0
                      ? 'text-green-600'
                      : 'text-amber-600'
                  "
                  class="text-2xl"
                ></fa-icon>
              </div>

              @if (statusStatistics) {
                <div class="mb-auto">
                  <div class="flex items-center justify-between mb-1">
                    <span class="text-sm text-gray-600">Completion</span>
                    <span class="text-sm font-medium">
                      {{
                        statusStatistics.overallStatistics.completionPercentage.toFixed(
                          1
                        )
                      }}%
                    </span>
                  </div>

                  <div
                    class="w-full h-2 bg-gray-100 rounded-full overflow-hidden"
                  >
                    <div
                      [ngClass]="{
                        'bg-green-500':
                          statusStatistics.overallStatistics
                            .completionPercentage >= 75,
                        'bg-amber-500':
                          statusStatistics.overallStatistics
                            .completionPercentage >= 30 &&
                          statusStatistics.overallStatistics
                            .completionPercentage < 75,
                        'bg-red-500':
                          statusStatistics.overallStatistics
                            .completionPercentage < 30,
                      }"
                      [style.width.%]="
                        statusStatistics.overallStatistics.completionPercentage
                      "
                      class="h-full rounded-full transition-all duration-500"
                    ></div>
                  </div>

                  <div class="grid grid-cols-3 gap-2 mt-4 text-xs text-center">
                    <div class="bg-green-50 rounded-lg p-2">
                      <div class="text-green-600 font-medium">
                        {{ statusStatistics.overallStatistics.totalPublished }}
                      </div>
                      <div class="text-gray-500">Published</div>
                    </div>
                    <div class="bg-blue-50 rounded-lg p-2">
                      <div class="text-blue-600 font-medium">
                        {{ statusStatistics.overallStatistics.totalApproved }}
                      </div>
                      <div class="text-gray-500">Approved</div>
                    </div>
                    <div class="bg-amber-50 rounded-lg p-2">
                      <div class="text-amber-600 font-medium">
                        {{ statusStatistics.overallStatistics.totalDraft }}
                      </div>
                      <div class="text-gray-500">Draft</div>
                    </div>
                  </div>
                </div>
              }

              <!-- Exempted Products Info -->
              @if (emailsAlreadySent && emailStatistics?.hadExemptions) {
                <div
                  class="mt-6 mb-3 px-3 py-2 bg-blue-50 rounded-lg border border-blue-100"
                >
                  <div class="flex items-center gap-2 mb-1">
                    <fa-icon
                      [icon]="icons.info"
                      class="text-blue-600 text-lg"
                    ></fa-icon>
                    <p class="text-sm text-blue-700 font-medium">
                      Emails Sent with Exemptions
                    </p>
                  </div>
                  <div class="text-xs text-blue-600 ml-6">
                    <p>The following products were exempted:</p>
                    <ul class="list-disc list-inside mt-1">
                      @for (
                        product of emailStatistics?.exemptedProducts;
                        track product
                      ) {
                        <li>{{ product.toLowerCase().replace("_", " ") }}</li>
                      }
                    </ul>
                  </div>
                </div>
              }

              <!-- Email Already Sent Alert or Send Button -->
              @if (emailsAlreadySent) {
                <div
                  class="mt-2 mb-3 px-3 py-2 bg-blue-50 rounded-lg border border-blue-100 flex items-center gap-2"
                >
                  <fa-icon
                    [icon]="icons.info"
                    class="text-blue-600 text-lg"
                  ></fa-icon>
                  <div>
                    <p class="text-sm text-blue-700 font-medium">
                      Emails Already Sent
                    </p>
                    <!-- <p class="text-xs text-blue-600">
                      Sent on
                      {{ emailStatistics?.date | date: "MMM dd, yyyy, h:mm a" }}
                    </p> -->
                  </div>
                </div>
              } @else {
                <!-- Send Email Button -->
                <button
                  [disabled]="
                    !canSendEmails() && selectedExemptedProducts.length === 0
                  "
                  (click)="sendEmails()"
                  class="flex items-center justify-center gap-2 w-full mt-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-sm"
                  [ngClass]="
                    canSendEmails() || selectedExemptedProducts.length > 0
                      ? 'bg-green-600 hover:bg-green-700 text-white'
                      : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  "
                >
                  <fa-icon [icon]="icons.envelope"></fa-icon>
                  <span>Send Notifications</span>
                </button>

                @if (selectedExemptedProducts.length > 0) {
                  <div class="mt-2 text-center">
                    <span class="text-xs text-amber-600">
                      <fa-icon [icon]="icons.warning" class="mr-1"></fa-icon>
                      Sending with
                      {{ selectedExemptedProducts.length }} exempted products
                    </span>
                  </div>
                }
              }
            </div>
          </div>
        </div>

        <!-- Status Distribution Chart -->
        <div class="lg:col-span-3">
          <div
            class="rounded-xl overflow-hidden border border-gray-100 shadow-sm h-full"
          >
            <div
              class="bg-gray-50 px-4 py-3 border-b border-gray-100 flex justify-between items-center"
            >
              <div class="flex items-center gap-2">
                <app-text variant="small/semibold" class="text-gray-700"
                  >Report Status Distribution</app-text
                >
              </div>
              <div class="flex items-center gap-1 text-xs text-gray-500">
                <span
                  class="inline-block w-2 h-2 rounded-full bg-green-500"
                ></span>
                Published
                <span
                  class="inline-block w-2 h-2 rounded-full bg-blue-500 ml-2"
                ></span>
                Approved
                <span
                  class="inline-block w-2 h-2 rounded-full bg-amber-500 ml-2"
                ></span>
                Draft
              </div>
            </div>

            <div
              class="p-5 flex items-center justify-center h-[calc(100%-48px)]"
            >
              @if (pieChartData) {
                <div class="h-full w-full max-h-[300px]">
                  <p-chart
                    type="pie"
                    [data]="pieChartData"
                    [options]="pieChartOptions"
                  ></p-chart>
                </div>
              } @else {
                <div class="text-gray-400 text-center">
                  <fa-icon
                    [icon]="icons.warning"
                    class="text-2xl mb-2"
                  ></fa-icon>
                  <p>No chart data available</p>
                </div>
              }
            </div>
          </div>
        </div>
      </div>

      <!-- Products Table -->
      <div class="rounded-xl overflow-hidden border border-gray-100 shadow-sm">
        <div
          class="bg-gray-50 px-4 py-3 border-b border-gray-100 flex justify-between items-center"
        >
          <div class="flex items-center gap-2">
            <app-text variant="small/semibold" class="text-gray-700"
              >Product Publication Status</app-text
            >
            <span
              class="text-xs px-2 py-0.5 bg-gray-200 text-gray-600 rounded-full"
              >{{ products.length }}</span
            >
          </div>
          <div class="flex items-center gap-2">
            <span
              class="text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-700"
            >
              <fa-icon [icon]="icons.check" class="text-xs mr-1"></fa-icon>
              {{ publishedProducts.length }} Published
            </span>

            <span
              class="text-xs px-2 py-0.5 rounded-full bg-amber-100 text-amber-700"
            >
              <fa-icon [icon]="icons.warning" class="text-xs mr-1"></fa-icon>
              {{ pendingProducts.length }} Pending
            </span>

            <span
              class="text-xs px-2 py-0.5 rounded-full bg-gray-100 text-gray-700"
            >
              <fa-icon [icon]="icons.info" class="text-xs mr-1"></fa-icon>
              {{ notRequiredProducts.length }} Not Required
            </span>

            @if (selectedExemptedProducts.length > 0) {
              <span
                class="text-xs px-2 py-0.5 rounded-full bg-blue-100 text-blue-700"
              >
                <fa-icon [icon]="icons.info" class="text-xs mr-1"></fa-icon>
                {{ selectedExemptedProducts.length }} Exempted
              </span>
            }
          </div>
        </div>

        <div class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr>
                <th
                  class="py-3 px-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider border-b"
                >
                  Product
                </th>
                <th
                  class="py-3 px-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider border-b"
                >
                  Status
                </th>
                <th
                  class="py-3 px-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider border-b hidden md:table-cell"
                >
                  ZSE
                </th>
                <th
                  class="py-3 px-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider border-b hidden md:table-cell"
                >
                  VFEX
                </th>
                @if (!emailsAlreadySent) {
                  <th
                    class="py-3 px-3 text-center text-xs font-semibold text-gray-500 uppercase tracking-wider border-b w-24"
                  >
                    Exempt
                  </th>
                }
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-100">
              @for (product of products; track product.productName) {
                <tr class="hover:bg-gray-50 transition-colors">
                  <td class="py-1 px-3 whitespace-nowrap">
                    <div class="flex items-center gap-2">
                      <span
                        [ngClass]="
                          product.fullyPublished
                            ? 'bg-green-100 text-green-700'
                            : isProductExempted(product.productName)
                              ? 'bg-blue-100 text-blue-700'
                              : product.specialNote
                                ? 'bg-gray-100 text-gray-700'
                                : checkIfProductHasApprovedStatus(
                                      product.productName
                                    )
                                  ? 'bg-blue-100 text-blue-600'
                                  : 'bg-amber-100 text-amber-700'
                        "
                        class="flex items-center justify-center w-6 h-6 rounded-full"
                      >
                        <fa-icon
                          [icon]="
                            product.fullyPublished
                              ? icons.check
                              : isProductExempted(product.productName)
                                ? icons.info
                                : product.specialNote
                                  ? icons.clock
                                  : checkIfProductHasApprovedStatus(
                                        product.productName
                                      )
                                    ? icons.clock
                                    : icons.warning
                          "
                          class="text-xs"
                        ></fa-icon>
                      </span>
                      <span class="font-medium text-gray-800 capitalize">
                        {{
                          product.productName.toLowerCase().replaceAll("_", " ")
                        }}
                      </span>
                      @if (product.isExempted) {
                        <span
                          class="ml-2 text-xs px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full"
                        >
                          Exempted
                        </span>
                      }
                      @if (product.specialNote) {
                        <span
                          class="ml-2 text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full"
                        >
                          {{ product.specialNote }}
                        </span>
                      }
                    </div>
                  </td>
                  <td class="py-1 px-3 whitespace-nowrap">
                    <span
                      [ngClass]="getProductStatus(product).cssClass"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    >
                      <fa-icon
                        [icon]="getProductStatus(product).icon"
                        class="mr-1 text-xs"
                      ></fa-icon>
                      {{ getProductStatus(product).status }}
                    </span>
                  </td>
                  <td class="py-1 px-3 hidden md:table-cell">
                    <div class="flex flex-col gap-1">
                      @if (
                        product.publishedExchangeMarkets["ZSE"] &&
                        product.publishedExchangeMarkets["ZSE"].length > 0
                      ) {
                        <div class="flex flex-wrap gap-1">
                          @for (
                            market of product.publishedExchangeMarkets["ZSE"];
                            track market
                          ) {
                            <span
                              class="inline-block px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded"
                              >{{ getMarketDisplayName(market) }}</span
                            >
                          }
                        </div>
                      }
                      @if (
                        product.pendingExchangeMarkets["ZSE"] &&
                        product.pendingExchangeMarkets["ZSE"].length > 0
                      ) {
                        <div class="flex flex-wrap gap-1">
                          @for (
                            market of product.pendingExchangeMarkets["ZSE"];
                            track market
                          ) {
                            <span
                              class="inline-block px-2 py-0.5 bg-amber-100 text-amber-800 text-xs rounded"
                              >{{ getMarketDisplayName(market) }}</span
                            >
                          }
                        </div>
                      }
                    </div>
                  </td>
                  <td class="py-1 px-3 hidden md:table-cell">
                    <div class="flex flex-col gap-1">
                      @if (
                        product.publishedExchangeMarkets["VFEX"] &&
                        product.publishedExchangeMarkets["VFEX"].length > 0
                      ) {
                        <div class="flex flex-wrap gap-1">
                          @for (
                            market of product.publishedExchangeMarkets["VFEX"];
                            track market
                          ) {
                            <span
                              class="inline-block px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded"
                              >{{ getMarketDisplayName(market) }}</span
                            >
                          }
                        </div>
                      }
                      @if (
                        product.pendingExchangeMarkets["VFEX"] &&
                        product.pendingExchangeMarkets["VFEX"].length > 0
                      ) {
                        <div class="flex flex-wrap gap-1">
                          @for (
                            market of product.pendingExchangeMarkets["VFEX"];
                            track market
                          ) {
                            <span
                              class="inline-block px-2 py-0.5 bg-amber-100 text-amber-800 text-xs rounded"
                              >{{ getMarketDisplayName(market) }}</span
                            >
                          }
                        </div>
                      }
                    </div>
                  </td>
                  @if (!emailsAlreadySent) {
                    <td class="py-1 px-3 text-center">
                      <div class="flex justify-center">
                        <input
                          type="checkbox"
                          [checked]="isProductExempted(product.productName)"
                          (change)="toggleExemptedProduct(product.productName)"
                          [disabled]="
                            product.fullyPublished || !!product.specialNote
                          "
                          class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
                        />
                      </div>
                    </td>
                  }
                </tr>
              }

              @if (products.length === 0) {
                <tr>
                  <td colspan="5" class="py-8 text-center text-gray-500">
                    <div class="flex flex-col items-center justify-center">
                      <fa-icon
                        [icon]="icons.warning"
                        class="text-3xl text-gray-300 mb-2"
                      ></fa-icon>
                      <p>No products available for the selected date</p>
                      <p class="text-sm text-gray-400 mt-1">
                        Try selecting a different date
                      </p>
                    </div>
                  </td>
                </tr>
              }
            </tbody>
          </table>
        </div>
      </div>

      <!-- Product Statistics -->
      @if (statusStatistics) {
        <div
          class="rounded-xl overflow-hidden border border-gray-100 shadow-sm"
        >
          <div class="bg-gray-50 px-4 py-3 border-b border-gray-100">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <app-text variant="small/semibold" class="text-gray-700"
                  >Product Completion Rates</app-text
                >
                <span class="text-xs text-gray-400"
                  >{{
                    Object.keys(statusStatistics.productStatistics).length
                  }}
                  products</span
                >
              </div>
              <span
                class="text-xs px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full"
                >Completion Status</span
              >
            </div>
          </div>

          <div class="p-5">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              @for (
                product of Object.keys(statusStatistics.productStatistics);
                track product
              ) {
                <div
                  class="p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-shadow"
                  [ngClass]="{
                    'border-blue-200 bg-blue-50': isProductExempted(product),
                  }"
                >
                  <div class="flex items-center justify-between mb-2">
                    <h3
                      class="text-sm font-medium text-gray-800 flex items-center gap-2"
                    >
                      {{ product }}
                      @if (isProductExempted(product)) {
                        <span
                          class="text-xs px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full"
                          >Exempted</span
                        >
                      }
                      @if (!shouldProductPublishOnDate(product)) {
                        <span
                          class="text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full"
                          >Not required today</span
                        >
                      }
                    </h3>
                    <span
                      [ngClass]="{
                        'bg-green-100 text-green-700':
                          statusStatistics.productStatistics[product]
                            .completionPercentage >= 75,
                        'bg-amber-100 text-amber-700':
                          statusStatistics.productStatistics[product]
                            .completionPercentage >= 30 &&
                          statusStatistics.productStatistics[product]
                            .completionPercentage < 75,
                        'bg-red-100 text-red-700':
                          statusStatistics.productStatistics[product]
                            .completionPercentage < 30,
                      }"
                      class="text-xs px-2 py-0.5 rounded-full font-medium"
                    >
                      {{
                        statusStatistics.productStatistics[
                          product
                        ].completionPercentage.toFixed(0)
                      }}%
                    </span>
                  </div>

                  <div class="mb-2">
                    <div
                      class="w-full h-2 bg-gray-100 rounded-full overflow-hidden"
                    >
                      <div
                        [ngClass]="{
                          'bg-green-500':
                            statusStatistics.productStatistics[product]
                              .completionPercentage >= 75,
                          'bg-amber-500':
                            statusStatistics.productStatistics[product]
                              .completionPercentage >= 30 &&
                            statusStatistics.productStatistics[product]
                              .completionPercentage < 75,
                          'bg-red-500':
                            statusStatistics.productStatistics[product]
                              .completionPercentage < 30,
                        }"
                        [style.width.%]="
                          statusStatistics.productStatistics[product]
                            .completionPercentage
                        "
                        class="h-full rounded-full transition-all duration-500"
                      ></div>
                    </div>
                  </div>

                  <div
                    class="flex items-center justify-between text-xs text-gray-500"
                  >
                    <span
                      >{{
                        statusStatistics.productStatistics[product].published
                      }}/{{
                        statusStatistics.productStatistics[product].total
                      }}
                      complete</span
                    >
                    <div class="flex items-center gap-2">
                      <span class="flex items-center gap-1">
                        <span
                          class="inline-block w-2 h-2 rounded-full bg-green-500"
                        ></span>
                        <span>{{
                          statusStatistics.productStatistics[product].published
                        }}</span>
                      </span>
                      <span class="flex items-center gap-1">
                        <span
                          class="inline-block w-2 h-2 rounded-full bg-blue-500"
                        ></span>
                        <span>{{
                          statusStatistics.productStatistics[product].approved
                        }}</span>
                      </span>
                      <span class="flex items-center gap-1">
                        <span
                          class="inline-block w-2 h-2 rounded-full bg-amber-500"
                        ></span>
                        <span>{{
                          statusStatistics.productStatistics[product].draft
                        }}</span>
                      </span>
                    </div>
                  </div>
                </div>
              }
            </div>
          </div>
        </div>
      }
    }
  </div>
</app-card>

<!-- Product Detail Dialog -->
<p-dialog
  [(visible)]="showDetailDialog"
  [style]="{ width: '90vw', maxWidth: '800px' }"
  [modal]="true"
  [dismissableMask]="true"
  [draggable]="false"
  [resizable]="false"
  [showHeader]="false"
  styleClass="rounded-xl overflow-hidden shadow-lg"
>
  <div
    class="p-4 bg-gradient-to-r from-gray-50 to-blue-50 flex justify-between items-center"
  >
    <div class="flex items-center gap-2">
      <div class="w-1 h-6 bg-primary rounded-full"></div>
      <h2 class="text-lg font-semibold text-gray-800">
        {{ selectedProduct }} Details
      </h2>
    </div>
    <button
      class="w-8 h-8 flex items-center justify-center rounded-full bg-white bg-opacity-60 hover:bg-opacity-100 text-gray-500 transition-colors"
      (click)="showDetailDialog = false"
    >
      <fa-icon [icon]="icons.close" class="text-sm"></fa-icon>
    </button>
  </div>

  <div class="p-4">
    <app-ats-notification-detail
      [productName]="selectedProduct"
      [date]="date"
    ></app-ats-notification-detail>
  </div>
</p-dialog>

<!-- Confirmation Dialog for Exemptions -->
<p-toast position="top-right"></p-toast>
<p-confirmDialog
  [style]="{ width: '450px' }"
  styleClass="shadow-lg rounded-lg"
></p-confirmDialog>
