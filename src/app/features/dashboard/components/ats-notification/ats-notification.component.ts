import { CommonModule, DatePipe } from "@angular/common";
import {
  Component,
  EventEmitter,
  OnInit,
  Output,
  inject,
  signal,
} from "@angular/core";
import { FormsModule } from "@angular/forms";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import {
  faCheckCircle,
  faClock,
  faEnvelope,
  faExclamationTriangle,
  faEye,
  faInfoCircle,
  faTimes,
} from "@fortawesome/free-solid-svg-icons";
import { ConfirmationService, MessageService } from "primeng/api";
import { ChartModule } from "primeng/chart";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { DatePicker } from "primeng/datepicker";
import { DialogModule } from "primeng/dialog";
import { ToastModule } from "primeng/toast";
import Swal from "sweetalert2";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { SpinnerComponent } from "../../../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { AtsNotificationDetailComponent } from "./ats-notification-detail.component";
import { AtsNotificationService } from "./ats-notification.service";

interface ProductStatus {
  productName: string;
  fullyPublished: boolean;
  publishedExchangeMarkets: Record<string, string[]>;
  pendingExchangeMarkets: Record<string, string[]>;
  isExempted?: boolean;
  specialNote?: string;
  notRequiredToday?: boolean; // New flag for products not required today
}

interface StatusStatistics {
  overallStatistics: {
    totalProducts: number;
    totalDraft: number;
    totalApproved: number;
    totalPublished: number;
    totalRequired: number;
    completionPercentage: number;
  };
  productStatistics: Record<
    string,
    {
      draft: number;
      approved: number;
      published: number;
      total: number;
      completionPercentage: number;
    }
  >;
}

interface EmailStatistics {
  date: string;
  totalEmailsSent: number;
  emailsStatus: string;
  exemptedProducts: string[];
  hadExemptions: boolean;
}

@Component({
  selector: "app-ats-notification",
  standalone: true,
  imports: [
    FormsModule,
    CardComponent,
    TextComponent,
    SpinnerComponent,
    DatePicker,
    ChartModule,
    FontAwesomeModule,
    DialogModule,
    ToastModule,
    AtsNotificationDetailComponent,
    ConfirmDialogModule,
    CommonModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: "./ats-notification.component.html",
  styleUrls: ["./ats-notification.component.css"],
})
export class AtsNotificationComponent implements OnInit {
  isLoading = signal(true);
  canSendEmails = signal(false);
  date: Date = new Date();
  maxDate: Date = new Date();
  emailsAlreadySent = false;
  emailSentDate: Date | null = null;
  icons = {
    check: faCheckCircle,
    warning: faExclamationTriangle,
    clock: faClock,
    envelope: faEnvelope,
    eye: faEye,
    close: faTimes,
    info: faInfoCircle,
  };

  Object = Object;

  products: ProductStatus[] = [];
  publishedProducts: string[] = [];
  pendingProducts: string[] = [];
  notRequiredProducts: string[] = [];
  statusStatistics: StatusStatistics | null = null;
  emailStatistics: EmailStatistics | null = null;
  selectedExemptedProducts: string[] = []; // Track selected exempted products

  pieChartData: any;
  pieChartOptions: any;

  // Output event to notify parent component of status changes
  @Output() statusUpdated = new EventEmitter<{
    isComplete: boolean;
    pendingCount: number;
  }>();

  // Dialog properties
  showDetailDialog = false;
  selectedProduct: string = "";

  // Inject the service and DatePipe
  private atsService = inject(AtsNotificationService);
  private datePipe = inject(DatePipe);
  private messageService = inject(MessageService);
  private confirmationService = inject(ConfirmationService);

  ngOnInit() {
    this.loadReportStatus();
    this.initPieChart();
  }

  calculateStatusCounts() {
    const published = this.products.filter((p) => p.fullyPublished).length;
    const notRequired = this.products.filter((p) => p.notRequiredToday).length;
    const pending = this.products.filter(
      (p) => !p.fullyPublished && !p.notRequiredToday,
    ).length;

    return { published, notRequired, pending };
  }

  loadReportStatus() {
    this.isLoading.set(true);
    const formattedDate = this.datePipe.transform(this.date, "yyyy-MM-dd");

    if (formattedDate) {
      // Load report status
      this.atsService.getReportStatus(formattedDate).subscribe({
        next: (response) => {
          this.canSendEmails.set(response.data.canSendEmails);
          this.publishedProducts = response.data.publishedProducts || [];
          this.pendingProducts = response.data.pendingProducts || [];

          // Check if emails have already been sent for this date
          this.emailsAlreadySent = response.data.emailsSent || false;

          // Reset exempted products selection
          this.selectedExemptedProducts = [];

          // Load email statistics if emails were sent
          if (this.emailsAlreadySent) {
            this.loadEmailStatistics(formattedDate);
          }

          // Emit status update to parent
          this.statusUpdated.emit({
            isComplete: response.data.canSendEmails,
            pendingCount: this.pendingProducts.length,
          });

          // Get detailed status
          this.atsService.getDetailedStatus(formattedDate).subscribe({
            next: (detailResponse) => {
              // Process product list to account for special publication days
              this.products = (detailResponse.data.products || []).map(
                (product: ProductStatus) => {
                  // Check if product should be considered for today
                  const shouldPublish = this.shouldProductPublishOnDate(
                    product.productName,
                  );

                  // If product doesn't need to publish today, mark with distinct status
                  if (!shouldPublish) {
                    return {
                      ...product,
                      fullyPublished: false, // Not marking as published
                      notRequiredToday: true, // New distinct status
                      pendingExchangeMarkets: {}, // Clear pending markets
                      specialNote: "Not required today",
                    };
                  }

                  return product;
                },
              );

              // Mark exempted products if emails were already sent
              if (
                response.data.hadExemptions &&
                response.data.exemptedProducts
              ) {
                this.markExemptedProducts(response.data.exemptedProducts);
              }

              // Get status statistics
              this.atsService.getStatusStatistics(formattedDate).subscribe({
                next: (statsResponse) => {
                  this.statusStatistics =
                    statsResponse.data as StatusStatistics;

                  // Adjust statistics to account for special publication days
                  this.adjustStatusStatisticsForSpecialDays();

                  this.updatePieChart();
                  this.isLoading.set(false);
                },
                error: (error) => {
                  console.error("Failed to load status statistics:", error);
                  this.isLoading.set(false);
                  this.showErrorToast("Failed to load status statistics");
                },
              });

              const statusCounts = this.calculateStatusCounts();
              this.publishedProducts = this.products
                .filter((p) => p.fullyPublished)
                .map((p) => p.productName);
              this.pendingProducts = this.products
                .filter((p) => !p.fullyPublished && !p.notRequiredToday)
                .map((p) => p.productName);
              this.notRequiredProducts = this.products
                .filter((p) => p.notRequiredToday)
                .map((p) => p.productName);
            },
            error: (error) => {
              console.error("Failed to load detailed status:", error);
              this.isLoading.set(false);
              this.showErrorToast("Failed to load detailed product status");
            },
          });
        },
        error: (error) => {
          console.error("Failed to load report status:", error);
          this.isLoading.set(false);
          this.showErrorToast("Failed to load report status");
        },
      });
    }
  }

  adjustStatusStatisticsForSpecialDays() {
    if (!this.statusStatistics) return;

    const specialProducts = ["WTR"]; // List of products with special publication days
    const dayOfWeek = this.date.getDay();

    // For each special product
    specialProducts.forEach((productName) => {
      // Check if the product exists in statistics
      if (!this.statusStatistics?.productStatistics[productName]) return;

      // Check if product should publish today
      if (!this.shouldProductPublishOnDate(productName)) {
        // Get current product stats
        const productStats =
          this.statusStatistics.productStatistics[productName];
        const totalRequired = productStats.total;

        // Adjust overall statistics
        this.statusStatistics.overallStatistics.totalRequired -= totalRequired;
        this.statusStatistics.overallStatistics.totalDraft -=
          productStats.draft;
        this.statusStatistics.overallStatistics.totalApproved -=
          productStats.approved;

        // Remove this product from statistics (or mark as not required)
        delete this.statusStatistics.productStatistics[productName];

        // Recalculate overall completion percentage
        if (this.statusStatistics.overallStatistics.totalRequired > 0) {
          this.statusStatistics.overallStatistics.completionPercentage =
            (this.statusStatistics.overallStatistics.totalPublished * 100) /
            this.statusStatistics.overallStatistics.totalRequired;
        } else {
          this.statusStatistics.overallStatistics.completionPercentage = 100;
        }
      }
    });
  }

  loadEmailStatistics(formattedDate: string) {
    this.atsService.getEmailStatistics(formattedDate).subscribe({
      next: (response) => {
        this.emailStatistics = response.data as EmailStatistics;
        if (this.emailStatistics.hadExemptions) {
          // Mark products that were exempted when emails were sent
          this.markExemptedProducts(this.emailStatistics.exemptedProducts);
        }
      },
      error: (error) => {
        console.error("Failed to load email statistics:", error);
        this.showErrorToast("Failed to load email statistics");
      },
    });
  }

  initPieChart() {
    const documentStyle = getComputedStyle(document.documentElement);

    this.pieChartOptions = {
      plugins: {
        legend: {
          position: "right",
          labels: {
            color: documentStyle.getPropertyValue("--text-color"),
            font: {
              family: "'Inter', sans-serif",
              size: 12,
            },
            padding: 20,
            usePointStyle: true,
          },
        },
        tooltip: {
          backgroundColor: documentStyle.getPropertyValue("--surface-card"),
          titleColor: documentStyle.getPropertyValue("--text-color"),
        },
      },
      cutout: "60%", // Makes it a donut chart
      responsive: true,
      maintainAspectRatio: false,
    };
  }

  updatePieChart() {
    if (this.statusStatistics) {
      const documentStyle = getComputedStyle(document.documentElement);

      this.pieChartData = {
        labels: ["Published", "Approved", "Draft"],
        datasets: [
          {
            data: [
              this.statusStatistics.overallStatistics.totalPublished,
              this.statusStatistics.overallStatistics.totalApproved,
              this.statusStatistics.overallStatistics.totalDraft,
            ],
            backgroundColor: [
              "rgba(34, 197, 94, 0.8)", // Green for Published
              "rgba(59, 130, 246, 0.8)", // Blue for Approved
              "rgba(245, 158, 11, 0.8)", // Amber for Draft
            ],
            hoverBackgroundColor: [
              "rgba(34, 197, 94, 1)", // Green
              "rgba(59, 130, 246, 1)", // Blue
              "rgba(245, 158, 11, 1)", // Amber
            ],
          },
        ],
      };
    }
  }

  getProductStatus(product: ProductStatus): {
    status: string;
    cssClass: string;
    icon: any;
  } {
    if (product.notRequiredToday) {
      return {
        status: "Not Required Today",
        cssClass: "bg-gray-100 text-gray-700",
        icon: this.icons.info,
      };
    }

    if (product.fullyPublished) {
      return {
        status: "Published",
        cssClass: "bg-green-100 text-green-700",
        icon: this.icons.check,
      };
    }

    if (this.isProductExempted(product.productName)) {
      return {
        status: "Exempted",
        cssClass: "bg-blue-100 text-blue-700",
        icon: this.icons.info,
      };
    }

    // Check if any market has APPROVED status
    const isAnyApproved = this.checkIfProductHasApprovedStatus(
      product.productName,
    );
    if (isAnyApproved) {
      return {
        status: "Approved",
        cssClass: "bg-blue-500 text-white",
        icon: this.icons.clock,
      };
    }

    return {
      status: "Pending",
      cssClass: "bg-amber-100 text-amber-700",
      icon: this.icons.warning,
    };
  }

  checkIfProductHasApprovedStatus(productName: string): boolean {
    if (
      !this.statusStatistics ||
      !this.statusStatistics.productStatistics[productName]
    ) {
      return false;
    }

    return this.statusStatistics.productStatistics[productName].approved > 0;
  }

  shouldProductPublishOnDate(productName: string): boolean {
    // Map of products with special publication days
    const specialPublicationDays: Record<string, Record<string, number>> = {
      WTR: {
        VFEX: 4, // Thursday (0-indexed, so 4 = Thursday)
        ZSE: 5, // Friday
      },
    };

    if (!specialPublicationDays[productName]) {
      return true; // No special rules, should publish any day
    }

    const dayOfWeek = this.date.getDay();
    const exchanges = Object.keys(specialPublicationDays[productName]);

    // Check if any exchange needs to publish today
    for (const exchange of exchanges) {
      if (specialPublicationDays[productName][exchange] === dayOfWeek) {
        return true;
      }
    }

    return false; // Not required to publish today
  }

  getMarketDisplayName(market: string): string {
    if (market === "N/A") {
      return "All"; // Better label for products without specific markets
    }
    return market;
  }

  // Mark products as exempted based on the list from the backend
  markExemptedProducts(exemptedProducts: string[]) {
    if (!exemptedProducts || exemptedProducts.length === 0) return;

    this.products = this.products.map((product) => {
      return {
        ...product,
        isExempted: exemptedProducts.includes(product.productName),
      };
    });
  }

  // Toggle a product as exempted or not
  toggleExemptedProduct(productName: string) {
    if (this.isProductExempted(productName)) {
      this.selectedExemptedProducts = this.selectedExemptedProducts.filter(
        (p) => p !== productName,
      );
    } else {
      this.selectedExemptedProducts.push(productName);
    }
  }

  // Check if a product is currently exempted
  isProductExempted(productName: string): boolean {
    return this.selectedExemptedProducts.includes(productName);
  }

  sendEmails() {
    if (!this.canSendEmails() && this.selectedExemptedProducts.length === 0) {
      return;
    }

    // Confirm the user wants to send emails with exempted products
    if (this.selectedExemptedProducts.length > 0) {
      Swal.fire({
        title: "Send Emails with Exemptions",
        html:
          `You're about to send emails with ${this.selectedExemptedProducts.length} exempted products:<br><br>` +
          `<ul class="text-left">${this.selectedExemptedProducts
            .map((p) => `<li>${p.toLowerCase().replace(/_/g, " ")}</li>`)
            .join("")}</ul><br>` +
          "These products will not be required to be published before sending emails. Continue?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, send emails",
        cancelButtonText: "Cancel",
      }).then((result) => {
        if (result.isConfirmed) {
          this.processSendEmails();
        }
      });
    } else {
      this.processSendEmails();
    }
  }

  // Process sending emails
  // Process sending emails
  private processSendEmails() {
    this.isLoading.set(true);
    const formattedDate = this.datePipe.transform(this.date, "yyyy-MM-dd");

    if (formattedDate) {
      this.atsService
        .sendEmails(formattedDate, this.selectedExemptedProducts)
        .subscribe({
          next: () => {
            this.loadReportStatus();

            Swal.fire({
              title: "Emails Sent",
              text: "Notification emails have been sent successfully",
              icon: "success",
              confirmButtonText: "Ok",
            });
          },
          error: (error) => {
            console.error("Failed to send emails:", error);
            this.isLoading.set(false);

            // Handle the specific error for missing required reports
            if (
              error?.error?.message &&
              error.error.message.includes(
                "Not all required reports are published",
              )
            ) {
              // Extract product names from the error message
              const errorMsg = error.error.message;
              const productListMatch = errorMsg.match(
                /Not all required reports are published: (.*)/,
              );

              if (productListMatch && productListMatch[1]) {
                const pendingProducts = productListMatch[1].split(", ");

                // Format product names for display (convert from UPPER_CASE to Title Case)
                const formattedProducts = pendingProducts.map(
                  (product: string) =>
                    product
                      .toLowerCase()
                      .split("_")
                      .map(
                        (word: string) =>
                          word.charAt(0).toUpperCase() + word.slice(1),
                      )
                      .join(" "),
                );

                // Show error with missing products list
                Swal.fire({
                  title: "Cannot Send Emails",
                  html: `
                <p>Not all required reports are published. The following products are still pending:</p>
                <ul class="mt-3 text-left">
                  ${formattedProducts.map((product: string) => `<li>• ${product}</li>`).join("")}
                </ul>
                <p class="mt-3 text-sm">You can either publish these products or exempt them using the checkboxes in the product table.</p>
              `,
                  icon: "warning",
                  confirmButtonText: "Ok",
                  confirmButtonColor: "#3085d6",
                });
              } else {
                // Fallback error message
                Swal.fire({
                  title: "Cannot Send Emails",
                  text: errorMsg,
                  icon: "error",
                  confirmButtonText: "Ok",
                });
              }
            } else {
              // Generic error handling
              this.showErrorToast("Failed to send notification emails");
            }
          },
        });
    }
  }

  onDateChange() {
    this.loadReportStatus();
  }

  getStatusClass(isPublished: boolean): string {
    return isPublished ? "text-green-600" : "text-amber-600";
  }

  openDetailDialog(productName: string) {
    this.selectedProduct = productName;
    this.showDetailDialog = true;
  }

  showErrorToast(message: string) {
    this.messageService.add({
      severity: "error",
      summary: "Error",
      detail: message,
      life: 5000,
    });
  }
}
