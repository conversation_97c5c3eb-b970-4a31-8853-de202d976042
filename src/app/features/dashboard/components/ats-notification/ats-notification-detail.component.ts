import { CommonModule, DatePipe } from "@angular/common";
import { Component, Input, OnInit, inject } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import {
  faCheck,
  faClock,
  faExclamationTriangle,
  faPen,
  faTimesCircle,
} from "@fortawesome/free-solid-svg-icons";
import { ConfirmationService, MessageService } from "primeng/api";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { DropdownModule } from "primeng/dropdown";
import { ToastModule } from "primeng/toast";
import { CardComponent } from "../../../../shared/ui/card/card.component";
import { SpinnerComponent } from "../../../../shared/ui/spinner/spinner.component";
import { TextComponent } from "../../../../shared/ui/text/text.component";
import { AtsNotificationService } from "./ats-notification.service";

@Component({
  selector: "app-ats-notification-detail",
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CardComponent,
    TextComponent,
    SpinnerComponent,
    FontAwesomeModule,
    ConfirmDialogModule,
    ToastModule,
    DropdownModule,
  ],
  providers: [ConfirmationService, MessageService],
  templateUrl: "./ats-notification-detail.component.html",
  styleUrls: ["./ats-notification-detail.component.css"],
})
export class AtsNotificationDetailComponent implements OnInit {
  @Input() productName: string = "";
  @Input() date: Date = new Date();

  isLoading = false;
  productDetails: any = null;
  statusOptions = [
    { label: "Draft", value: "DRAFT" },
    { label: "Approved", value: "APPROVED" },
    { label: "Published", value: "PUBLISHED" },
  ];

  icons = {
    check: faCheck,
    warning: faExclamationTriangle,
    clock: faClock,
    edit: faPen,
    close: faTimesCircle,
  };

  private atsService = inject(AtsNotificationService);
  private datePipe = inject(DatePipe);
  private confirmationService = inject(ConfirmationService);
  private messageService = inject(MessageService);

  ngOnInit() {
    this.loadProductDetails();
  }

  loadProductDetails() {
    if (!this.productName) return;

    this.isLoading = true;
    const formattedDate = this.datePipe.transform(this.date, "yyyy-MM-dd");

    if (formattedDate) {
      this.atsService.getDetailedStatus(formattedDate).subscribe({
        next: (response) => {
          const products = response.data.products || [];
          let productDetails =
            products.find((p: any) => p.productName === this.productName) ||
            null;

          // Check if this product has special publication day requirements
          if (
            productDetails &&
            !this.shouldProductPublishToday(this.productName)
          ) {
            // Mark product as not required today
            productDetails = {
              ...productDetails,
              isFullyPublished: true,
              specialNote: "Not required today",
              pendingExchangeMarkets: {}, // Clear pending markets
            };
          }

          this.productDetails = productDetails;
          this.isLoading = false;
        },
        error: (error) => {
          console.error("Failed to load product details:", error);
          this.isLoading = false;
        },
      });
    }
  }

  updateStatus(exchange: string, market: string, currentStatus: string) {
    this.confirmationService.confirm({
      message: `Update status for ${this.productName} on ${exchange}${market !== "N/A" ? ", " + market : ""}?`,
      header: "Update Status",
      icon: "pi pi-exclamation-triangle",
      accept: () => {
        const formattedDate = this.datePipe.transform(this.date, "yyyy-MM-dd");

        if (formattedDate) {
          const payload = {
            statsDate: formattedDate,
            exchange: exchange,
            market: market !== "N/A" ? market : null,
            productName: this.productName,
            status: currentStatus,
          };

          this.atsService.updateStatus(payload).subscribe({
            next: () => {
              this.messageService.add({
                severity: "success",
                summary: "Status Updated",
                detail: `Status for ${this.productName} on ${exchange}${market !== "N/A" ? ", " + market : ""} updated to ${currentStatus}`,
              });
              this.loadProductDetails();
            },
            error: (error) => {
              console.error("Failed to update status:", error);
              this.messageService.add({
                severity: "error",
                summary: "Update Failed",
                detail: "Failed to update status. Please try again.",
              });
            },
          });
        }
      },
    });
  }

  publishProduct(exchange: string, market: string) {
    this.confirmationService.confirm({
      message: `Publish ${this.productName} on ${exchange}${market !== "N/A" ? ", " + market : ""}?`,
      header: "Publish Product",
      icon: "pi pi-exclamation-triangle",
      accept: () => {
        const formattedDate = this.datePipe.transform(this.date, "yyyy-MM-dd");

        if (formattedDate) {
          const payload = {
            statsDate: formattedDate,
            exchange: exchange,
            market: market !== "N/A" ? market : null,
            productName: this.productName,
          };

          this.atsService.markAsPublished(payload).subscribe({
            next: () => {
              this.messageService.add({
                severity: "success",
                summary: "Product Published",
                detail: `${this.productName} on ${exchange}${market !== "N/A" ? ", " + market : ""} has been published successfully`,
              });
              this.loadProductDetails();
            },
            error: (error) => {
              console.error("Failed to publish product:", error);
              this.messageService.add({
                severity: "error",
                summary: "Publication Failed",
                detail: "Failed to publish product. Please try again.",
              });
            },
          });
        }
      },
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case "PUBLISHED":
        return "text-green-600";
      case "APPROVED":
        return "text-blue-600";
      case "DRAFT":
      default:
        return "text-amber-600";
    }
  }

  getStatusIcon(status: string) {
    switch (status) {
      case "PUBLISHED":
        return this.icons.check;
      case "APPROVED":
        return this.icons.clock;
      case "DRAFT":
      default:
        return this.icons.warning;
    }
  }

  getMarketDisplayName(market: string): string {
    if (market === "N/A") {
      return "All Markets"; // Better label for products without specific markets
    }
    return market;
  }

  shouldProductPublishToday(productName: string): boolean {
    // Map of products with special publication days
    const specialPublicationDays: Record<string, Record<string, number>> = {
      WTR: {
        VFEX: 4, // Thursday (0-indexed, so 4 = Thursday)
        ZSE: 5, // Friday
      },
    };

    if (!specialPublicationDays[productName]) {
      return true; // No special rules, should publish any day
    }

    const dayOfWeek = this.date.getDay();
    const exchanges = Object.keys(specialPublicationDays[productName]);

    // Check if any exchange needs to publish today
    for (const exchange of exchanges) {
      if (specialPublicationDays[productName][exchange] === dayOfWeek) {
        return true;
      }
    }

    return false; // Not required to publish today
  }

  getStatusDisplay(status: string): {
    text: string;
    cssClass: string;
    icon: any;
  } {
    switch (status) {
      case "PUBLISHED":
        return {
          text: "Published",
          cssClass: "bg-green-100 text-green-800",
          icon: this.icons.check,
        };
      case "APPROVED":
        return {
          text: "Approved",
          cssClass: "bg-blue-100 text-blue-800",
          icon: this.icons.clock,
        };
      case "DRAFT":
      default:
        return {
          text: "Draft",
          cssClass: "bg-amber-100 text-amber-800",
          icon: this.icons.warning,
        };
    }
  }
}
