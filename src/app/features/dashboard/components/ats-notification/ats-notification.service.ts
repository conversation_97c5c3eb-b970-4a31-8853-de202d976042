import { environment } from "@/environments/environment.development";
import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class AtsNotificationService {
  private apiUrl = environment.apiBaseUrl;

  constructor(private http: HttpClient) {}

  /**
   * Check the status of reports for a given date
   * @param date The stats date in YYYY-MM-DD format
   */
  getReportStatus(date: string): Observable<any> {
    return this.http.get(
      `${this.apiUrl}/ats-report-notifications/status?date=${date}`,
    );
  }

  /**
   * Get detailed publication status for all products
   * @param date The stats date in YYYY-MM-DD format
   */
  getDetailedStatus(date: string): Observable<any> {
    return this.http.get(
      `${this.apiUrl}/ats-report-notifications/detailed-status?date=${date}`,
    );
  }

  /**
   * Get status statistics for all products
   * @param date The stats date in YYYY-MM-DD format
   */
  getStatusStatistics(date: string): Observable<any> {
    return this.http.get(
      `${this.apiUrl}/ats-report-notifications/status-statistics?date=${date}`,
    );
  }

  /**
   * Get email statistics for a given date
   * @param date The stats date in YYYY-MM-DD format
   */
  getEmailStatistics(date: string): Observable<any> {
    return this.http.get(
      `${this.apiUrl}/ats-report-notifications/email-statistics?date=${date}`,
    );
  }

  /**
   * Mark a product as published
   */
  markAsPublished(payload: any): Observable<any> {
    return this.http.post(
      `${this.apiUrl}/ats-report-notifications/publish`,
      payload,
    );
  }

  /**
   * Update the status of a product
   */
  updateStatus(payload: any): Observable<any> {
    return this.http.post(
      `${this.apiUrl}/ats-report-notifications/update-status`,
      payload,
    );
  }

  /**
   * Send emails to subscribers
   * @param date The stats date in YYYY-MM-DD format
   * @param exemptedProducts List of products to exempt from publication requirement
   */
  sendEmails(date: string, exemptedProducts: string[] = []): Observable<any> {
    return this.http.post(
      `${this.apiUrl}/ats-report-notifications/send-emails`,
      {
        statsDate: date,
        exemptedProducts: exemptedProducts,
      },
    );
  }
}
