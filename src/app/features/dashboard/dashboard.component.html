<div class="flex flex-col gap-3">
  <!-- Head<PERSON> remains the same -->
  <header class="mb-3">
    <app-text variant="subHeading/semibold">
      Welcome, {{ user?.firstName }} {{ user?.lastName }}
    </app-text>
  </header>

  <!-- Enhanced Reporting Section -->
  <div class="flex flex-col gap-6">
    <div>
      <!-- Subs Stats Grid -->

      @if (isSubscriptionStatsLoading()) {
        <div class="flex justify-between w-full">
          <app-spinner></app-spinner>
        </div>
      } @else {
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 xl:grid-cols-4">
          @for (stat of subscriptionStatsCardValues; track stat.title) {
            <div
              class="flex items-start p-6 rounded-xl"
              [ngClass]="stat.bgColor"
            >
              <div class="flex-1">
                <div class="flex items-center justify-between">
                  <p class="text-base font-semibold" [ngClass]="stat.textColor">
                    {{ stat.title }}
                  </p>

                  <div [class]="['text-2xl', stat.iconColor]">
                    <fa-icon [icon]="stat.icon"></fa-icon>
                  </div>
                </div>

                <div class="flex items-center justify-between w-full">
                  <p [ngClass]="stat.iconColor" class="text-2xl font-semibold">
                    {{ stat.value }}
                  </p>
                </div>

                <button class="flex items-center justify-end w-full gap-2">
                  @if (stat.queryParam) {
                    <a
                      [routerLink]="['/admin/subscriptions']"
                      [queryParams]="{ status: stat.queryParam }"
                    >
                      <app-text
                        variant="small/semibold"
                        [ngClass]="stat.iconColor"
                        >View More</app-text
                      >
                    </a>
                  } @else {
                    <a [routerLink]="['/admin/subscriptions/initiated']">
                      <app-text
                        variant="small/semibold"
                        [ngClass]="stat.iconColor"
                        >View More</app-text
                      >
                    </a>
                  }
                  <fa-icon
                    [icon]="faIcons.arrowRight"
                    [ngClass]="stat.iconColor"
                  ></fa-icon>
                </button>
              </div>
            </div>
          }
        </div>
      }
    </div>

    <div class="grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3 grid">
      <div class="flex flex-col gap-6 xl:col-span-1">
        <app-card>
          <div class="flex flex-col gap-6">
            <div class="flex justify-between gap-3">
              <div>
                <app-text variant="title/semibold">
                  Subscription Trends
                </app-text>
                <!-- <app-text variant="small/normal">
                  Monthly Subscription & Revenue
                </app-text> -->
              </div>

              <div class="w-[86px]">
                <p-datepicker
                  inputStyleClass="border-none shadow-none"
                  size="small"
                  variant="filled"
                  [(ngModel)]="date"
                  view="month"
                  dateFormat="M/yy"
                  [maxDate]="maxDate"
                  [readonlyInput]="true"
                  (ngModelChange)="loadRevenueStats()"
                />
              </div>
            </div>

            @if (isRevenueStatsLoading()) {
              <div class="flex justify-center items-center w-full">
                <app-spinner></app-spinner>
              </div>
            } @else {
              <div class="grid grid-cols-1 gap-6">
                <!-- <app-revenue-stats-card
                  title="Wallet Top-ups"
                  [value]="revenueStats?.walletTopups ?? 0"
                  [color]="'text-sky-900'"
                  [bgColor]="'bg-sky-100'"
                  [icon]="faIcons.walletTopUps"
                /> -->

                <app-revenue-stats-card
                  title="Revenue"
                  subtitle="inc. VAT"
                  [value]="revenueStats?.revenueIncVat ?? 0"
                  [color]="'text-amber-900'"
                  [bgColor]="'bg-amber-100'"
                  [icon]="faIcons.revenueIncVAT"
                />

                <app-revenue-stats-card
                  title="Revenue"
                  subtitle="ex. VAT"
                  [value]="revenueStats?.revenueExcVat ?? 0"
                  [color]="'text-green-900'"
                  [bgColor]="'bg-green-100'"
                  [icon]="faIcons.revenueExVAT"
                />

                <app-revenue-stats-card
                  title="VAT"
                  [value]="revenueStats?.vat ?? 0"
                  [color]="'text-red-900'"
                  [bgColor]="'bg-red-100'"
                  [icon]="faIcons.vat"
                />
              </div>
            }
          </div>
        </app-card>
      </div>

      <div class="xl:col-span-2">
        <dash-revenue-chart></dash-revenue-chart>
      </div>
    </div>

    <div class="hidden">
      <app-text variant="title/semibold" class="mb-6"> Your Audience </app-text>
      <app-text variant="small/normal">
        Demographic properties of your users
      </app-text>
    </div>

    <div class="hidden grid-cols-1 gap-6 sm:grid-cols-2 xl:grid-cols-4">
      @for (chart of clientChartConfigs; track chart.dataKey) {
        @if (clientStats) {
          <dash-clients-chart
            [chartData]="clientStats[chart.dataKey]"
            [title]="chart.dataKey"
            [backgroundColors]="chart.backgroundColors"
            [title]="chart.title"
          />
        }
      }
    </div>

    <div class="hidden grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      <app-vot-tracker
        class="col-span-1 md:col-span-2 lg:col-span-3"
        [votStats]="votStats"
      />
    </div>
  </div>
</div>
