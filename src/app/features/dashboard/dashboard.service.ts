import { ApiResponse } from "@/app/core/interfaces/api-response.interface";
import { environment } from "@/environments/environment.development";
import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import {
  ClientStats,
  DashboardStats,
  RevenueStats,
  SubscriptionStats,
  YearlyRevenueStats,
} from "./dashboard.model";

@Injectable({
  providedIn: "root",
})
export class DashboardService {
  private apiUrl = environment.apiBaseUrl;

  constructor(private http: HttpClient) {}

  // Real API call
  getDashboardStats(): Observable<DashboardStats> {
    return this.http.get<DashboardStats>(`${this.apiUrl}/dashboard/stats`);
  }

  getSubscriptionStats() {
    return this.http.get<ApiResponse<SubscriptionStats>>(
      `${this.apiUrl}/admin/dashboard/subscription-stats`,
    );
  }

  getRevenueStats(date: string) {
    return this.http.get<ApiResponse<RevenueStats>>(
      `${this.apiUrl}/admin/dashboard/revenue-stats?date=${date}`,
    );
  }

  getYearlyRevenueStats(year: number) {
    return this.http.get<ApiResponse<YearlyRevenueStats>>(
      `${this.apiUrl}/admin/dashboard/yearly-revenue-stats?year=${year}`,
    );
  }

  // Mock data for development/testing
  getMockDashboardStats(): Observable<DashboardStats> {
    const mockStats: DashboardStats = {
      subscriptions: {
        total: 1250,
        active: 980,
        expired: 270,
        pending: 50,
      },
      revenue: {
        monthlyLabels: [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "Jul",
          "Aug",
          "Sep",
          "Oct",
          "Nov",
          "Dec",
        ],
        walletTopups: [
          35000, // Jan
          45000, // Feb
          42000, // Mar
          48000, // Apr
          47000, // May
          52000, // Jun
          55000, // Jul (Summer increase)
          53000, // Aug
          51000, // Sep (Post-summer dip)
          55000, // Oct (Fall increase)
          58000, // Nov (Pre-holiday increase)
          60000, // Dec (Holiday season peak)
        ],
        subscriptionRevenue: [
          50000, // Jan
          67000, // Feb
          62000, // Mar
          70000, // Apr
          65000, // May
          78000, // Jun
          82000, // Jul (Summer increase)
          80000, // Aug
          77000, // Sep (Post-summer dip)
          80000, // Oct (Fall increase)
          85000, // Nov (Pre-holiday increase)
          90000, // Dec (Holiday season peak)
        ],
        totalWalletTopups: 650000, // Adjusted total (sum of all topups)
        totalSubscriptionRevenue: 850000, // Adjusted total (sum of all revenues)
      },
      ats: {
        published: 65,
        approved: 25,
        draft: 10,
      },
    };

    return of(mockStats);
  }

  getMockClientStats(): Observable<ClientStats> {
    const totalClients = 1250;

    const mockStats: ClientStats = {
      newVsReturningClients: {
        New: 750,
        Returning: 500,
      },
      gender: {
        Male: 700,
        Female: 550,
      },
      age: {
        "Under 30": 800,
        "Over 30": 450,
      },
      type: {
        Corporate: 600,
        Retail: 650,
      },
    };

    return of(mockStats);
  }
}
