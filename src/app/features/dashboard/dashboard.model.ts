import { ProductPackage } from "@/app/core/interfaces/common.interface";
import { IconDefinition } from "@fortawesome/free-solid-svg-icons";

export interface Currency {
  id: string;
  name: string;
  code: string;
}

export interface GroupedPackages {
  [key: string]: ProductPackage[];
}

export interface PackageResponse {
  data: ProductPackage[];
}

export interface StatCard {
  title: string;
  value: string;
  icon: IconDefinition;
  bgColor: string;
  textColor: string;
  iconColor: string;
  href?: string;
}

export interface ClientDashboard {
  walletBalance: number;
  activeSubscriptionsCount: number;
  allSubscriptionsCount: number;
  expiredSubscriptionsCount: number;
  recentSubscriptionsList: ProductPackage[];
}

export interface StatsCard {
  title: string;
  value: string;
  icon: IconDefinition;
  bgColor: string;
  textColor: string;
  iconColor: string;
}

export interface DashboardStats {
  subscriptions: {
    total: number;
    active: number;
    expired: number;
    pending: number;
  };
  revenue: {
    monthlyLabels: string[];
    walletTopups: number[];
    subscriptionRevenue: number[];
    totalWalletTopups: number;
    totalSubscriptionRevenue: number;
  };
  ats: {
    published: number;
    approved: number;
    draft: number;
  };
}

export interface SubscriptionStats {
  expiringSubscriptions: number;
  expiredSubscriptions: number;
  activeSubscriptions: number;
  pendingSubscriptions: number;
}

export interface RevenueStats {
  walletTopups: number;
  revenueIncVat: number;
  revenueExcVat: number;
  vat: number;
}

export interface YearlyRevenueStats {
  monthlyLabels: string[];
  subscriptions: number[];
  revenue: number[];
}

export interface ClientStats {
  newVsReturningClients: {
    [key: string]: number;
  };
  gender: {
    [key: string]: number;
  };
  age: {
    [key: string]: number;
  };
  type: {
    [key: string]: number;
  };
}

export interface ClientChartConfig {
  dataKey: keyof ClientStats;
  title: string;
  backgroundColors: string[];
}
