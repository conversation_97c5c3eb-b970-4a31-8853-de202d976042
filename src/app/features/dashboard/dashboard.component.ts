import { CommonModule, DatePipe } from "@angular/common";
import { Component, ElementRef, inject, OnInit, signal } from "@angular/core";
import { Router, RouterModule } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { ChartConfiguration, ChartType } from "chart.js";

import { AdminUser } from "@/app/core/models/userInfo.model";

import { TextComponent } from "../../shared/ui/text/text.component";
import { VotTrackerComponent } from "./components/vot-tracker/vot-tracker.component";

import { FormsModule } from "@angular/forms";
import { DatePicker } from "primeng/datepicker";
import { CardComponent } from "../../shared/ui/card/card.component";
import { SpinnerComponent } from "../../shared/ui/spinner/spinner.component";
import { AuthService } from "../auth/core/services/auth.service";
import { ClientsChartComponent } from "./components/clients-chart/clients-chart.component";
import { RevenueChartComponent } from "./components/revenue-chart/revenue-chart.component";
import { RevenueStatsCardComponent } from "./components/revenue-stats-card/revenue-stats-card.component";
import { faIcons } from "./dashboard.constants";
import {
  ClientChartConfig,
  ClientStats,
  DashboardStats,
  RevenueStats,
  SubscriptionStats,
  YearlyRevenueStats,
} from "./dashboard.model";
import { DashboardService } from "./dashboard.service";

@Component({
  selector: "app-dashboard",
  standalone: true,
  imports: [
    VotTrackerComponent,
    TextComponent,
    RouterModule,
    FontAwesomeModule,
    CardComponent,
    RevenueStatsCardComponent,
    RevenueChartComponent,
    ClientsChartComponent,
    FormsModule,
    DatePicker,
    SpinnerComponent,
    CommonModule,
  ],
  templateUrl: "./dashboard.component.html",
  styleUrls: ["./dashboard.component.scss"],
  providers: [DatePipe],
})
export class DashboardComponent implements OnInit {
  private datePipe = inject(DatePipe);
  // User and UI state
  user: AdminUser | null = null;
  faIcons = faIcons;

  // Dashboard state signals
  activeView = signal<"overview" | "detailed">("overview");
  isLoading = signal(true);
  isSubscriptionStatsLoading = signal(true);
  isRevenueStatsLoading = signal(true);
  isAtsNotificationsComplete = signal(false);
  pendingAtsReportsCount = signal(0);

  router = inject(Router);

  // Reference to the ATS Notification section
  atsNotificationSection = inject(ElementRef);

  // Dashboard statistics
  dashboardStats: DashboardStats | null = null;
  subscriptionStats: SubscriptionStats | null = null;
  revenueStats: RevenueStats | null = null;
  yearlyRevenueStats: YearlyRevenueStats | null = null;
  clientStats: ClientStats | null = null;

  documentStyle = getComputedStyle(document.documentElement);

  date: Date = new Date();
  maxDate: Date | undefined;

  get subscriptionStatsCardValues() {
    return [
      {
        title: "Active Subscriptions",
        value: this.subscriptionStats?.activeSubscriptions || 0,
        icon: faIcons.activeSubscriptions,
        bgColor: "bg-gradient-to-r from-green-100 to-green-200",
        textColor: "text-green-900",
        iconColor: "text-green-500",
        queryParam: "ACTIVE",
      },
      {
        title: "Expired Subscriptions",
        value: this.subscriptionStats?.expiredSubscriptions || 0,
        icon: faIcons.expiredSubscriptions,
        bgColor: "bg-gradient-to-r from-red-100 to-red-200",
        textColor: "text-red-900",
        iconColor: "text-red-500",
        queryParam: "EXPIRED",
      },
      {
        title: "Expiring Subscriptions",
        value: this.subscriptionStats?.expiringSubscriptions || 0,
        icon: faIcons.expiredSubscriptions,
        bgColor: "bg-gradient-to-r from-purple-100 to-purple-200",
        textColor: "text-purple-900",
        iconColor: "text-purple-500",
        queryParam: "EXPIRING",
      },
      {
        title: "Initiated Transactions",
        value: this.subscriptionStats?.pendingSubscriptions || 0,
        icon: faIcons.allSubscriptions,
        bgColor: "bg-gradient-to-r from-amber-100 to-amber-200",
        textColor: "text-amber-900",
        iconColor: "text-amber-500",
      },
    ];
  }

  navigateToSubscriptions(status: string) {
    this.router.navigate(["/admin/subscriptions"], {
      queryParams: { status: status },
    });
  }

  clientChartConfigs: ClientChartConfig[] = [
    {
      dataKey: "newVsReturningClients",
      title: "New vs. Returning",
      backgroundColors: [
        this.documentStyle.getPropertyValue("--p-sky-600"),
        this.documentStyle.getPropertyValue("--p-sky-400"),
      ],
    },
    {
      dataKey: "gender",
      title: "Gender",
      backgroundColors: [
        this.documentStyle.getPropertyValue("--p-teal-600"),
        this.documentStyle.getPropertyValue("--p-teal-400"),
      ],
    },
    {
      dataKey: "age",
      title: "Age",
      backgroundColors: [
        this.documentStyle.getPropertyValue("--p-amber-600"),
        this.documentStyle.getPropertyValue("--p-amber-400"),
      ],
    },
    {
      dataKey: "type",
      title: "Retail vs. Corporate",
      backgroundColors: [
        this.documentStyle.getPropertyValue("--p-purple-600"),
        this.documentStyle.getPropertyValue("--p-purple-400"),
      ],
    },
  ];

  // Chart configurations
  subscriptionPieChart!: ChartConfiguration;
  revenueLineChart!: ChartConfiguration;
  subscriptionRevenueChart!: ChartConfiguration;
  clientBarChart!: ChartConfiguration;
  socialTrafficBarChart!: ChartConfiguration;

  constructor(
    private authService: AuthService,
    private dashboardService: DashboardService,
  ) {
    this.user = this.authService.currentUser;
  }

  ngOnInit() {
    this.maxDate = new Date();
    this.loadDashboardData();
    this.loadSubscriptionStats();
    this.loadRevenueStats();
  }

  trackByClientType(index: number, clientType: any): string {
    return clientType.key; // Assuming you want to track by the key of the key-value pair
  }

  // Method to scroll to ATS notifications section
  scrollToAtsNotifications() {
    const atsSection = document.getElementById("ats-notifications-section");
    if (atsSection) {
      atsSection.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }

  // Method to update ATS notification summary stats
  updateAtsNotificationStatus(isComplete: boolean, pendingCount: number) {
    this.isAtsNotificationsComplete.set(isComplete);
    this.pendingAtsReportsCount.set(pendingCount);
  }

  private loadSubscriptionStats() {
    this.isSubscriptionStatsLoading.set(true);
    this.dashboardService.getSubscriptionStats().subscribe({
      next: (stats) => {
        this.subscriptionStats = stats.data;
        this.isSubscriptionStatsLoading.set(false);
      },
      error: (error) => {
        this.isSubscriptionStatsLoading.set(false);
        console.log(error);
      },
    });
  }

  protected loadRevenueStats() {
    this.isRevenueStatsLoading.set(true);
    this.dashboardService
      .getRevenueStats(this.datePipe.transform(this.date, "yyyy-MM-dd") ?? "")
      .subscribe({
        next: (stats) => {
          this.revenueStats = stats.data;
          this.isRevenueStatsLoading.set(false);
        },
        error: (error) => {
          this.isRevenueStatsLoading.set(false);
          console.log(error);
        },
      });
  }

  // Fetch dashboard data
  private loadDashboardData() {
    this.isLoading.set(true);
    this.dashboardService.getMockDashboardStats().subscribe({
      next: (stats: DashboardStats) => {
        this.dashboardStats = stats;
        this.initializeCharts(stats);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error("Failed to load dashboard stats", error);
        this.isLoading.set(false);
      },
    });

    this.dashboardService.getMockClientStats().subscribe({
      next: (stats: ClientStats) => {
        this.clientStats = stats;
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error("Failed to load client stats", error);
        this.isLoading.set(false);
      },
    });
  }

  // Initialize chart configurations
  private initializeCharts(stats: DashboardStats) {
    // Subscription Pie Chart
    this.subscriptionPieChart = {
      type: "pie" as ChartType,
      data: {
        labels: ["Active", "Expired", "Pending"],
        datasets: [
          {
            data: [
              stats.subscriptions.active,
              stats.subscriptions.expired,
              stats.subscriptions.pending,
            ],
            backgroundColor: [
              "rgba(75, 192, 192, 0.6)", // Green for Active
              "rgba(255, 99, 132, 0.6)", // Red for Expired
              "rgba(54, 162, 235, 0.6)", // Blue for Pending
            ],
          },
        ],
      },
      options: {
        responsive: true,
        plugins: {
          title: { display: true, text: "Subscription Distribution" },
        },
      },
    };
  }

  // View switching method
  switchView(view: "overview" | "detailed") {
    this.activeView.set(view);
  }

  get revenueStatsCardValues() {
    return {
      items: [
        {
          label: "Wallet Top-ups",
          value: this.dashboardStats?.revenue.totalWalletTopups || 0,
          color: "text-info",
          prefix: "$",
        },
        {
          label: "Income",
          value: this.dashboardStats?.revenue.totalSubscriptionRevenue || 0,
          color: "text-primary",
          prefix: "$",
        },
      ],
    };
  }

  get votStats() {
    return {
      items: [
        {
          label: "Published",
          value: this.dashboardStats?.ats.published || 0,
          color: "bg-success",
        },
        {
          label: "Approved",
          value: this.dashboardStats?.ats.approved || 0,
          color: "bg-primary",
        },
        {
          label: "Draft",
          value: this.dashboardStats?.ats.draft || 0,
          color: "bg-warning",
        },
      ],
    };
  }

  getClientChartData(key: keyof ClientStats) {
    return this.clientStats?.[key] ?? {};
  }
}
