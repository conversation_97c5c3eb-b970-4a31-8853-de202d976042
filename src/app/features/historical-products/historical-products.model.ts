import { Exchange } from "@/app/core/enums/exchange.enum";
import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { Status } from "@/app/core/enums/status.enum";
import { BaseResponse } from "@/app/core/interfaces/common.interface";

export interface HistoricalProduct extends BaseResponse {
  name: string;
  description: string;
  linkTable: string;
  exchangeId: number;
  exchangeName: Exchange;
  price: number;
  currencyId: string;
  currencyCode: string;
  categoryType: ProductCategoryType.HISTORICAL;
}

export interface CreateHistoricalProductRequest {
  name: string;
  description: string;
  linkTable: string;
  exchangeName: Exchange;
  price: number;
  currencyId: string;
  categoryType: ProductCategoryType.HISTORICAL;
}

export interface UpdateHistoricalProductRequest {
  id: string;
  name?: string;
  description?: string;
  linkTable?: string;
  categoryType?: ProductCategoryType.HISTORICAL;
  exchangeName?: Exchange;
  status?: Status;
  price?: number;
  currencyId?: string;
}
