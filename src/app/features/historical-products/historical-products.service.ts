import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { Status } from "@/app/core/enums/status.enum";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable, map } from "rxjs";
import { environment } from "src/environments/environment.development";
import {
  CreateHistoricalProductRequest,
  UpdateHistoricalProductRequest,
} from "./historical-products.model";

@Injectable({
  providedIn: "root",
})
export class HistoricalProductsService {
  private apiServerUrl = environment.apiBaseUrl;

  constructor(private http: HttpClient) {}

  public getHistoricalProducts(status?: Status): Observable<any> {
    // Create HttpParams object
    let params = new HttpParams().set(
      "categoryType",
      ProductCategoryType.HISTORICAL,
    );

    // Only add status if defined
    if (status !== undefined) {
      params = params.set("status", status);
    }

    // Pass params to the request
    return this.http
      .get<any>(`${this.apiServerUrl}/addons`, { params })
      .pipe(map((response) => response));
  }

  public getActiveHistoricalProducts(): Observable<any> {
    // Create HttpParams object with both categoryType and status parameters
    const params = new HttpParams()
      .set("categoryType", ProductCategoryType.HISTORICAL)
      .set("status", Status.ACTIVE);

    // Pass params to the request
    return this.http
      .get<any>(`${this.apiServerUrl}/addons`, { params })
      .pipe(map((response) => response));
  }

  public getProductTables(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/products/tables`);
  }

  public createHistoricalProduct(
    productData: CreateHistoricalProductRequest,
  ): Observable<any> {
    // Ensure categoryType is always HISTORICAL
    const dataWithCategory = {
      ...productData,
      categoryType: ProductCategoryType.HISTORICAL,
    };
    return this.http.post<any>(`${this.apiServerUrl}/addons`, dataWithCategory);
  }

  public updateHistoricalProduct(
    productData: UpdateHistoricalProductRequest,
  ): Observable<any> {
    // Ensure categoryType is always HISTORICAL if it's being updated
    const dataWithCategory = {
      ...productData,
      categoryType: ProductCategoryType.HISTORICAL,
    };
    return this.http.put<any>(`${this.apiServerUrl}/addons`, dataWithCategory);
  }
}
