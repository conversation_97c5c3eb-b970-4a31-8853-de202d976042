import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Exchange } from "@/app/core/enums/exchange.enum";
import { ProductCategoryType } from "@/app/core/enums/product-package-type.enum";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { Option } from "@/app/core/utils/map-to-options.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { CurrencyPipe, DatePipe } from "@angular/common";
import { Component, inject, OnInit, signal } from "@angular/core";
import {
  Form<PERSON><PERSON>er,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import { CurrenciesService } from "../currency/currencies.service";
import { Currency } from "../currency/currency.model";
import {
  CreateHistoricalProductRequest,
  HistoricalProduct,
  UpdateHistoricalProductRequest,
} from "./historical-products.model";
import { HistoricalProductsService } from "./historical-products.service";

type ModalKey = "addItem" | "editItem" | "viewItem";

@Component({
  selector: "app-historical-products",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ModalComponent,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    FormFieldComponent,
    ButtonPermissionDirective,
    DatePipe,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    CurrencyPipe,
  ],
  templateUrl: "./historical-products.component.html",
  providers: [DatePipe],
})
export class HistoricalProductsComponent implements OnInit {
  private historicalProductsService = inject(HistoricalProductsService);
  private currenciesService = inject(CurrenciesService);
  private fb = inject(FormBuilder);
  private readonly datePipe = inject(DatePipe);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;

  filteredHistoricalProducts = signal<HistoricalProduct[]>([]);
  historicalProducts = signal<HistoricalProduct[]>([]);
  productTables: any[] = [];
  selectedHistoricalProduct: HistoricalProduct | null = null;
  statusFilter: string | null = null;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  currencies: Currency[] = [];

  get exportData(): any[] {
    return this.filteredHistoricalProducts().map((product) => ({
      "Product Name": product.name,
      Description: product.description || "",
      "Link Table": product.linkTable?.replaceAll("_", " ") || "",
      "Category Type": product.categoryType,
      Exchange: product.exchangeName,
      Price: product.price,
      Currency: product.currencyCode,
      Status: product.status,
      "Created Date": this.datePipe.transform(
        product.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": product.modifiedDate
        ? this.datePipe.transform(product.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": product.createdUserName ?? "-",
      "Modified By": product.modifiedUserName ?? "-",
    }));
  }

  get productTableOptions(): Option[] {
    return this.productTables.map((table) => ({
      value: table,
      label: table,
    }));
  }

  get currencyOptions(): Option[] {
    return this.currencies.map((currency) => ({
      value: currency.id,
      label: currency.name,
    }));
  }

  get exchangeOptions(): Option[] {
    return [
      { value: Exchange.VFEX, label: Exchange.VFEX },
      { value: Exchange.ZSE, label: Exchange.ZSE },
    ];
  }

  addHistoricalProductForm = this.fb.nonNullable.group({
    name: [
      "",
      [Validators.required, Validators.minLength(2), Validators.maxLength(50)],
    ],
    description: ["", [Validators.minLength(2), Validators.maxLength(100)]],
    linkTable: ["", [Validators.required, Validators.minLength(2)]],
    exchangeName: [
      Exchange.VFEX,
      [Validators.required, Validators.minLength(2), Validators.maxLength(50)],
    ],
    price: [0, [Validators.required, Validators.min(0)]],
    currencyId: ["", [Validators.required]],
    // Always set categoryType to HISTORICAL
    categoryType: [ProductCategoryType.HISTORICAL],
  }) as FormGroup<FormControlType<CreateHistoricalProductRequest>>;

  editHistoricalProductForm = this.fb.nonNullable.group({
    id: [""],
    name: ["", [Validators.minLength(2), Validators.maxLength(50)]],
    description: ["", [Validators.minLength(2), Validators.maxLength(100)]],
    linkTable: ["", [Validators.minLength(2)]],
    exchangeName: [
      Exchange.VFEX,
      [Validators.minLength(2), Validators.maxLength(50)],
    ],
    status: [""],
    currencyId: [""],
    price: [0, [Validators.min(0)]],
    // Always keep the categoryType as HISTORICAL
    categoryType: [ProductCategoryType.HISTORICAL],
  }) as FormGroup<FormControlType<UpdateHistoricalProductRequest>>;

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  isDataLoading = false;
  isHistoricalProductTablesLoading = false;
  isCurrenciesLoading = false;

  ngOnInit(): void {
    // Read status parameter from URL query params
    this.route.queryParams.subscribe((params) => {
      this.statusFilter = params["status"] || null;
      this.loadData();
    });
    this.getProductTables();
    this.getCurrencies();
  }

  loadData(): void {
    this.isDataLoading = true;
    this.historicalProductsService.getHistoricalProducts().subscribe({
      next: (response) => {
        this.historicalProducts.set(response.data);

        // Apply filters after data is loaded
        this.applyFilters(this.searchTerm, this.statusFilter);

        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load historical products");
        this.isDataLoading = false;
      },
    });
  }

  toggleRow(product: HistoricalProduct): void {
    if (this.expandedRows[product.id]) {
      delete this.expandedRows[product.id];
    } else {
      this.expandedRows[product.id] = true;
    }
    // Trigger change detection by creating a new object
    this.expandedRows = { ...this.expandedRows };
  }

  onSearch(term: string): void {
    this.applyFilters(term, this.statusFilter);
  }

  applyFilters(
    searchTerm: string | null = null,
    statusFilter: string | null = null,
  ): void {
    let filtered = this.historicalProducts();

    // Apply status filter if provided and not null
    if (statusFilter && statusFilter !== "null") {
      filtered = filtered.filter(
        (product) =>
          product?.status?.toUpperCase() === statusFilter.toUpperCase(),
      );
    }

    // Apply search term filter if provided
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(term) ||
          (product.description &&
            product.description.toLowerCase().includes(term)) ||
          (product.linkTable &&
            product.linkTable.toLowerCase().includes(term)) ||
          product.exchangeName.toLowerCase().includes(term) ||
          product.currencyCode.toLowerCase().includes(term) ||
          product.price.toString().includes(term),
      );
    }

    this.filteredHistoricalProducts.set(filtered);
  }

  // Method to update URL when filter is changed programmatically
  updateStatusFilter(status: string | null): void {
    // If status is null, 'null' as string, or empty, remove it from query params
    const queryParams =
      status && status !== "null" && status !== "" ? { status } : {};

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      // Use 'merge' to keep other query params, but if we're clearing status
      // we should replace all query params
      queryParamsHandling: Object.keys(queryParams).length ? "merge" : "",
    });

    // Update the filter immediately to avoid waiting for route change
    this.statusFilter =
      status && status !== "null" && status !== "" ? status : null;
    this.applyFilters(this.searchTerm, this.statusFilter);
  }

  toggleModal(mode: ModalKey, product?: HistoricalProduct): void {
    this.selectedHistoricalProduct = product || null;

    if (mode === "editItem" && product) {
      this.editHistoricalProductForm.patchValue({
        id: product.id,
        name: product.name,
        description: product.description,
        linkTable: product.linkTable,
        exchangeName: product.exchangeName,
        currencyId: product.currencyId,
        price: product.price,
        status: product.status,
        categoryType: ProductCategoryType.HISTORICAL, // Always HISTORICAL
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  handleHistoricalProductSubmit(mode: "add" | "update"): void {
    const form =
      mode === "add"
        ? this.addHistoricalProductForm
        : this.editHistoricalProductForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    // Ensure categoryType is always HISTORICAL
    if (mode === "add") {
      form.patchValue({ categoryType: ProductCategoryType.HISTORICAL });
    } else {
      form.patchValue({ categoryType: ProductCategoryType.HISTORICAL });
    }

    const submitAction =
      mode === "add"
        ? this.historicalProductsService.createHistoricalProduct(
            form.value as CreateHistoricalProductRequest,
          )
        : this.historicalProductsService.updateHistoricalProduct(
            form.value as UpdateHistoricalProductRequest,
          );

    submitAction.subscribe({
      next: () => {
        showToast({
          message:
            mode === "add"
              ? `Historical product has been added successfully`
              : `Historical product has been updated successfully`,
          type: "success",
        });
        this.loadData();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
        if (mode === "add") {
          // Reset with default categoryType
          form.patchValue({ categoryType: ProductCategoryType.HISTORICAL });
        }
      },
      error: (error) => {
        handleError(
          error,
          `Failed to ${mode} historical product: ${error.message}`,
        );
      },
    });
  }

  getProductTables() {
    this.isHistoricalProductTablesLoading = true;
    this.historicalProductsService.getProductTables().subscribe({
      next: (response) => {
        this.productTables = response.data;
        this.isHistoricalProductTablesLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load product tables");
        this.isHistoricalProductTablesLoading = false;
      },
    });
  }

  getCurrencies() {
    this.isCurrenciesLoading = true;
    this.currenciesService.getCurrencies().subscribe({
      next: (response) => {
        this.currencies = response.data;
        this.isCurrenciesLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load currencies");
        this.isCurrenciesLoading = false;
      },
    });
  }
}
