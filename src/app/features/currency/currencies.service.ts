import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment.development";
import { CreateCurrencyRequest, UpdateCurrencyRequest } from "./currency.model";

@Injectable({
  providedIn: "root",
})
export class CurrenciesService {
  private apiServerUrl = environment.apiBaseUrl;
  constructor(private http: HttpClient) {}

  public getCurrencies(): Observable<any> {
    return this.http.get<any>(`${this.apiServerUrl}/currencies`);
  }

  public createCurrency(currencyData: CreateCurrencyRequest): Observable<any> {
    return this.http.post<any>(`${this.apiServerUrl}/currencies`, currencyData);
  }

  public updateCurrency(currencyData: UpdateCurrencyRequest): Observable<any> {
    return this.http.put<any>(`${this.apiServerUrl}/currencies`, currencyData);
  }
}
