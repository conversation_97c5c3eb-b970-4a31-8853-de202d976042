<app-card>
  <p-toolbar styleClass="rounded-xl">
    <ng-template #start>
      <ng-container *appButtonPermission="'create'">
        <app-button size="sm" class="!gap-2" (click)="toggleModal('addItem')">
          <i class="text-sm pi pi-plus"></i>
          New Currency
        </app-button>
      </ng-container>
    </ng-template>

    <ng-template #end>
      <app-button
        size="sm"
        class="!gap-2"
        variant="secondary"
        (click)="
          reportExportService.exportToExcel(exportData, 'currencies.xlsx')
        "
        >
        <i class="text-sm pi pi-upload"></i>
        Export
      </app-button>
    </ng-template>
  </p-toolbar>

  <!-- Loading State -->
  @if (isDataLoading) {
    <div class="flex items-center justify-center p-8">
      <div class="flex flex-col items-center gap-4">
        <i class="text-4xl pi pi-spin pi-spinner text-primary"></i>
        <span class="text-gray-600">Loading currencies...</span>
      </div>
    </div>
  } @else {
    <!-- Currency Table -->
    <p-table
      [value]="filteredCurrencies()"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      dataKey="id"
      [expandedRowKeys]="expandedRows"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [showCurrentPageReport]="true"
      styleClass="p-datatable-elegant"
      >
      <ng-template #caption>
        <div class="flex flex-wrap items-center justify-between gap-2">
          <app-text variant="title/semibold">Manage Currencies</app-text>
          <p-iconfield>
            <p-inputicon styleClass="pi pi-search" />
            <input
              pSize="small"
              pInputText
              type="text"
              [(ngModel)]="searchTerm"
              (ngModelChange)="onSearch($event)"
              placeholder="Search currencies..."
              />
          </p-iconfield>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-gray-50 text-nowrap">
          <th style="width: 4rem"></th>
          <th pSortableColumn="createdDate">
            <div class="flex items-center gap-2">
              Created Date
              <p-sortIcon field="createdDate" />
            </div>
          </th>
          <th pSortableColumn="name">
            <div class="flex items-center justify-between gap-2">
              Name
              <p-sortIcon field="name" />
              <p-columnFilter
                type="text"
                field="name"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>
          <th pSortableColumn="code">
            <div class="flex items-center justify-between gap-2">
              Code
              <p-sortIcon field="code" />
              <p-columnFilter
                type="text"
                field="code"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>
          <th pSortableColumn="status">
            <div class="flex items-center justify-between gap-2">
              Status
              <p-sortIcon field="status" />
              <p-columnFilter
                type="text"
                field="status"
                display="menu"
                class="ml-auto"
                />
            </div>
          </th>
          <th>Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-currency let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [icon]="
                expanded
                  ? 'pi pi-chevron-down text-xs'
                  : 'pi pi-chevron-right text-xs'
              "
              (click)="toggleRow(currency)"
              class="p-button-text p-button-rounded p-button-plain"
              [class.expanded]="expanded"
            ></button>
          </td>
          <td>{{ currency.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ currency.name }}</td>
          <td>{{ currency.code }}</td>
          <td>{{ currency.status }}</td>
          <td>
            <div class="flex gap-2">
              <ng-container *appButtonPermission="'view'">
                <app-button
                  size="icon"
                  (click)="toggleModal('viewItem', currency)"
                  >
                  <fa-icon [icon]="eyeIcon"></fa-icon>
                </app-button>
              </ng-container>
              <ng-container *appButtonPermission="'edit'">
                <app-button
                  variant="success"
                  size="icon"
                  (click)="toggleModal('editItem', currency)"
                  >
                  <fa-icon [icon]="editIcon"></fa-icon>
                </app-button>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expanded Row Template -->
      <ng-template pTemplate="rowexpansion" let-currency>
        <tr>
          <td colspan="6">
            <div class="p-4 bg-gray-50 rounded-md">
              <div
                class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
                >
                <!-- Basic Information -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">Name</h5>
                  <p class="text-gray-800">{{ currency.name }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">Code</h5>
                  <p class="text-gray-800">{{ currency.code }}</p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Status
                  </h5>
                  <p class="text-gray-800">{{ currency.status }}</p>
                </div>

                <!-- Audit -->
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created Date
                  </h5>
                  <p class="text-gray-800">
                    {{ currency.createdDate | date: "yyyy-MM-dd HH:mm:ss" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified Date
                  </h5>
                  <p class="text-gray-800">
                    {{
                    currency.modifiedDate
                    ? (currency.modifiedDate | date: "yyyy-MM-dd HH:mm:ss")
                    : "-"
                    }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Created By
                  </h5>
                  <p class="text-gray-800">
                    {{ currency.createdUserName ?? "-" }}
                  </p>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Modified By
                  </h5>
                  <p class="text-gray-800">
                    {{ currency.modifiedUserName ?? "-" }}
                  </p>
                </div>
              </div>

              <!-- Description if available -->
              @if (currency.description) {
                <div class="mt-4">
                  <h5 class="text-sm font-semibold text-gray-600 mb-1">
                    Description
                  </h5>
                  <p class="text-gray-800 whitespace-pre-line">
                    {{ currency.description }}
                  </p>
                </div>
              }

              <!-- Action Buttons -->
              <div class="mt-4 flex justify-end">
                <app-button
                  size="sm"
                  variant="secondary"
                  icon="pi pi-pencil"
                  (click)="toggleModal('editItem', currency)"
                  >
                  Edit
                </app-button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6" class="p-8 text-center">
            <div class="flex flex-col items-center gap-4">
              <i class="text-4xl text-gray-400 pi pi-inbox"></i>
              <span class="text-gray-600">No currencies found</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  }
</app-card>

<!-- Add Currency Modal -->
<app-modal [isVisible]="modals.addItem" [config]="{ title: 'Add Currency' }">
  <form
    [formGroup]="addCurrencyForm"
    (ngSubmit)="handleCurrencySubmit('add')"
    class="flex flex-col gap-6"
    >
    <div class="grid gap-3 md:grid-cols-2">
      <app-form-field
        label="Name"
        placeholder="Name"
        type="text"
        [control]="addCurrencyForm.get('name')!"
        [required]="true"
        />

      <app-form-field
        label="Code"
        placeholder="Code"
        type="text"
        [control]="addCurrencyForm.get('code')!"
        [required]="true"
        />
    </div>

    <div class="flex items-center justify-end gap-3">
      <app-button (click)="toggleModal('addItem')" variant="outline"
        >Cancel</app-button
        >
        <app-button type="submit">Submit</app-button>
      </div>
    </form>
  </app-modal>

  <!-- Edit Currency Modal -->
  <app-modal [isVisible]="modals.editItem" [config]="{ title: 'Edit Currency' }">
    <form
      [formGroup]="editCurrencyForm"
      (ngSubmit)="handleCurrencySubmit('update')"
      >
      <div class="flex flex-col gap-3">
        <app-form-field
          label="Name"
          type="text"
          [control]="editCurrencyForm.get('name')!"
          [required]="true"
          />

        <app-form-field
          label="Code"
          type="text"
          [control]="editCurrencyForm.get('code')!"
          [required]="true"
          />

        <app-form-field
          label="Status"
          type="select"
        [options]="[
          { value: 'ACTIVE', label: 'Active' },
          { value: 'INACTIVE', label: 'Inactive' },
        ]"
          [control]="editCurrencyForm.get('status')!"
          [required]="true"
          />
      </div>

      <div class="flex items-center justify-end gap-3 mt-4">
        <app-button (click)="toggleModal('editItem')" variant="outline"
          >Cancel</app-button
          >
          <app-button type="submit">Submit</app-button>
        </div>
      </form>
    </app-modal>

    <!-- View Currency Modal -->
    <app-modal [isVisible]="modals.viewItem" [config]="{ title: 'View Currency' }">
      <div class="flex flex-col gap-3">
        <div>
          <label class="form-label">Name</label>
          <input
            type="text"
            [value]="selectedCurrency?.name"
            class="form-control"
            disabled
            />
        </div>
        <div>
          <label class="form-label">Code</label>
          <input
            type="text"
            [value]="selectedCurrency?.code"
            class="form-control"
            disabled
            />
        </div>
        <div>
          <label class="form-label">Status</label>
          <input
            type="text"
            [value]="selectedCurrency?.status"
            class="form-control"
            disabled
            />
        </div>

        <div class="flex justify-end">
          <app-button (click)="toggleModal('viewItem')">Close</app-button>
        </div>
      </div>
    </app-modal>

    <p-toast />
