import { DatePipe } from "@angular/common";
import { Component, inject, OnInit, signal } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { faEdit, faEye } from "@fortawesome/free-solid-svg-icons";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { RippleModule } from "primeng/ripple";
import { TableModule } from "primeng/table";
import { ToastModule } from "primeng/toast";
import { ToolbarModule } from "primeng/toolbar";

import { ButtonPermissionDirective } from "@/app/core/directives/role-button.directive";
import { Status } from "@/app/core/enums/status.enum";
import { ReportExportService } from "@/app/core/services/report-export-service";
import { handleError } from "@/app/core/utils/error-handler.util";
import { showToast } from "@/app/core/utils/show-toast.util";
import { onlyLettersAndHyphensValidator } from "@/app/core/validators/custom.validators";
import { markFormGroupTouched } from "@/app/shared/ui/form-field/form-field.util";
import { FormControlType } from "@/app/shared/ui/form/form.interface";
import { ButtonComponent } from "../../shared/ui/button/button.component";
import { CardComponent } from "../../shared/ui/card/card.component";
import { FormFieldComponent } from "../../shared/ui/form-field/form-field.component";
import { ModalComponent } from "../../shared/ui/modal/modal.component";
import { TextComponent } from "../../shared/ui/text/text.component";
import { CurrenciesService } from "./currencies.service";
import {
  CreateCurrencyRequest,
  Currency,
  UpdateCurrencyRequest,
} from "./currency.model";

type ModalKey = "addItem" | "editItem" | "viewItem";

@Component({
  selector: "app-currency",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    ModalComponent,
    ButtonComponent,
    CardComponent,
    TextComponent,
    TableModule,
    FormFieldComponent,
    ButtonPermissionDirective,
    ButtonModule,
    ToastModule,
    DialogModule,
    InputTextModule,
    RippleModule,
    ToolbarModule,
    IconFieldModule,
    InputIconModule,
    DatePipe,
  ],
  templateUrl: "./currency.component.html",
  providers: [DatePipe],
})
export class CurrencyComponent implements OnInit {
  private currencyService = inject(CurrenciesService);
  private fb = inject(FormBuilder);
  private readonly datePipe = inject(DatePipe);
  protected readonly reportExportService = inject(ReportExportService);

  readonly eyeIcon = faEye;
  readonly editIcon = faEdit;

  filteredCurrencies = signal<Currency[]>([]);
  currencies = signal<Currency[]>([]);
  selectedCurrency: Currency | null = null;

  isDataLoading = false;
  searchTerm = "";
  expandedRows: { [key: string]: boolean } = {};

  get exportData(): any[] {
    return this.filteredCurrencies().map((currency) => ({
      "Currency Name": currency.name,
      "Currency Code": currency.code,
      Status: currency.status,
      "Created Date": this.datePipe.transform(
        currency.createdDate,
        "yyyy-MM-dd HH:mm:ss",
      ),
      "Modified Date": currency.modifiedDate
        ? this.datePipe.transform(currency.modifiedDate, "yyyy-MM-dd HH:mm:ss")
        : "-",
      "Created By": currency.createdUserName ?? "-",
      "Modified By": currency.modifiedUserName ?? "-",
    }));
  }

  addCurrencyForm = this.fb.nonNullable.group({
    name: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    code: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(10),
        Validators.pattern(/^[A-Z]+$/),
      ],
    ],
  }) as FormGroup<FormControlType<CreateCurrencyRequest>>;

  editCurrencyForm = this.fb.group({
    id: ["", [Validators.required]],
    name: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        onlyLettersAndHyphensValidator(),
      ],
    ],
    code: [
      "",
      [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(10),
        Validators.pattern(/^[A-Z]+$/),
      ],
    ],
    status: ["", Validators.required],
  }) as FormGroup<FormControlType<UpdateCurrencyRequest>>;

  modals: Record<ModalKey, boolean> = {
    addItem: false,
    editItem: false,
    viewItem: false,
  };

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.isDataLoading = true;
    this.currencyService.getCurrencies().subscribe({
      next: (response) => {
        this.currencies.set(response.data);
        this.filteredCurrencies.set(response.data);
        this.isDataLoading = false;
      },
      error: (error) => {
        handleError(error, "Failed to load currencies");
        this.isDataLoading = false;
      },
    });
  }

  onSearch(term: string): void {
    if (!term) {
      this.filteredCurrencies.set(this.currencies());
      return;
    }

    const filtered = this.currencies().filter(
      (currency) =>
        currency.name.toLowerCase().includes(term.toLowerCase()) ||
        currency.code.toLowerCase().includes(term.toLowerCase()) ||
        currency.status?.toLowerCase().includes(term.toLowerCase()),
    );
    this.filteredCurrencies.set(filtered);
  }

  toggleRow(currency: Currency): void {
    if (this.expandedRows[currency.id]) {
      delete this.expandedRows[currency.id];
    } else {
      this.expandedRows[currency.id] = true;
    }
    this.expandedRows = { ...this.expandedRows };
  }

  toggleModal(mode: ModalKey, currency?: Currency): void {
    this.selectedCurrency = currency || null;

    if (mode === "editItem" && currency) {
      this.editCurrencyForm.patchValue({
        id: currency.id,
        name: currency.name,
        code: currency.code,
        status: currency.status || Status.ACTIVE,
      });
    }

    this.modals[mode] = !this.modals[mode];
  }

  handleCurrencySubmit(mode: "add" | "update"): void {
    const form = mode === "add" ? this.addCurrencyForm : this.editCurrencyForm;

    if (form.invalid) {
      markFormGroupTouched(form);
      return;
    }

    const submitAction =
      mode === "add"
        ? this.currencyService.createCurrency(
            form.value as CreateCurrencyRequest,
          )
        : this.currencyService.updateCurrency(
            form.value as UpdateCurrencyRequest,
          );

    submitAction.subscribe({
      next: () => {
        showToast({
          message: `Currency has been ${mode === "add" ? "added" : "updated"} successfully`,
          type: "success",
        });
        this.loadData();
        this.toggleModal(mode === "add" ? "addItem" : "editItem");
        form.reset();
      },
      error: (error) => {
        handleError(error, `Failed to ${mode} currency: ${error.message}`);
      },
    });
  }
}
