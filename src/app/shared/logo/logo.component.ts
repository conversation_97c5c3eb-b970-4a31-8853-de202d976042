import { APP_ROUTES } from '@/app/core/constants/routes.constant';
import { NgClass } from '@angular/common';
import { Component, Input } from '@angular/core';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-logo',
  templateUrl: './logo.component.html',
  standalone: true,
  imports: [NgClass, RouterModule],
})
export class LogoComponent {
  @Input() alt: string = 'DataDirect';
  @Input() size: string = 'h-10';
  @Input() variant: 'dark' | 'white' = 'dark';

  get src() {
    return this.variant === 'dark'
      ? 'images/logo.png'
      : 'images/logo-white.png';
  }

  routes = APP_ROUTES;
}
