// messages.component.ts
import { NgClass } from '@angular/common';
import { Component, EventEmitter, input, Output } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  faCircleCheck,
  faCircleInfo,
  faCircleXmark,
  faTimes,
  faTriangleExclamation,
} from '@fortawesome/free-solid-svg-icons';

type MessageVariant = 'primary' | 'success' | 'warning' | 'danger';

@Component({
  selector: 'app-messages',
  standalone: true,
  imports: [FontAwesomeModule, NgClass],
  template: `
    <div [class]="baseStyles" [ngClass]="variantStyles[variant()]" role="alert">
      <div class="flex items-center gap-3">
        <span class="inline-flex p-1">
          <fa-icon [icon]="icons[variant()]" [class]="iconSizes"></fa-icon>
        </span>
        <div class="flex-1">
          @if (title()) {
            <h3 class="font-medium">{{ title() }}</h3>
          }
          <p class="text-sm">{{ message() }}</p>
        </div>
        @if (dismissible()) {
          <button
            type="button"
            class="ml-auto hover:opacity-70 transition-opacity"
            (click)="onDismiss()"
            aria-label="Dismiss message"
          >
            <fa-icon [icon]="faTimes" class="h-4 w-4"></fa-icon>
          </button>
        }
      </div>
    </div>
  `,
})
export class MessagesComponent {
  // Inputs using Angular 18 syntax
  variant = input<MessageVariant>('primary');
  title = input<string>();
  message = input.required<string>();
  dismissible = input<boolean>(false);

  // Output for dismiss event
  @Output() dismissed = new EventEmitter<void>();

  // Font Awesome icons
  protected readonly faTimes = faTimes;
  protected readonly icons = {
    primary: faCircleInfo,
    success: faCircleCheck,
    warning: faTriangleExclamation,
    danger: faCircleXmark,
  };

  // Styles
  protected readonly baseStyles = 'rounded-lg p-3 text-sm flex items-start';
  protected readonly iconSizes = 'text-xl';

  protected readonly variantStyles = {
    primary: 'bg-sky-100 text-sky-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-amber-100 text-amber-800',
    danger: 'bg-red-100 text-red-800',
  };

  onDismiss(): void {
    this.dismissed.emit();
  }
}
