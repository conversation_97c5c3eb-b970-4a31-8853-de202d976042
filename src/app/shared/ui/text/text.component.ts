import { cn } from "@/app/core/utils/cn.util";
import { NgClass } from "@angular/common";
import { Component, Input, OnInit } from "@angular/core";

type Size =
  | "xs"
  | "small"
  | "regular"
  | "paragraph"
  | "title"
  | "titleLg"
  | "subHeading"
  | "heading";
type Weight = "light" | "normal" | "medium" | "semibold" | "bold";

@Component({
  selector: "app-text",
  templateUrl: "./text.component.html",
  standalone: true,
  imports: [NgClass],
})
export class TextComponent implements OnInit {
  @Input({ required: true }) variant!: `${Size}/${Weight}`;
  @Input() className?: string;
  classes: string = "";

  private textStyles = {
    size: {
      xs: "text-[11px] text-foreground",
      small: "text-[14px] text-foreground/80",
      regular: "text-sm sm:text-base text-foreground",
      paragraph: "text-sm sm:text-base text-foreground/80",
      title: "text-base tracking-tight text-foreground",
      titleLg: "text-xl tracking-tight text-foreground sm:text-xl 2xl:text-xl",
      subHeading: "text-xl tracking-tight text-foreground sm:text-2xl",
      heading:
        "text-4xl sm:text-5xl 2xl:text-6xl tracking-normal !leading-tight text-foreground",
    },
    weight: {
      light: "font-light",
      normal: "font-normal",
      medium: "font-medium",
      semibold: "font-semibold",
      bold: "font-bold",
    },
  };

  ngOnInit(): void {
    const [size, weight] = this.variant.split("/") as [Size, Weight];
    this.classes = cn(
      this.textStyles.size[size],
      this.textStyles.weight[weight],
      this.className,
    );
  }
}
