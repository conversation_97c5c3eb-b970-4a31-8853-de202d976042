
import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  forwardRef,
} from "@angular/core";
import {
  AbstractControl,
  ControlValueAccessor,
  FormControl,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from "@angular/forms";
import { Subject, debounceTime, takeUntil } from "rxjs";
import { FileValidation, FormFieldOption, InputType } from "./form-field.model";

@Component({
  selector: "app-form-field",
  standalone: true,
  imports: [ReactiveFormsModule],
  templateUrl: "./form-field.component.html",
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FormFieldComponent),
      multi: true,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormFieldComponent
  implements ControlValueAccessor, OnInit, OnDestroy
{
  private _control: FormControl = new FormControl();

  @Input() label = "";
  @Input() type: InputType = "text";
  @Input()
  get control(): FormControl {
    return this._control;
  }
  set control(value: AbstractControl) {
    if (!(value instanceof FormControl)) {
      throw new Error("FormFieldComponent only accepts FormControl instances");
    }
    this._control = value;
  }
  @Input() required = false;
  @Input() inputClass = "";
  @Input() placeholder = "";
  @Input() helperText = "";
  @Input() options: FormFieldOption[] = [];
  @Input() showCharacterCount = false;
  @Input() showClearButton = false;
  @Input() autocomplete?: string;
  @Input() pattern?: string;
  @Input() inputMode?: string;
  @Input() min?: number | string;
  @Input() max?: number | string;
  @Input() step?: number | string;
  @Input() minLength?: number;
  @Input() maxLength?: number;
  @Input() rows: number | string = 3;
  @Input() accept = "";
  @Input() multiple = false;
  @Input() fileValidation: FileValidation = {};
  @Input() customErrorMessages: Record<string, string> = {};
  @Input() debounceTime = 300;
  @Input() fileButtonText = "Choose File";

  @Output() valueChange = new EventEmitter<any>();
  @Output() touched = new EventEmitter<void>();
  @Output() inputChange = new EventEmitter<Event>();
  @Output() fileSelected = new EventEmitter<FileList>();
  @Output() fileRemoved = new EventEmitter<File>();

  @ViewChild("input") inputRef!: ElementRef;
  @ViewChild("fileInput") fileInputRef!: ElementRef<HTMLInputElement>;

  private destroy$ = new Subject<void>();
  private inputChange$ = new Subject<Event>();

  inputId = `form-field-${Math.random().toString(36).substring(2)}`;
  errorId = `${this.inputId}-error`;
  helperId = `${this.inputId}-helper`;

  isFocused = false;
  selectedFiles: File[] = [];
  fileValidationErrors: string[] = [];

  private onChange: (value: any) => void = () => {};
  private onTouched: () => void = () => {};

  get isStandardInput(): boolean {
    return [
      "text",
      "email",
      "password",
      "number",
      "tel",
      "date",
      "datetime-local",
      "url",
      "search",
    ].includes(this.type);
  }

  constructor() {
    this.inputChange$
      .pipe(debounceTime(this.debounceTime), takeUntil(this.destroy$))
      .subscribe((event) => {
        this.inputChange.emit(event);
      });
  }

  ngOnInit() {
    this.setupValueChanges();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupValueChanges() {
    this.control.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        this.onChange(value);
        this.valueChange.emit(value);
      });
  }

  writeValue(value: any): void {
    if (this.type === "file") {
      this.selectedFiles = value || [];
    }
    this.control.setValue(value, { emitEvent: false });
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    isDisabled ? this.control.disable() : this.control.enable();
  }

  onFocus(): void {
    this.isFocused = true;
  }

  onBlur(): void {
    this.isFocused = false;
    this.onTouched();
    this.touched.emit();
    this.control.markAsTouched();
  }

  onInput(event: Event): void {
    this.inputChange$.next(event);
  }

  onSelectChange(event: Event): void {
    const select = event.target as HTMLSelectElement;
    const value = select.value;
    this.control.setValue(value);
    this.inputChange.emit(event);
  }

  onFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    const files = input.files;

    if (!files?.length) return;

    if (this.validateFiles(files)) {
      this.selectedFiles = this.multiple ? Array.from(files) : [files[0]];
      this.control.setValue(this.selectedFiles);
      this.fileSelected.emit(files);
    } else {
      input.value = "";
      this.selectedFiles = [];
      this.control.setValue(null);
    }
  }

  validateFiles(files: FileList): boolean {
    const { maxSize, allowedTypes } = this.fileValidation;
    this.fileValidationErrors = [];
    let isValid = true;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      if (maxSize && file.size > maxSize) {
        const errorMessage =
          this.fileValidation.errors?.maxSize ||
          `File ${file.name} exceeds maximum size of ${this.formatFileSize(maxSize)}`;
        this.fileValidationErrors.push(errorMessage);
        isValid = false;
      }

      if (allowedTypes?.length && !allowedTypes.includes(file.type)) {
        const errorMessage =
          this.fileValidation.errors?.fileType ||
          `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(", ")}`;
        this.fileValidationErrors.push(errorMessage);
        isValid = false;
      }
    }

    if (!isValid) {
      this.control.setErrors({ ...this.control.errors, fileValidation: true });
    }

    return isValid;
  }

  removeFile(file: File): void {
    this.fileValidationErrors = [];
    this.selectedFiles = this.selectedFiles.filter((f) => f !== file);
    const newValue = this.selectedFiles.length ? this.selectedFiles : null;
    this.control.setValue(newValue);
    this.fileRemoved.emit(file);

    if (this.fileInputRef) {
      this.fileInputRef.nativeElement.value = "";
    }

    // Clear file validation errors if no files are selected
    if (!this.selectedFiles.length) {
      const currentErrors = { ...this.control.errors };
      delete currentErrors["fileValidation"];
      this.control.setErrors(
        Object.keys(currentErrors).length ? currentErrors : null,
      );
    }
  }

  clearValue(): void {
    this.control.setValue("");
    if (this.inputRef) {
      this.inputRef.nativeElement.focus();
    }
  }

  getAriaDescribedBy(): string {
    const ids: string[] = [];

    if (this.helperText && !(this.control.touched && this.control.invalid)) {
      ids.push(this.helperId);
    }

    if (this.control.touched && this.control.invalid) {
      ids.push(this.errorId);
    }

    return ids.join(" ");
  }

  getErrorMessage(): string {
    if (!this.control.errors) return "";

    // Return file validation errors if present
    if (this.fileValidationErrors.length > 0) {
      return this.fileValidationErrors[0];
    }

    const errorKey = Object.keys(this.control.errors)[0];
    const error = this.control.errors[errorKey];

    if (this.customErrorMessages[errorKey]) {
      return this.customErrorMessages[errorKey];
    }

    switch (errorKey) {
      case "required":
        return "This field is required";
      case "email":
        return "Please enter a valid email address";
      case "minlength":
        return `Minimum length is ${error.requiredLength} characters`;
      case "maxlength":
        return `Maximum length is ${error.requiredLength} characters`;
      case "min":
        return `Minimum value is ${error.min}`;
      case "max":
        return `Maximum value is ${error.max}`;
      case "pattern":
        return "Please enter a valid format";
      case "onlyLettersAndHyphens":
        return "Only letters and hyphens are allowed";
      default:
        return "Invalid value";
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 B";

    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  isNearMaxLength(): boolean {
    if (!this.maxLength || !this.control.value) return false;
    const currentLength = this.control.value.length;
    return currentLength >= this.maxLength * 0.9;
  }
}
