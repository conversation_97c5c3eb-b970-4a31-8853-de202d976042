<div class="relative">
  @if (label) {
    <label
      [for]="inputId"
      class="block mb-2 text-sm font-medium text-foreground"
    >
      {{ label }}
      @if (required) {
        <span class="ml-1 text-danger">*</span>
      }
    </label>
  }

  <div class="relative">
    <!-- Standard Input Types -->
    @if (isStandardInput) {
      <input
        #input
        [id]="inputId"
        [type]="type"
        [attr.inputmode]="inputMode"
        [attr.pattern]="pattern"
        [attr.min]="min"
        [attr.max]="max"
        [attr.step]="step"
        [attr.maxlength]="maxLength"
        [attr.minlength]="minLength"
        [attr.autocomplete]="autocomplete"
        [attr.placeholder]="placeholder"
        [attr.aria-describedby]="getAriaDescribedBy()"
        [attr.aria-invalid]="control.touched && control.invalid"
        [formControl]="control"
        class="block w-full px-4 py-2 text-sm transition-colors duration-200 ease-in-out border rounded-lg h-9 text-foreground bg-background border-input placeholder:text-muted focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 disabled:bg-light disabled:cursor-not-allowed {{
          inputClass
        }}"
        [class.border-danger]="control.touched && control.invalid"
        [class.focus:border-danger]="control.touched && control.invalid"
        [class.focus:ring-red-100]="control.touched && control.invalid"
        (focus)="onFocus()"
        (blur)="onBlur()"
        (input)="onInput($event)"
      />

      @if (showClearButton && control.value) {
        <button
          type="button"
          class="absolute transition-colors duration-200 -translate-y-1/2 right-3 top-1/2 text-muted hover:text-foreground"
          (click)="clearValue()"
          aria-label="Clear input"
        >
          ✕
        </button>
      }
    }

    <!-- Textarea -->
    @if (type === "textarea") {
      <textarea
        #input
        [id]="inputId"
        [attr.rows]="rows"
        [attr.maxlength]="maxLength"
        [attr.placeholder]="placeholder"
        [attr.aria-describedby]="getAriaDescribedBy()"
        [attr.aria-invalid]="control.touched && control.invalid"
        [formControl]="control"
        class="block w-full px-4 py-2 text-sm transition-colors duration-200 ease-in-out border rounded-lg text-foreground bg-background border-input placeholder:text-muted focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 disabled:bg-light disabled:cursor-not-allowed modern-scrollbar {{
          inputClass
        }}"
        [class.border-danger]="control.touched && control.invalid"
        [class.focus:border-danger]="control.touched && control.invalid"
        [class.focus:ring-red-100]="control.touched && control.invalid"
        (focus)="onFocus()"
        (blur)="onBlur()"
        (input)="onInput($event)"
      ></textarea>
    }

    <!-- Select -->
    @if (type === "select") {
      <div class="relative">
        <select
          #input
          [id]="inputId"
          [attr.aria-describedby]="getAriaDescribedBy()"
          [attr.aria-invalid]="control.touched && control.invalid"
          [formControl]="control"
          class="block w-full px-4 py-2 text-sm transition-colors duration-200 ease-in-out border rounded-lg appearance-none h-9 text-foreground bg-background border-input focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 disabled:bg-light disabled:cursor-not-allowed {{
            inputClass
          }}"
          [class.border-danger]="control.touched && control.invalid"
          [class.focus:border-danger]="control.touched && control.invalid"
          [class.focus:ring-red-100]="control.touched && control.invalid"
          (focus)="onFocus()"
          (blur)="onBlur()"
          (change)="onSelectChange($event)"
        >
          <option value="" disabled [selected]="!control.value">
            {{ placeholder || "Select an option" }}
          </option>
          @for (option of options; track option.value) {
            <option [value]="option.value" [disabled]="option.disabled">
              {{ option.label }}
            </option>
          }
        </select>
        <div
          class="absolute inset-y-0 flex items-center pointer-events-none right-3"
        >
          <svg
            class="w-4 h-4 transition-colors duration-200 text-muted"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M19 9l-7 7-7-7"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
            />
          </svg>
        </div>
      </div>
    }

    <!-- File Input -->
    @if (type === "file") {
      <div class="relative">
        <input
          #fileInput
          type="file"
          [id]="inputId"
          [accept]="accept"
          [multiple]="multiple"
          [attr.aria-describedby]="getAriaDescribedBy()"
          [attr.aria-invalid]="
            control.touched &&
            (control.invalid || fileValidationErrors.length > 0)
          "
          class="hidden"
          (change)="onFileChange($event)"
          (focus)="onFocus()"
          (blur)="onBlur()"
        />

        <button
          type="button"
          class="inline-flex items-center px-4 py-2 text-sm font-medium transition-colors duration-200 border rounded-lg h-9 text-foreground bg-background border-input hover:bg-light focus:outline-none focus:ring-2 focus:ring-primary/20 {{
            inputClass
          }}"
          (click)="fileInput.click()"
        >
          {{ fileButtonText }}
        </button>

        @if (selectedFiles.length) {
          <div class="mt-3 space-y-2">
            @for (file of selectedFiles; track file.name) {
              <div
                class="flex items-center justify-between p-3 border rounded-lg bg-light border-input/10"
              >
                <div class="flex items-center space-x-3">
                  <span class="text-sm text-foreground">{{ file.name }}</span>
                  <span class="text-xs text-muted"
                    >({{ formatFileSize(file.size) }})</span
                  >
                </div>
                <button
                  type="button"
                  class="transition-colors duration-200 text-muted hover:text-danger"
                  (click)="removeFile(file)"
                  aria-label="Remove file"
                >
                  ✕
                </button>
              </div>
            }
          </div>
        }

        <!-- File Validation Errors -->
        @if (fileValidationErrors.length > 0) {
          <div class="mt-2 text-sm text-danger" role="alert">
            @for (error of fileValidationErrors; track error) {
              <div>{{ error }}</div>
            }
          </div>
        }
      </div>
    }

    <!-- Character Counter -->
    @if (showCharacterCount && maxLength) {
      <div
        class="absolute text-xs transition-colors duration-200 right-3 -bottom-6"
        [class.text-muted]="!isNearMaxLength()"
        [class.text-warning]="isNearMaxLength()"
      >
        {{ control.value?.length || 0 }}/{{ maxLength }}
      </div>
    }

    <!-- Validation Messages -->
    @if (control.touched && control.invalid && !fileValidationErrors.length) {
      <div class="mt-2 text-sm text-danger" [id]="errorId" role="alert">
        {{ getErrorMessage() }}
      </div>
    }

    <!-- Helper Text -->
    @if (helperText && !(control.touched && control.invalid)) {
      <div class="mt-2 text-sm text-muted" [id]="helperId">
        {{ helperText }}
      </div>
    }
  </div>
</div>
