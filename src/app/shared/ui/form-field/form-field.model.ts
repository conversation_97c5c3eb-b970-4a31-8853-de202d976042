export type InputType =
  | "text"
  | "email"
  | "password"
  | "number"
  | "tel"
  | "date"
  | "datetime-local"
  | "textarea"
  | "select"
  | "file"
  | "checkbox"
  | "radio"
  | "url"
  | "search";

export interface FormFieldOption {
  value: string | number | boolean;
  label: string;
  disabled?: boolean;
}

export interface FileValidation {
  maxSize?: number;
  allowedTypes?: string[];
  errors?: {
    maxSize?: string;
    fileType?: string;
  };
}
