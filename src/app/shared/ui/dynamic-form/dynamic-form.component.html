
<form
  [formGroup]="form"
  (ngSubmit)="onSubmit()"
  class="flex flex-col space-y-4"
>
  @for (field of fields(); track field.key) {
    <div class="form-field">
      <label
        [for]="field.key"
        class="block text-sm font-medium text-gray-700 mb-1"
      >
        {{ field.label }}
      </label>

      @switch (field.type) {
        @case ("text") {
          <input
            type="text"
            [id]="field.key"
            [formControlName]="field.key"
            class="form-input"
          />
        }
        @case ("email") {
          <input
            type="email"
            [id]="field.key"
            [formControlName]="field.key"
            class="form-input"
          />
        }
        @case ("password") {
          <input
            type="password"
            [id]="field.key"
            [formControlName]="field.key"
            class="form-input"
          />
        }
        @case ("number") {
          <input
            type="number"
            [id]="field.key"
            [formControlName]="field.key"
            class="form-input"
          />
        }
        @case ("select") {
          <select
            [id]="field.key"
            [formControlName]="field.key"
            class="form-select"
          >
            <option value="" disabled>Select an option</option>
            @for (option of field.options; track option.value) {
              <option [value]="option.value">{{ option.label }}</option>
            }
          </select>
        }
        @case ("date") {
          <input
            type="date"
            [id]="field.key"
            [formControlName]="field.key"
            class="form-input"
          />
        }
      }

      <!-- Error messages -->
      @if (shouldShowErrors(field.key)) {
        <div class="error-container">
          @if (form.get(field.key)?.errors?.["required"]) {
            <span class="error-message">This field is required</span>
          }
          @if (form.get(field.key)?.errors?.["minlength"]) {
            <span class="error-message">
              Minimum length is {{ field.validations?.minLength }}
            </span>
          }
          @if (form.get(field.key)?.errors?.["maxlength"]) {
            <span class="error-message">
              Maximum length is {{ field.validations?.maxLength }}
            </span>
          }
          @if (form.get(field.key)?.errors?.["pattern"]) {
            <span class="error-message">Invalid format</span>
          }
          @if (form.get(field.key)?.errors?.["min"]) {
            <span class="error-message">
              Minimum value is {{ field.validations?.min }}
            </span>
          }
          @if (form.get(field.key)?.errors?.["max"]) {
            <span class="error-message">
              Maximum value is {{ field.validations?.max }}
            </span>
          }
        </div>
      }
    </div>
  }

  <div class="flex justify-end space-x-2">
    @if (showResetButton()) {
      <button
        type="button"
        (click)="resetForm()"
        class="btn-secondary"
        [disabled]="isSubmitting()"
      >
        Reset
      </button>
    }
    <button
      type="submit"
      class="btn-primary"
      [disabled]="!form.valid || isSubmitting()"
    >
      {{ submitButtonText() }}
      @if (isSubmitting()) {
        <span class="ml-2">
          <svg
            class="animate-spin h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </span>
      }
    </button>
  </div>
</form>
