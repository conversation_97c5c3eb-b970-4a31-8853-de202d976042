
import {
  Component,
  EventEmitter,
  Input,
  Output,
  computed,
  signal,
} from "@angular/core";
import {
  <PERSON><PERSON><PERSON>er,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";

export interface FormField {
  key: string;
  label: string;
  type: "text" | "email" | "password" | "number" | "select" | "date";
  validations?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    min?: number;
    max?: number;
  };
  options?: { value: any; label: string }[];
  defaultValue?: any;
}

@Component({
  selector: "app-dynamic-form",
  standalone: true,
  imports: [ReactiveFormsModule],
  templateUrl: "./dynamic-form.component.html",
  styleUrl: "./dynamic-form.component.css",
})
export class DynamicFormComponent {
  // Signals
  fields = signal<FormField[]>([]);
  submitButtonText = signal<string>("Submit");
  initialValues = signal<{ [key: string]: any } | undefined>(undefined);
  isSubmitting = signal<boolean>(false);
  showResetButton = signal<boolean>(true);

  // Form
  form!: FormGroup;

  // Computed values
  formValue = computed(() => this.form?.value);

  // Output
  @Output() formSubmit = new EventEmitter<any>();

  constructor(private readonly fb: FormBuilder) {}

  @Input() set formFields(fields: FormField[]) {
    this.fields.set(fields);
    this.initializeForm();
  }

  @Input() set buttonText(text: string) {
    this.submitButtonText.set(text);
  }

  @Input() set formInitialValues(values: { [key: string]: any } | undefined) {
    this.initialValues.set(values);
    if (this.form) {
      this.form.patchValue(values ?? {});
    }
  }

  @Input() set enableReset(show: boolean) {
    this.showResetButton.set(show);
  }

  private initializeForm() {
    const group: { [key: string]: FormControl } = {};

    this.fields().forEach((field) => {
      const validators = this.getValidators(field.validations);
      const initialValue =
        this.initialValues()?.[field.key] ?? field.defaultValue ?? "";
      group[field.key] = new FormControl(initialValue, validators);
    });

    this.form = this.fb.group(group);

    if (this.initialValues()) {
      this.form.patchValue(this.initialValues() ?? {});
    }
  }

  private getValidators(validations?: FormField["validations"]) {
    const validators: any[] = [];

    if (validations?.required) {
      validators.push(Validators.required);
    }
    if (validations?.minLength) {
      validators.push(Validators.minLength(validations.minLength));
    }
    if (validations?.maxLength) {
      validators.push(Validators.maxLength(validations.maxLength));
    }
    if (validations?.pattern) {
      validators.push(Validators.pattern(validations.pattern));
    }
    if (validations?.min) {
      validators.push(Validators.min(validations.min));
    }
    if (validations?.max) {
      validators.push(Validators.max(validations.max));
    }

    return validators;
  }

  shouldShowErrors(fieldKey: string): boolean {
    const control = this.form.get(fieldKey);
    return control
      ? control.invalid && (control.dirty || control.touched)
      : false;
  }

  onSubmit() {
    if (this.form.valid && !this.isSubmitting()) {
      this.isSubmitting.set(true);
      try {
        this.formSubmit.emit(this.form.value);
      } finally {
        this.isSubmitting.set(false);
      }
    } else {
      this.markAllAsTouched();
    }
  }

  private markAllAsTouched() {
    Object.keys(this.form.controls).forEach((key) => {
      const control = this.form.get(key);
      control?.markAsTouched();
    });
  }

  resetForm() {
    this.form.reset(this.initialValues() ?? {});
  }
}
