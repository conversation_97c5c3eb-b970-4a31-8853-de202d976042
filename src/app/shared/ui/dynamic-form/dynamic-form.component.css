:host {
  @apply block w-full max-w-lg mx-auto;
}

.form-input {
  @apply block w-full rounded-md border-gray-300 shadow-sm 
       focus:border-blue-500 focus:ring-blue-500 sm:text-sm;
}

.form-select {
  @apply block w-full px-4 py-2 text-sm transition-colors duration-200 ease-in-out border rounded-lg h-9 text-foreground bg-background placeholder:text-muted focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 disabled:bg-light disabled:cursor-not-allowed;
}

.error-container {
  @apply mt-1;
}

.error-message {
  @apply text-sm text-red-600;
}

.btn-primary {
  @apply inline-flex items-center px-4 py-2 text-sm font-medium text-white 
       bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none 
       focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 
       disabled:bg-gray-400 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 
       bg-white rounded-md border border-gray-300 hover:bg-gray-50 
       focus:outline-none focus:ring-2 focus:ring-offset-2 
       focus:ring-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed;
}
