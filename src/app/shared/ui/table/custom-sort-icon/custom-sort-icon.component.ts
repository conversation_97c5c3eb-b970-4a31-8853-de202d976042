// custom-sort-icon.component.ts

import { Component, Input } from '@angular/core';
import { Table } from 'primeng/table';

@Component({
  selector: 'app-custom-sort-icon',
  standalone: true,
  imports: [],
  template: `
    <span class="custom-sort-icon">
      <!-- Default non-sorted state -->
      @if (table.sortField !== field) {
        <i class="pi pi-sort-alt text-sm text-gray-400"></i>
      }

      <!-- Ascending sort -->
      @if (table.sortField === field && table.sortOrder === 1) {
        <i class="pi pi-sort-amount-up-alt text-sm text-primary"></i>
      }

      <!-- Descending sort -->
      @if (table.sortField === field && table.sortOrder === -1) {
        <i class="pi pi-sort-amount-down text-sm text-primary"></i>
      }
    </span>
  `,
  styles: [
    `
      .custom-sort-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-left: 0.25rem;
        font-size: 0.85rem;
      }

      .custom-sort-icon .pi {
        transition: all 0.2s ease;
      }

      /* Hover effect */
      th:hover .pi-sort-alt {
        opacity: 1;
        color: #6b7280; /* Gray-500 */
      }

      /* Optional: Add slight movement to the icons on sort */
      .pi-sort-amount-up-alt {
        transform: translateY(-1px);
      }

      .pi-sort-amount-down {
        transform: translateY(1px);
      }
    `,
  ],
})
export class CustomSortIconComponent {
  @Input() field: string = '';
  @Input() table!: Table;
}
