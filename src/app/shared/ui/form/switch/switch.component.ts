import { Component, Input, forwardRef } from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';

export interface SwitchProps {
  id?: string;
  name?: string;
  disabled?: boolean;
  class?: string;
  describedBy?: string;
  label?: string;
}

@Component({
  selector: 'app-switch',
  standalone: true,
  templateUrl: './switch.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SwitchComponent),
      multi: true,
    },
  ],
  imports: [ReactiveFormsModule],
})
export class SwitchComponent implements ControlValueAccessor {
  @Input() id?: string;
  @Input() name?: string;
  @Input() disabled = false;
  @Input() class = '';
  @Input() describedBy?: string;
  @Input() label = '';

  control = new FormControl<boolean | null>(false);
  touched = false;

  get switchClass(): string {
    return `peer inline-flex h-5 w-8 shrink-0 cursor-pointer items-center rounded-full
      border-2 border-transparent transition-colors focus-visible:outline-none
      focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2
      focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50
      data-[state=checked]:bg-primary data-[state=unchecked]:bg-input ${this.class}`;
  }

  toggle(): void {
    if (!this.disabled) {
      const currentValue = this.control.value ?? false; // Handle null values
      this.control.setValue(!currentValue);
      this.onTouched();
    }
  }

  writeValue(value: boolean | null): void {
    this.control.setValue(value ?? false, { emitEvent: false }); // Default to false if value is null
  }

  registerOnChange(fn: (value: boolean | null) => void): void {
    this.control.valueChanges.subscribe(fn);
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    isDisabled ? this.control.disable() : this.control.enable();
  }

  private onTouched = () => {};
}
