<label
  class="inline-flex items-center gap-2"
  [class.cursor-not-allowed]="disabled"
>
  <button
    type="button"
    role="switch"
    [attr.aria-checked]="control.value"
    [attr.id]="id"
    [attr.name]="name"
    [attr.aria-describedby]="describedBy"
    [attr.data-state]="control.value ? 'checked' : 'unchecked'"
    [disabled]="disabled"
    (click)="toggle()"
    [class]="switchClass"
  >
    <span
      class="pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4"
    ></span>
  </button>
  <span class="text-sm font-medium leading-none" [class.opacity-70]="disabled">
    {{ label }}
  </span>
</label>
