import { Component, Input, forwardRef } from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';

export interface TextareaProps {
  placeholder?: string;
  id?: string;
  name?: string;
  readonly?: boolean;
  disabled?: boolean;
  class?: string;
  describedBy?: string;
  rows?: number;
  maxLength?: number;
}

@Component({
  selector: 'app-textarea',
  standalone: true,
  templateUrl: './textarea.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TextareaComponent),
      multi: true,
    },
  ],
  imports: [ReactiveFormsModule],
})
export class TextareaComponent<T> implements ControlValueAccessor {
  @Input() placeholder = '';
  @Input() id?: string;
  @Input() name?: string;
  @Input() readonly = false;
  @Input() disabled = false;
  @Input() class = '';
  @Input() describedBy?: string;
  @Input() rows = 3;
  @Input() maxLength?: number;

  control = new FormControl<T | null>(null);
  touched = false;

  get textareaClass(): string {
    return `flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2
      text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none
      focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed
      disabled:opacity-50 ${this.class}`;
  }

  writeValue(value: T | null): void {
    this.control.setValue(value, { emitEvent: false });
  }

  registerOnChange(fn: (value: T | null) => void): void {
    this.control.valueChanges.subscribe(fn);
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    isDisabled ? this.control.disable() : this.control.enable();
  }

  private onTouched = () => {};

  onBlur(): void {
    if (!this.touched) {
      this.touched = true;
      this.onTouched();
    }
  }
}
