import { Component, Input, forwardRef } from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';

export interface DatepickerProps {
  id?: string;
  name?: string;
  readonly?: boolean;
  disabled?: boolean;
  class?: string;
  describedBy?: string;
  min?: string;
  max?: string;
}

@Component({
  selector: 'app-datepicker',
  standalone: true,
  templateUrl: './datepicker.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DatepickerComponent),
      multi: true,
    },
  ],
  imports: [ReactiveFormsModule],
})
export class DatepickerComponent<T> implements ControlValueAccessor {
  @Input() id?: string;
  @Input() name?: string;
  @Input() readonly = false;
  @Input() disabled = false;
  @Input() class = '';
  @Input() describedBy?: string;
  @Input() min?: string;
  @Input() max?: string;

  control = new FormControl<T | null>(null);
  touched = false;

  get dateClass(): string {
    return `flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1
      text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1
      focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 ${this.class}`;
  }

  writeValue(value: T | null): void {
    this.control.setValue(value, { emitEvent: false });
  }

  registerOnChange(fn: (value: T | null) => void): void {
    this.control.valueChanges.subscribe(fn);
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    isDisabled ? this.control.disable() : this.control.enable();
  }

  private onTouched = () => {};

  onBlur(): void {
    if (!this.touched) {
      this.touched = true;
      this.onTouched();
    }
  }
}
