import { Component, Input, forwardRef } from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';

export interface InputProps {
  type?: 'text' | 'email' | 'password' | 'number';
  placeholder?: string;
  id?: string;
  name?: string;
  readonly?: boolean;
  disabled?: boolean;
  class?: string;
  describedBy?: string;
}

@Component({
  selector: 'app-input',
  standalone: true,
  templateUrl: './input.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InputComponent),
      multi: true,
    },
  ],
  imports: [ReactiveFormsModule],
})
export class InputComponent<T> implements ControlValueAccessor {
  @Input() type: InputProps['type'] = 'text';
  @Input() placeholder: string = '';
  @Input() id?: string;
  @Input() name?: string;
  @Input() readonly = false;
  @Input() disabled = false;
  @Input() class = '';
  @Input() describedBy?: string;
  @Input() min?: number; // New min input
  @Input() max?: number; // New max input

  control = new FormControl<T | null>(null);
  touched = false;

  get inputClass(): string {
    return `flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1
      text-sm shadow-sm transition-colors file:border-0 file:bg-transparent
      file:text-sm file:font-medium placeholder:text-muted-foreground
      focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring
      disabled:cursor-not-allowed disabled:bg-background disabled:opacity-75
      ${this.class}`;
  }

  writeValue(value: T | null): void {
    this.control.setValue(value, { emitEvent: false });
  }

  registerOnChange(fn: (value: T | null) => void): void {
    this.control.valueChanges.subscribe(fn);
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    isDisabled ? this.control.disable() : this.control.enable();
  }

  private onTouched = () => {};

  onBlur(): void {
    if (!this.touched) {
      this.touched = true;
      this.onTouched();
    }
  }
}
