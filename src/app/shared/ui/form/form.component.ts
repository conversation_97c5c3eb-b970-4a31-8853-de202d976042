
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormControlType } from './form.interface';

@Component({
  selector: 'app-form',
  standalone: true,
  imports: [ReactiveFormsModule],
  templateUrl: './form.component.html',
})
export class FormComponent<T extends Record<string, any>> {
  @Input({ required: true }) formGroup!: FormGroup<FormControlType<T>>;
  @Output() submitted = new EventEmitter<T>();

  onSubmit(event: SubmitEvent): void {
    event.preventDefault();
    if (this.formGroup.valid) {
      // Use type assertion here since we know the form structure matches T
      this.submitted.emit(this.formGroup.getRawValue() as T);
    } else {
      this.formGroup.markAllAsTouched();
    }
  }
}
