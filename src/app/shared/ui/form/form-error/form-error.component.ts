import { Component, Input, Signal } from "@angular/core";
import { AbstractControl, ValidationErrors } from "@angular/forms";

// Define a type for the error message functions
type ErrorMessageFn = (error: any) => string;

// Define the structure of our error messages object
interface ErrorMessages {
  [key: string]: ErrorMessageFn;
}

@Component({
  selector: "app-form-error",
  template: `
    @if (
      control() && control().invalid && (control().dirty || control().touched)
      ) {
      <div
        >
        @if (control().errors; as errors) {
          @for (errorKey of getErrorKeys(errors); track errorKey) {
            <small
              class="error-message"
              >
              {{ getErrorMessage(errorKey, errors[errorKey]) }}
            </small>
          }
        }
      </div>
    }
    `,
  standalone: true,
})
export class FormErrorComponent {
  @Input() control!: Signal<AbstractControl>;

  private errorMessages: ErrorMessages = {
    required: () => "This field is required",
    email: () => "Invalid email format",
    passwordStrength: () =>
      "Password must contain uppercase, lowercase, number, and special character",
    passwordMismatch: () => "Passwords do not match",
    minlength: (error: any) =>
      `Minimum length is ${error.requiredLength} characters`,
    maxlength: (error: any) =>
      `Maximum length is ${error.requiredLength} characters`,
  };

  getErrorKeys(errors: ValidationErrors): string[] {
    return Object.keys(errors || {});
  }

  getErrorMessage(errorKey: string, errorValue: any): string {
    const messageFunc =
      this.errorMessages[errorKey as keyof typeof this.errorMessages];
    return messageFunc
      ? messageFunc(errorValue)
      : `Validation error: ${errorKey}`;
  }
}
