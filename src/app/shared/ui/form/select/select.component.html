<select
  [formControl]="control"
  [attr.id]="id"
  [attr.name]="name"
  [attr.aria-describedby]="describedBy"
  [class]="selectClass"
  (blur)="onBlur()"
  >
  <option value="" disabled selected hidden>{{ placeholder }}</option>
  @for (option of options; track option) {
    <option
      [value]="option.value"
      [disabled]="option.disabled"
      >
      {{ option.label }}
    </option>
  }
</select>
