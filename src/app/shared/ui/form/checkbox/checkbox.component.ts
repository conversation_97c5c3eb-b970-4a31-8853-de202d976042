import { Component, Input, forwardRef } from '@angular/core';
import {
  ControlValueAccessor,
  FormControl,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';

export interface CheckboxProps {
  id?: string;
  name?: string;
  disabled?: boolean;
  class?: string;
  describedBy?: string;
  label?: string;
}

@Component({
  selector: 'app-checkbox',
  standalone: true,
  templateUrl: './checkbox.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CheckboxComponent),
      multi: true,
    },
  ],
  imports: [ReactiveFormsModule],
})
export class CheckboxComponent implements ControlValueAccessor {
  @Input() id?: string;
  @Input() name?: string;
  @Input() disabled = false;
  @Input() class = '';
  @Input() describedBy?: string;
  @Input() label = '';

  control = new FormControl<boolean | null>(false);
  touched = false;

  get checkboxClass(): string {
    return `form-checkbox h-5 w-5 text-primary rounded border-gray-300 focus:ring-0 ${this.class}`;
  }

  writeValue(value: boolean | null): void {
    this.control.setValue(value ?? false, { emitEvent: false });
  }

  registerOnChange(fn: (value: boolean | null) => void): void {
    this.control.valueChanges.subscribe((value) => {
      fn(value ?? false); // Provide a default `false` value if `null` is received
    });
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    isDisabled ? this.control.disable() : this.control.enable();
  }

  private onTouched = () => {};
}
