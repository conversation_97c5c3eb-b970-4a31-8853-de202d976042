<button
  [type]="type()"
  [disabled]="disabled()"
  [class]="computedClasses()"
  [attr.aria-label]="ariaLabel()"
  [attr.aria-disabled]="disabled()"
>
  @if (loading()) {
    <div class="inline-block mr-2 animate-spin">
      <fa-icon [icon]="loadingIcon" [class]="iconClass()"></fa-icon>
    </div>
  }

  @if (leftIcon() && !loading()) {
    <fa-icon [icon]="leftIcon()!" [class]="iconClass()"></fa-icon>
  }

  @if (label()) {
    <span [class]="iconClass() ? 'ml-2' : ''">{{ label() }}</span>
  }

  <ng-content></ng-content>

  @if (rightIcon() && !loading()) {
    <fa-icon [icon]="rightIcon()!" [class]="iconClass() + ' ml-2'"></fa-icon>
  }
</button>
