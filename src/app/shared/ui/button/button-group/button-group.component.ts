import { Component, input } from '@angular/core';

@Component({
  selector: 'app-button-group',
  standalone: true,
  template: `
    <div
      class="inline-flex"
      [class]="orientation() === 'vertical' ? 'flex-col' : 'flex-row'"
      role="group"
    >
      <ng-content></ng-content>
    </div>
  `,
  styles: [
    `
      :host ::ng-deep .flex-row app-button:not(:first-child) button {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
      :host ::ng-deep .flex-row app-button:not(:last-child) button {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-right: 0; /* Added this line */
      }

      :host ::ng-deep .flex-col app-button:not(:first-child) button {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
      }
      :host ::ng-deep .flex-col app-button:not(:last-child) button {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }
    `,
  ],
})
export class ButtonGroupComponent {
  orientation = input<'horizontal' | 'vertical'>('horizontal');
}
