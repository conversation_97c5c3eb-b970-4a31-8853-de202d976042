<div
  class="flex"
  [ngClass]="{
    'flex-row': orientation === 'row',
    'flex-col': orientation === 'column',
  }"
>
  @for (button of buttons; track button) {
    <app-button
      [variant]="button.variant || 'primary'"
      [type]="button.type || 'button'"
      [size]="button.size || 'default'"
      [disabled]="button.disabled || false"
      [isLoading]="button.isLoading || false"
      [fullWidth]="button.fullWidth || false"
      [outline]="button.outline || false"
      (click)="button.onClick ? button.onClick() : null"
    >
      {{ button.label }}
    </app-button>
  }
</div>
