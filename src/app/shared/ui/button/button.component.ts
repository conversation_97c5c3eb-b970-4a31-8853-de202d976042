export type ButtonSize = "sm" | "md" | "lg" | "icon";

// button.component.ts
import { Component, computed, input } from "@angular/core";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { IconDefinition } from "@fortawesome/fontawesome-svg-core";
import { faSpinner } from "@fortawesome/free-solid-svg-icons";

export type ButtonVariant =
  | "primary"
  | "secondary"
  | "success"
  | "danger"
  | "warning"
  | "ghost"
  | "outline"
  | "outline-primary"
  | "outline-secondary"
  | "outline-success"
  | "outline-danger"
  | "outline-warning";

@Component({
  selector: "app-button",
  standalone: true,
  imports: [FontAwesomeModule],
  templateUrl: "./button.component.html",
})
export class ButtonComponent {
  type = input<"button" | "submit" | "reset">("button");
  variant = input<ButtonVariant>("primary");
  size = input<ButtonSize>("md");
  disabled = input<boolean>(false);
  loading = input<boolean>(false);
  leftIcon = input<IconDefinition | null>(null);
  rightIcon = input<IconDefinition | null>(null);
  label = input<string>();
  ariaLabel = input<string>();
  block = input<boolean>(false);
  class = input<string>();

  protected readonly loadingIcon = faSpinner;

  private readonly baseStyles =
    "inline-flex items-center justify-center font-medium rounded-md transition-all focus:outline-none focus:ring-0 disabled:opacity-60 disabled:cursor-not-allowed";

  private readonly variantStyles: Record<ButtonVariant, string> = {
    primary: "bg-primary text-white hover:bg-primary/90 focus:ring-primary",
    secondary:
      "bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-100",
    success: "bg-green-500 text-white hover:bg-green-700 focus:ring-green-500",
    danger: "bg-red-500 text-white hover:bg-red-700 focus:ring-red-500",
    warning: "bg-amber-500 text-white hover:bg-amber-500 focus:ring-amber-400",
    ghost: "bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500",
    outline:
      "border border-gray-500 text-gray-500 hover:bg-gray-500 hover:text-white focus:ring-gray-500",
    "outline-primary":
      "border border-primary text-primary hover:text-white hover:bg-primary focus:ring-primary",
    "outline-secondary":
      "border border-gray-500 text-gray-500 hover:bg-gray-100 focus:ring-gray-500",
    "outline-success":
      "border border-green-500 text-green-500 hover:bg-green-100 focus:ring-green-500",
    "outline-danger":
      "border border-red-500 text-red-500 hover:bg-red-100 focus:ring-red-500",
    "outline-warning":
      "border border-amber-500 text-amber-500 hover:bg-amber-100 focus:ring-amber-400",
  };

  private readonly sizeStyles: Record<ButtonSize, string> = {
    sm: "px-3 py-1.5 text-sm h-7",
    md: "px-4 py-2 text-sm h-9",
    lg: "px-6 py-3 text-base h-11",
    icon: "p-2 size-7 flex items-center justify-center rounded-md",
  };

  protected readonly iconClass = computed(
    () =>
      ({
        sm: "h-4 w-4",
        md: "h-5 w-5",
        lg: "h-6 w-6",
        icon: "h-4 w-4",
      })[this.size()],
  );

  protected readonly computedClasses = computed(() => {
    const classes = [
      this.baseStyles,
      this.variantStyles[this.variant()],
      this.sizeStyles[this.size()],
      this.class(),
    ];

    if (this.block()) {
      classes.push("w-full");
    }

    return classes.join(" ");
  });
}
