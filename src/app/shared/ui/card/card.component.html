<div
  class="p-6 bg-white rounded-2xl  flex flex-col {{ className }}"
  [ngClass]="className"
>
  @if (title || subtitle) {
    <div class="mb-4">
      @if (title) {
        <app-text variant="titleLg/semibold">
          {{ title }}
        </app-text>
      }
      @if (subtitle) {
        <app-text variant="small/normal">
          {{ subtitle }}
        </app-text>
      }
    </div>
  }

  <div class="flex-grow">
    <ng-content></ng-content>
  </div>

  @if (footerTemplate) {
    <div class="pt-4 mt-4 border-t border-gray-200">
      <ng-container *ngTemplateOutlet="footerTemplate"></ng-container>
    </div>
  }
</div>
