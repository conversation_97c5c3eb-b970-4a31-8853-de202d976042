import { cn } from '@/app/core/utils/cn.util';
import { Component, ContentChild, Input, TemplateRef } from '@angular/core';
import { TextComponent } from '../text/text.component';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-card',
  templateUrl: './card.component.html',
  standalone: true,
  imports: [TextComponent, NgClass],
})
export class CardComponent {
  @Input() title?: string;
  @Input() subtitle?: string;
  @Input() className: string = '';
  cn = cn;

  @ContentChild('headerTemplate') headerTemplate?: TemplateRef<any>;
  @ContentChild('footerTemplate') footerTemplate?: TemplateRef<any>;
}
