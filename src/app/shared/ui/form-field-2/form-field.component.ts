
import { Component, ContentChild, Input } from "@angular/core";
import { FormGroup, NgControl } from "@angular/forms";

@Component({
  selector: "app-form-field-2",
  standalone: true,
  imports: [],
  template: `
    <div>
      <ng-content></ng-content>
      @if (shouldShowError) {
        <div class="invalid-feedback">
          {{ errorMessage }}
        </div>
      }
    </div>
  `,
  providers: [],
})
export class FormField2Component {
  @Input() form?: FormGroup;
  @ContentChild(NgControl) control!: NgControl;

  get shouldShowError(): boolean {
    return !!(
      this.control?.invalid &&
      (this.control?.dirty || this.control?.touched)
    );
  }

  get errorMessage(): string {
    if (!this.control || !this.control.errors) return "";

    const errors = this.control.errors;
    if (errors["required"]) return "This field is required";
    if (errors["minlength"]) return "Input is too short";
    if (errors["maxlength"]) return "Input is too long";
    if (errors["pattern"]) return "Invalid input format";

    return "Invalid input";
  }
}
