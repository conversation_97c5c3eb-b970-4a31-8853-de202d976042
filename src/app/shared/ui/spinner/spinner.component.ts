import { NgClass } from '@angular/common';
import { Component, Input } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faSpinner } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-spinner',
  templateUrl: './spinner.component.html',
  standalone: true,
  imports: [FontAwesomeModule, NgClass],
})
export class SpinnerComponent {
  @Input() strokeWidth: number = 8;
  @Input() stroke: string = '#000';
  @Input() ariaLabel: string = 'Loading';
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() variant: 'primary' | 'secondary' | 'warning' | 'danger' = 'primary';
  @Input() isLoading: boolean = true;

  protected readonly loadingIcon = faSpinner;

  get variantClass(): string {
    const variantMap = {
      primary: 'text-primary',
      secondary: 'text-secondary',
      warning: 'text-warning',
      danger: 'text-danger',
    };
    return variantMap[this.variant];
  }

  get sizeClass(): string {
    const sizeMap = {
      sm: 'text-base',
      md: 'text-2xl',
      lg: 'text-4xl',
    };
    return sizeMap[this.size];
  }
}
