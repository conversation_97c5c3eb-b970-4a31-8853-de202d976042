// modal.component.ts

import { Component, EventEmitter, Input, Output } from "@angular/core";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { ButtonComponent } from "../button/button.component";

export interface ModalConfig {
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  confirmDisabled?: boolean;
}

@Component({
  selector: "app-modal",
  standalone: true,
  imports: [DialogModule, ButtonModule, ButtonComponent],
  template: `
    <p-dialog
      [(visible)]="isVisible"
      (visibleChange)="onVisibilityChange($event)"
      [modal]="true"
      [breakpoints]="breakpoints"
      [style]="dialogStyle"
      [draggable]="false"
      [resizable]="false"
      [closable]="true"
      [closeOnEscape]="true"
      styleClass="custom-modal"
    >
      <!-- Conditional Header -->
      @if (config.title) {
        <ng-template pTemplate="header">
          <div class="flex items-center justify-between w-full">
            <h2 class="text-xl font-semibold text-foreground">
              {{ config.title }}
            </h2>
          </div>
        </ng-template>
      }

      <!-- Content -->
      <div class="px-6 py-4 space-y-6">
        @if (config.message) {
          <div class="text-sm text-foreground/60">
            {{ config.message }}
          </div>
        }

        <ng-content></ng-content>
      </div>

      <!-- Conditional Footer -->
      @if (config.cancelText || config.confirmText) {
        <ng-template pTemplate="footer">
          <div class="flex justify-end space-x-4">
            @if (config.cancelText) {
              <app-button size="sm" variant="outline" (click)="onCancel()">
                {{ config.cancelText }}
              </app-button>
            }
            @if (config.confirmText) {
              <app-button
                size="sm"
                class=""
                [disabled]="config.confirmDisabled || false"
                (click)="onConfirm()"
              >
                {{ config.confirmText }}
              </app-button>
            }
          </div>
        </ng-template>
      }
    </p-dialog>
  `,
  styles: [
    `
      :host ::ng-deep .custom-modal {
        @apply rounded-lg shadow-lg border border-ring/10;
      }
      :host ::ng-deep .p-dialog-header {
        @apply p-4 border-b border-ring/10;
      }
      :host ::ng-deep .p-dialog-content {
        @apply p-0;
      }
      :host ::ng-deep .p-dialog-footer {
        @apply p-4 border-t border-ring/10;
      }
    `,
  ],
})
export class ModalComponent {
  @Input() config: ModalConfig = {};
  @Input() isVisible = false;
  @Output() visibilityChange = new EventEmitter<boolean>();
  @Output() confirm = new EventEmitter<void>();
  @Output() cancel = new EventEmitter<void>();
  @Input() size: "sm" | "md" | "lg" | "xl" = "md";

  breakpoints = {
    "1199px": "75vw",
    "575px": "90vw",
  };

  get dialogStyle() {
    const sizeMap: Record<string, string> = {
      sm: "30vw",
      md: "50vw",
      lg: "70vw",
      xl: "90vw",
    };
    return {
      width: sizeMap[this.size] || "50vw",
    };
  }

  onVisibilityChange(visible: boolean) {
    this.isVisible = visible;
    this.visibilityChange.emit(visible);
  }

  onConfirm() {
    this.confirm.emit();
    this.isVisible = false;
    this.visibilityChange.emit(false);
  }

  onCancel() {
    this.cancel.emit();
    this.isVisible = false;
    this.visibilityChange.emit(false);
  }
}
