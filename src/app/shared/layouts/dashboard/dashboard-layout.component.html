<div class="flex w-full min-h-screen text-gray-900 bg-gray-50">
  <!-- Sidebar Overlay -->
  @if (isSidebarOpen()) {
    <div
      class="fixed inset-0 z-40 bg-black/40 lg:hidden"
      (click)="toggleSidebar()"
    ></div>
  }

  <!-- Sidebar -->
  <aside
    class="flex-none fixed z-50 w-64 h-screen bg-[#233543] shadow-xl transition-transform duration-300
    transform
    {{ isSidebarOpen() ? 'translate-x-0' : '-translate-x-full' }}
    lg:translate-x-0"
    >
    <!-- Sidebar Header -->
    <div class="flex items-center p-6 space-x-3">
      <app-logo size="h-8" variant="dark" />
      <app-text variant="regular/semibold" className="text-xl text-white">
        DataDirect
      </app-text>
    </div>

    <!-- Navigation Items -->
    <nav class="p-4 space-y-2 h-[calc(100vh-80px)] modern-scrollbar">
      @for (item of filteredNavItems(); track $index) {
        <!-- Section Header with Icon -->
        @if (item.header && item.children && item.children.length > 0) {
          <div class="flex items-center gap-2 px-3 pt-6 mb-1">
            @if (item.icon) {
              <fa-icon
                [icon]="item.icon"
                class="text-sm text-gray-500"
              ></fa-icon>
            }
            <p class="text-xs text-gray-400 uppercase">{{ item.header }}</p>
          </div>
        }

        <!-- Main Menu Item -->
        @if (item.label && item.routerLink) {
          <a
            [routerLink]="item.routerLink"
            routerLinkActive="bg-dark text-background"
            class="flex items-center gap-3 px-3 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-dark hover:text-background group"
            >
            @if (item.icon) {
              <fa-icon [icon]="item.icon" class="text-xl"></fa-icon>
            }
            <span>{{ item.label }}</span>
            @if (item.badge) {
              <span class="ml-auto badge badge-warning">{{ item.badge }}</span>
            }
          </a>
        }

        <!-- First Level Children -->
        @if (item.children) {
          @for (child of item.children; track child) {
            @if (child.label) {
              <div
                (click)="handleNavItemClick(child)"
                [routerLink]="child.routerLink"
                class="flex items-center gap-3 px-3 py-2 text-sm font-medium text-gray-300 rounded-md cursor-pointer hover:bg-dark hover:text-background group"
                >
                @if (child.icon) {
                  <fa-icon [icon]="child.icon" class="text-xl"></fa-icon>
                }
                <span>{{ child.label }}</span>
              </div>
            }
          }
        }
      }
    </nav>
  </aside>

  <!-- Main Content Area -->
  <div class="flex flex-col w-full transition-all duration-300">
    <!-- Header -->
    <header
      class="sticky top-0 z-40 backdrop-blur-sm transition-shadow
      {{ scrolled ? ' bg-white/90' : '' }}"
      >
      <div class="px-4 py-3 lg:px-8">
        <div class="flex items-center justify-between">
          <!-- Mobile Menu Toggle -->
          <div class="flex items-center space-x-4">
            <button
              (click)="toggleSidebar()"
              class="text-gray-600 transition-colors lg:hidden hover:text-gray-900"
              >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-6 h-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 6h16M4 12h16M4 18h16"
                  />
              </svg>
            </button>

            <!-- Mobil Logo and Brand -->
            <div class="flex items-center space-x-4 lg:hidden">
              <app-logo variant="dark" size="h-6" />
              <app-text variant="regular/semibold" className="text-gray-800">
                DataDirect
              </app-text>
            </div>
          </div>

          <!-- Header Actions -->
          <div class="flex items-center space-x-4">
            <!-- Search Button -->
            <!-- <button class="text-gray-600 transition-colors hover:text-gray-900">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-5 h-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
            </svg>
          </button> -->

          <!-- Notifications -->
          <!-- <button
          class="relative text-gray-600 transition-colors hover:text-gray-900"
          >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-5 h-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
              />
          </svg>
          <span
            class="absolute flex items-center justify-center w-4 h-4 text-xs text-white bg-red-500 rounded-full -top-1 -right-1"
            >
            3
          </span>
        </button> -->

        <!-- User Profile Dropdown -->
        <button (click)="menu.toggle($event)">
          <p-avatar
            [label]="userInitials"
            styleClass="cursor-pointer text-sm font-semibold"
            size="normal"
            [style.backgroundColor]="avatarColors.bgColor"
            [style.color]="avatarColors.textColor"
            shape="square"
            />
        </button>

        <!-- Dropdown Menu -->
        <div class="card">
          <p-menu
            #menu
            [popup]="true"
            [model]="items"
            class="flex justify-center"
            styleClass="w-full md:w-60"
            >
            <ng-template #submenuheader let-item>
              <span class="font-semibold">{{ item.label }}</span>
            </ng-template>
            <ng-template #item let-item>
              <a
                [routerLink]="item.href"
                (click)="handleMenuItemClick(item)"
                pRipple
                class="flex items-center gap-2 text-sm p-menu-item-link"
                >
                <span [class]="item.icon"></span>
                <span>{{ item.label }}</span>
                @if (item.badge) {
                  <p-badge
                    class="ml-auto"
                    [value]="item.badge"
                    />
                }
                @if (item.shortcut) {
                  <span
                    class="p-1 ml-auto text-xs border rounded border-surface bg-emphasis text-muted-color"
                    >{{ item.shortcut }}</span
                    >
                  }
                </a>
              </ng-template>
              <ng-template #end>
                <div
                  class="relative flex items-start w-full gap-2 p-2 overflow-hidden transition-colors duration-200 bg-transparent border-0 rounded-none cursor-default hover:bg-surface-100 dark:hover:bg-surface-800 text-start"
                  >
                  <p-avatar
                    [label]="userInitials"
                    styleClass="cursor-pointer text-sm font-semibold"
                    size="normal"
                    [style.backgroundColor]="avatarColors.bgColor"
                    [style.color]="avatarColors.textColor"
                    shape="square"
                    />
                  <div>
                    <div class="text-sm font-bold text-gray-800">
                      {{ currentUser?.firstName }} {{ currentUser?.lastName }}
                    </div>
                    <div
                      class="text-[10px] tracking-widest text-gray-500 uppercase"
                      >
                      {{ userRole.replaceAll("_", " ").toLowerCase() }}
                    </div>
                  </div>
                </div>
              </ng-template>
            </p-menu>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="min-h-screen p-4 bg-gray-50 lg:p-8 lg:pt-4 lg:pl-72">
    <router-outlet></router-outlet>
  </main>
</div>
</div>
