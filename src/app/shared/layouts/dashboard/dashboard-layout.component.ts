import { AdminUser } from "@/app/core/models/userInfo.model";
import { AuthService } from "@/app/features/auth/core/services/auth.service";

import {
  ChangeDetectorRef,
  Component,
  inject,
  OnDestroy,
  OnInit,
  signal,
} from "@angular/core";
import { RouterModule } from "@angular/router";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
import { MenuItem } from "primeng/api";
import { AvatarModule } from "primeng/avatar";
import { BadgeModule } from "primeng/badge";
import { ButtonModule } from "primeng/button";
import { MenuModule } from "primeng/menu";
import { RippleModule } from "primeng/ripple";
import { fromEvent, Subscription } from "rxjs";
import { map, throttleTime } from "rxjs/operators";
import { LogoComponent } from "../../logo/logo.component";
import { TextComponent } from "../../ui/text/text.component";
import { createMenuItems, createNavItems } from "./dashboard-layout.constants";
import { NavItem } from "./dashboard-layout.model";

@Component({
  selector: "app-dashboard-layout",
  standalone: true,
  imports: [
    RouterModule,
    LogoComponent,
    TextComponent,
    FontAwesomeModule,
    ButtonModule,
    BadgeModule,
    RippleModule,
    AvatarModule,
    MenuModule
],
  templateUrl: "./dashboard-layout.component.html",
  styles: [
    `
      /* Custom scrollbar for sidebar */
      .modern-scrollbar::-webkit-scrollbar {
        width: 8px;
      }
      .modern-scrollbar::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
      .modern-scrollbar::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
      }
      .modern-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
    `,
  ],
})
export class DashboardLayoutComponent implements OnInit, OnDestroy {
  // All existing methods and properties remain the same
  scrolled = false;
  private scrollSubscription: Subscription | null = null;
  isSidebarOpen = signal(false);
  filteredNavItems = signal<NavItem[]>([]);
  private authService = inject(AuthService);
  private cd = inject(ChangeDetectorRef);

  documentStyle = getComputedStyle(document.documentElement);
  avatarColors = {
    bgColor: this.documentStyle.getPropertyValue("--p-purple-200"),
    textColor: this.documentStyle.getPropertyValue("--p-purple-900"),
  };

  get currentUser(): AdminUser | null {
    return this.authService.currentUser;
  }

  get userInitials(): string {
    return (
      (this.currentUser?.firstName?.charAt(0) || "") +
      (this.currentUser?.lastName?.charAt(0) || "")
    );
  }

  items: MenuItem[] = createMenuItems(() => this.logout());

  get userRole(): string {
    return this.authService.currentUser?.role || "";
  }

  navItems: NavItem[] = createNavItems(() => this.logout());

  ngOnInit(): void {
    this.updateFilteredNavItems();

    this.scrollSubscription = fromEvent(window, "scroll")
      .pipe(
        throttleTime(50),
        map(() => window.scrollY > 50),
      )
      .subscribe((isScrolled) => {
        this.scrolled = isScrolled;
      });
  }

  ngOnDestroy(): void {
    if (this.scrollSubscription) {
      this.scrollSubscription.unsubscribe();
    }
  }

  scrollToTop(): void {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }

  toggleSidebar(): void {
    this.isSidebarOpen.update((value) => !value);
    this.cd.detectChanges();
  }

  handleNavItemClick(item: NavItem): void {
    if (item.type === "action" && item.action) {
      item.action();
    }
  }

  handleMenuItemClick(item: MenuItem): void {
    if (item["action"]) {
      item["action"]();
    }
  }

  logout(): void {
    this.authService.logout();
  }

  private hasRole(requiredRoles: string[]): boolean {
    if (!requiredRoles || requiredRoles.length === 0) {
      return false;
    }
    if (requiredRoles.includes("ALL")) {
      return true;
    }
    return requiredRoles.includes(this.userRole);
  }

  private filterItems(items: NavItem[]): NavItem[] {
    return items
      .filter((item) => !item.roles || this.hasRole(item.roles))
      .map((item) => ({
        ...item,
        children: item.children ? this.filterItems(item.children) : undefined,
      }));
  }

  private updateFilteredNavItems(): void {
    this.filteredNavItems.set(this.filterItems(this.navItems));
  }
}
