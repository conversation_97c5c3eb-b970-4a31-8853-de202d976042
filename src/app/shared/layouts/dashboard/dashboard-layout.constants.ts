import { APP_ROUTES } from "@/app/core/constants/routes";
import { Roles } from "@/app/core/enums/roles.enum";
import {
  faBank,
  faBox,
  faCalendarCheck,
  faChartBar,
  faChartLine,
  faCheck,
  faCheckCircle,
  faClock,
  faCogs,
  faEdit,
  faEye,
  faFileInvoice,
  faGear,
  faHourglassHalf,
  faInstitution,
  faMoneyBill,
  faPlus,
  faReceipt,
  faSignOut,
  faSquare,
  faTachometer,
  faTimesCircle,
  faTrash,
  faUserCircle,
  faUsers,
  faWallet,
} from "@fortawesome/free-solid-svg-icons";
import { NavItem } from "./dashboard-layout.model";

export const createMenuItems = (logout: () => void) => [
  {
    label: "Profile",
    items: [
      {
        label: "Change Password",
        icon: "pi pi-cog",
        // shortcut: "⌘+O",
        href: APP_ROUTES.changePassword,
      },
      {
        label: "Logout",
        icon: "pi pi-sign-out",
        // shortcut: "⌘+Q",
        action: () => logout(),
      },
    ],
  },
  { separator: true },
];

export const ICONS = {
  dashboard: faTachometer,
  subscriptions: faCalendarCheck,
  wallet: faWallet,
  plus: faPlus,
  exit: faSignOut,
  client: faUserCircle,
  square: faSquare,
  institution: faInstitution,
  bank: faBank,
  edit: faEdit,
  view: faEye,
  delete: faTrash,
  softDelete: faTrash,
  add: faPlus,
  settings: faGear,
  period: faClock,
  money: faMoneyBill,
  product: faBox,
  chart: faChartLine,
  statement: faFileInvoice,
  active: faCheckCircle,
  pending: faHourglassHalf,
  expired: faTimesCircle,
  tracking: faChartBar,
  subscription: faReceipt,
  users: faUsers,
  cogs: faCogs,
  tick: faCheck,
  bankDeposit: faMoneyBill,
  historicalData: faFileInvoice,
} as const;

export const createNavItems = (logout: () => void): NavItem[] => [
  {
    label: "Dashboard",
    icon: ICONS.dashboard,
    routerLink: "/admin/dashboard",
  },
  {
    header: "ADMINISTRATION",
    children: [
      {
        label: "Manage Admins",
        icon: ICONS.client,
        routerLink: "/admin/admins",
        roles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      },
      {
        label: "Manage Departments",
        icon: ICONS.institution,
        routerLink: "/admin/department",
        roles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      },
      {
        label: "Manage Roles",
        icon: ICONS.edit,
        routerLink: "/admin/role",
        roles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      },
      {
        label: "Manage Groups",
        icon: ICONS.edit,
        routerLink: "/admin/group",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
        ],
      },
    ],
  },
  {
    header: "Sys Configs",
    children: [
      {
        label: "Currency",
        icon: ICONS.money,
        routerLink: "/admin/currency",
        roles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      },
      {
        label: "Periods",
        icon: ICONS.period,
        routerLink: "/admin/periods",
        roles: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER, Roles.VOT],
      },
    ],
  },
  {
    header: "Banks",
    children: [
      {
        label: "Banks",
        icon: ICONS.subscription,
        routerLink: "/admin/banks",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
          Roles.OPPS_MANAGER,
        ],
      },
      {
        label: "Bank Deposits",
        icon: ICONS.bankDeposit,
        routerLink: "/admin/bank-deposits",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.QA_AND_SECURITY,
          Roles.FINANCE_ADMIN,
          Roles.VOT,
        ],
      },
    ],
  },
  {
    header: "CLIENTS",
    children: [
      {
        label: "Clients",
        icon: ICONS.client,
        routerLink: "/admin/clients",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.FINANCE_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.VOT,
        ],
      },
      {
        label: "Client Types",
        icon: ICONS.square,
        routerLink: "/admin/clients/client-types",
        roles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      },
      {
        label: "Client Groups",
        icon: ICONS.users,
        routerLink: "/admin/client-groups",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.FINANCE_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.VOT,
        ],
      },
      {
        label: "Package Exemptions",
        icon: ICONS.tick,
        routerLink: "/admin/package-exemptions",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.FINANCE_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.VOT,
        ],
      },
    ],
  },
  {
    header: "PRODUCTS",
    children: [
      {
        label: "Products",
        icon: ICONS.product,
        routerLink: "/admin/products-management",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.QA_AND_SECURITY,
          Roles.FINANCE_ADMIN,
          Roles.VOT,
        ],
      },
      {
        label: "Historical Products",
        icon: ICONS.plus,
        routerLink: "/admin/historical-products",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.QA_AND_SECURITY,
          Roles.FINANCE_ADMIN,
          Roles.VOT,
        ],
      },
      {
        label: "Addons",
        icon: ICONS.plus,
        routerLink: "/admin/addons",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.QA_AND_SECURITY,
          Roles.FINANCE_ADMIN,
          Roles.VOT,
        ],
      },
      {
        label: "Package Categories",
        icon: ICONS.square,
        routerLink: "/admin/package-categories",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
        ],
      },
      {
        label: "Product Packages",
        icon: ICONS.product,
        routerLink: "/admin/product-packages",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.FINANCE_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
        ],
      },
      {
        label: "Historical Packages",
        icon: ICONS.product,
        routerLink: "/admin/historical-packages",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.FINANCE_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
        ],
      },
      {
        label: "ATS Products",
        icon: ICONS.product,
        routerLink: "/admin/ats/products",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
        ],
      },
    ],
  },
  {
    header: "SUBS",
    children: [
      {
        label: "All Subs",
        icon: ICONS.active,
        routerLink: "/admin/subscriptions",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.QA_AND_SECURITY,
          Roles.FINANCE_ADMIN,
          Roles.VOT,
        ],
      },
      {
        label: "Initiated Transactions",
        icon: ICONS.active,
        routerLink: "/admin/subscriptions/initiated",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.QA_AND_SECURITY,
          Roles.FINANCE_ADMIN,
          Roles.VOT,
        ],
      },
    ],
  },
  {
    header: "VOT",
    children: [
      {
        label: "VOT Users",
        icon: ICONS.users,
        routerLink: "/admin/products/vot-users",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.QA_AND_SECURITY,
          Roles.OPPS_OFFICER,
          Roles.FINANCE_ADMIN,
          Roles.VOT,
        ],
      },
      {
        label: "ATS VOT Users",
        icon: ICONS.cogs,
        routerLink: "/admin/ats/vot-users",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.FINANCE_ADMIN,
          Roles.VOT,
        ],
      },
    ],
  },
  {
    header: "HISTORICAL DATA",
    children: [
      {
        label: "Historical Data",
        icon: ICONS.historicalData,
        routerLink: "/admin/historical-data",
        roles: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.QA_AND_SECURITY,
          Roles.OPPS_OFFICER,
          Roles.FINANCE_ADMIN,
          Roles.VOT,
        ],
      }
    ],
  },
  {
    header: "LOG OUT",
    children: [
      {
        label: "Log Out",
        icon: ICONS.exit,
        type: "action",
        action: () => logout(),
      },
    ],
  },
];
