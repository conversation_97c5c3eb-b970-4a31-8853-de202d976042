import { APP_ROUTES } from '@/app/core/constants/routes.constant';
import { AuthService } from '@/app/features/auth/core/services/auth.service';

import { Component, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { LogoComponent } from '../../logo/logo.component';
import { ButtonComponent } from '../../ui/button/button.component';
import { ContainerComponent } from '../../ui/container/container.component';

interface NavItem {
  label: string;
  routerLink: string;
  badge?: number;
}

@Component({
  selector: 'app-nav',
  standalone: true,
  imports: [
    RouterModule,
    LogoComponent,
    ContainerComponent,
    ButtonComponent
],
  templateUrl: './nav.component.html',
  styleUrls: ['./nav.component.css'],
})
export class NavComponent {
  isMobileMenuOpen = false;
  authService = inject(AuthService);

  navItems: NavItem[] = [
    { label: 'Dashboard', routerLink: APP_ROUTES.dashboard },
    { label: 'Subscriptions', routerLink: APP_ROUTES.subscriptions },
    { label: 'Wallet', routerLink: APP_ROUTES.wallet },
  ];

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }
}
