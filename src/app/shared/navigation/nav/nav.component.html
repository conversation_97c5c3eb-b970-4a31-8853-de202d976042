<nav class="py-2 text-gray-300 bg-dark">
  <app-container>
    <!-- Desktop Navigation -->
    <div class="flex items-center justify-between">
      <!-- Left section: Logo -->
      <div class="flex items-center gap-12">
        <app-logo variant="white" />

        <!-- Center section: Main navigation -->
        <div class="items-center hidden space-x-4 lg:flex">
          @for (item of navItems; track item.routerLink) {
            <a
              [routerLink]="item.routerLink"
              routerLinkActive="text-white"
              class="flex items-center px-3 py-2 space-x-2 rounded-md hover:bg-white/5"
            >
              @if (item.badge) {
                <span class="text-lime-400">{{ item.badge }}</span>
              }
              <span>{{ item.label }}</span>
            </a>
          }
        </div>
      </div>

      <div class="flex items-center gap-3">
        <!-- Mobile menu button -->
        <button
          class="p-2 rounded-md lg:hidden hover:hover:bg-white/5"
          (click)="toggleMobileMenu()"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-6 h-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            />
          </svg>
        </button>

        <app-button size="sm" (click)="authService.logout()">Logout</app-button>
      </div>
    </div>

    <!-- Mobile Navigation -->
    @if (isMobileMenuOpen) {
      <div class="lg:hidden">
        <div class="pt-6 pb-3 space-y-1">
          @for (item of navItems; track item.routerLink) {
            <a
              [routerLink]="item.routerLink"
              routerLinkActive="text-white"
              class="block px-3 py-2 text-base font-medium rounded-md hover:hover:bg-white/5"
            >
              @if (item.badge) {
                <span class="text-lime-400">{{ item.badge }}</span>
              }
              {{ item.label }}
            </a>
          }
        </div>
      </div>
    }
  </app-container>
</nav>
