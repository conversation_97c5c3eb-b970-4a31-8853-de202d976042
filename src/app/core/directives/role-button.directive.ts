// button-permission.directive.ts
import { Directive, Input, TemplateRef, ViewContainerRef } from "@angular/core";
import { PermissionsService } from "../services/permissions.service";

@Directive({
  selector: "[appButtonPermission]",
  standalone: true,
})
export class ButtonPermissionDirective {
  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private permissionsService: PermissionsService,
  ) {}

  @Input() set appButtonPermission(
    action: "create" | "edit" | "delete" | "view",
  ) {
    const hasPermission = this.permissionsService.hasPermission(action);

    if (hasPermission) {
      this.viewContainer.createEmbeddedView(this.templateRef);
    } else {
      this.viewContainer.clear();
    }
  }
}
