import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export interface PasswordValidationErrors {
  lowercase?: boolean;
  uppercase?: boolean;
  digit?: boolean;
  specialChar?: boolean;
  noSpaces?: boolean;
  minlength?: boolean;
  maxlength?: boolean;
}

export class PasswordValidators {
  static patternValidator(
    minLength: number = 8,
    maxLength: number = 32,
  ): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const value = control.value;
      const errors: PasswordValidationErrors = {};

      // Length checks
      if (value.length < minLength) {
        errors.minlength = true;
      }

      if (value.length > maxLength) {
        errors.maxlength = true;
      }

      // Pattern checks
      if (!/(?=.*[a-z])/.test(value)) {
        errors.lowercase = true;
      }

      if (!/(?=.*[A-Z])/.test(value)) {
        errors.uppercase = true;
      }

      if (!/(?=.*\d)/.test(value)) {
        errors.digit = true;
      }

      if (!/(?=.*[!@#$%^&*(),.?":{}|<>])/.test(value)) {
        errors.specialChar = true;
      }

      if (/\s/.test(value)) {
        errors.noSpaces = true;
      }

      return Object.keys(errors).length > 0 ? errors : null;
    };
  }

  // Password strength indicator (optional)
  static getPasswordStrength(password: string): number {
    let strength = 0;

    if (password.length >= 8) strength += 20;
    if (/(?=.*[a-z])/.test(password)) strength += 20;
    if (/(?=.*[A-Z])/.test(password)) strength += 20;
    if (/(?=.*\d)/.test(password)) strength += 20;
    if (/(?=.*[!@#$%^&*(),.?":{}|<>])/.test(password)) strength += 20;

    return strength;
  }
}
