import { Exchange } from '../enums/exchange.enum';

export interface ExcelExportOptions {
  fileName: string;
  sheetName: string;
  exchangeName?: string;
  exchange: Exchange;
  reportTitle: string;
  footer?: string;
  author?: string;
}

export interface ExcelHeaderOptions<T> {
  key: keyof T;
  header: string;
  alignment?: 'left' | 'center' | 'right';
  footerItem?: {
    value: string | number;
    colSpan?: number;
  };
}
