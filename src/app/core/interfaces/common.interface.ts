import { Status } from "../enums/status.enum";

export type ModalKey = "addItem" | "editItem" | "viewItem";

export interface ClientType {
  id: string;
  type: string;
  status: string;
}

export interface Country {
  id: string;
  name: string;
  code: string;
  cities: {
    id: string;
    name: string;
  }[];
}

export interface City {
  id: string;
  name: string;
}

export interface Sector {
  id: string;
  sector: string;
  status: string;
}

export interface CompanyType {
  id: string;
  type: string;
}

export interface ProductPackage {
  id: string;
  status: string;
  createdDate: number;
  modifiedDate: number | null;
  createdUser: string;
  modifiedUser: string;
  systemField: null;
  approvedBy: null;
  dateApproved: null;
  name: string;
  description: string | null;
  price: number;
  expiryPeriod: number;
  isRecommended: boolean;
  productPackageCategoryId: string;
  currencyId: string | null;
  products: Product[];
  addons: Addon[];
  periods: Period[];
  currencyCode: string | null;
  productPackageCategoryName: string;
}

export interface Product {
  id: string;
  status: string;
  createdDate: number;
  modifiedDate: number | null;
  createdUser: string | null;
  modifiedUser: string;
  systemField: null;
  approvedBy: null;
  dateApproved: null;
  name: string;
  description: string;
  linkTable: string;
  exchangeId: number;
}

export interface Addon {
  id: string;
  status: string;
  createdDate: number;
  modifiedDate: number | null;
  createdUser: string;
  modifiedUser: string;
  systemField: null;
  approvedBy: null;
  dateApproved: null;
  name: string;
  linkTable: string;
  description: string;
  price: number;
  currencyId: string;
  currencyCode: string | null;
  exchangeId: number;
}

export interface Period {
  id: string;
  status: string;
  createdDate: number;
  modifiedDate: number | null;
  createdUser: string;
  modifiedUser: string;
  systemField: null;
  approvedBy: null;
  dateApproved: null;
  name: string;
  length: number;
  multiplier: number;
  discount: number;
  price: number;
  isDefault: boolean;
}

export interface BaseResponse {
  id: string;
  status?: Status;
  createdDate: string;
  modifiedDate?: string;
  createdUser?: string;
  createdUserName: string;
  modifiedUser?: string;
  modifiedUserName: string;
  systemField?: string;
  approvedBy?: string;
  dateApproved?: string;
}
