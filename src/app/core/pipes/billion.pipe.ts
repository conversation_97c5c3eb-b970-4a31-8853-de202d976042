import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
  name: "billionFormat",
  standalone: true,
})
export class BillionFormatPipe implements PipeTransform {
  transform(value: number): string {
    if (value >= 1_000_000_000) {
      // Format with 2 decimal places and comma as decimal separator
      const billions = value / 1_000_000_000;
      return `${Math.floor(billions)},${String(Math.floor((billions % 1) * 100)).padStart(2, '0')} bln`;
    }
    return `${value.toLocaleString()}`;
  }
}