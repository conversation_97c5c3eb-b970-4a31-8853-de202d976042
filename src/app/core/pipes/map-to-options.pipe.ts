import { Pipe, PipeTransform } from '@angular/core';
import { mapDataToOptions, Option } from "@/app/core/utils/map-to-options.util";

@Pipe({
  name: 'mapToOptions',
  standalone: true,
})
export class MapToOptionsPipe implements PipeTransform {
  transform(
    items: any[] | null,
    valueKey: string,
    labelKey: string
  ): Option[] {
    if (!items) return [];
    return mapDataToOptions(items, valueKey, labelKey);
  }
}