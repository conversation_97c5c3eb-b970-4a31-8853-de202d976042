import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'unixDate',
  standalone: true,
})
export class UnixDatePipe implements PipeTransform {
  transform(
    value: number,
    format: 'full' | 'long' | 'medium' | 'short' = 'medium'
  ): string | null {
    if (!value) return null;

    const date = new Date(value * 1000);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: format,
    }).format(date);
  }
}
