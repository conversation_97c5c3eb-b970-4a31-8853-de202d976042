export function CreateJsonFromTable<T>(
  tableId: string,
  rowMapper: (cells: HTMLTableCellElement[], edited: boolean) => T
): T[] {
  const tableElement = document.getElementById(tableId)
  console.log('🚀 ~ tableElement:', tableElement)
  if (!tableElement) {
    console.error('Table not found')
    return []
  }

  const rows = tableElement.querySelectorAll('tbody tr')
  if (!rows.length) {
    console.error('No table rows found')
    return []
  }

  const jsonData: T[] = []

  rows.forEach((row) => {
    const cells = Array.from(row.querySelectorAll('td'))
    const hasEditedCells = cells.some((cell) =>
      cell.hasAttribute('data-edited')
    )

    if (hasEditedCells) {
      const rowData = rowMapper(cells, hasEditedCells)
      jsonData.push(rowData)
    }
  })

  return jsonData
}
