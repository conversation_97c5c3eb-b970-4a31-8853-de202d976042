import Swal from "sweetalert2";

type ToastType = "success" | "error" | "info" | "warning";

interface ShowToastOptions {
  message: string;
  type: ToastType;
  title?: string;
  position?: "top-end" | "top-start" | "bottom-end" | "bottom-start" | "center"; // Optional positioning
  timer?: number; // Optional timer duration in milliseconds
  isToast?: boolean;
  showConfirmButton?: boolean;
}

/**
 * Utility function to display a toast notification.
 * @param options - Configuration options for the toast.
 */
export function showToast({
  message,
  type,
  title,
  position = "top-end",
  timer = 15000,
  isToast = true,
  showConfirmButton = false,
}: ShowToastOptions): void {
  Swal.fire({
    icon: type,
    title: title || capitalizeFirstLetter(type),
    text: message,
    toast: isToast,
    position,
    showConfirmButton: showConfirmButton,
    timer,
    showCloseButton: true,
    timerProgressBar: true,
  });
}

/**
 * Capitalizes the first letter of a string.
 * @param str - The string to capitalize.
 * @returns The capitalized string.
 */
function capitalizeFirstLetter(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
