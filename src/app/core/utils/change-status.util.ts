import { Status } from "../enums/status.enum";

export function changeStatusByAvailableStatus(
  availableStatus: number,
  changeStatus: (status: Status) => void,
): void {
  switch (availableStatus) {
    case 1:
      changeStatus(Status.DRAFT);
      break;
    case 2:
      changeStatus(Status.APPROVED);
      break;
    case 3:
      changeStatus(Status.PUBLISHED);
      break;
    default:
      console.warn("Unknown status:", availableStatus);
      break;
  }
}
