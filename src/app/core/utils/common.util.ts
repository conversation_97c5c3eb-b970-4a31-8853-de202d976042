import { Market } from "../enums/market.enum";
import { Status } from "../enums/status.enum";

export function getStatus(status: Status): string {
  switch (status) {
    case Status.DRAFT:
      return "Draft";
    case Status.APPROVED:
      return "Approved";
    case Status.PUBLISHED:
      return "Published";
    default:
      return status;
  }
}

export function getMarket(market: Market): string {
  switch (market) {
    case Market.ETF:
      return "Exchange-Traded Fund (ETF)";
    case Market.REG:
      return "Equities";
    case Market.ODD:
      return "Odd Lot";
    case Market.REIT:
      return "Real Estate Investment Trust (REIT)";
    case Market.BOND:
      return "Bond";
    default:
      return market;
  }
}
