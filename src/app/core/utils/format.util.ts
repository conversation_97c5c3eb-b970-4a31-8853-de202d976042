export function formatNumber(
  value: number | null | undefined,
  options?: Intl.NumberFormatOptions,
): string {
  if (value === null || value === undefined) {
    return "";
  }

  return value.toLocaleString("en-US", options);
}

export function formatCurrency(
  value: number | null | undefined,
  currency: string = "USD",
): string {
  if (value === null || value === undefined) {
    return "";
  }

  return value.toLocaleString("en-US", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

export function formatPercentage(
  value: number | null | undefined,
  options?: Intl.NumberFormatOptions,
): string {
  if (value === null || value === undefined) {
    return "";
  }

  return value.toLocaleString("en-US", {
    style: "percent",
    ...options,
  });
}

export function formatScientific(value: number | null | undefined): string {
  if (value === null || value === undefined) {
    return "";
  }

  return value.toExponential(2);
}

export function formatDate(dateInput: number | string): string {
  // Parse string input into a Date object if it's a valid string
  const date = new Date(dateInput);

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    throw new Error("Invalid date input");
  }

  return date.toLocaleDateString("en-US", {
    weekday: "long", // "Thursday"
    year: "numeric", // "2024"
    month: "long", // "November"
    day: "numeric", // "14"
  });
}
