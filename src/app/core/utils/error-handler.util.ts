import Swal from "sweetalert2";
import { showToast } from "./show-toast.util";

interface ErrorResponse {
  status: string;
  statusCode: number;
  message: string;
  data: any;
}

export function handleError(
  error: any,
  title?: string,
  type?: "error" | "info" | "warning",
) {
  let errorResponse: ErrorResponse;

  if (error.error) {
    // Assuming the error response follows the defined structure
    errorResponse = error.error as ErrorResponse;
  } else {
    // Fallback for unexpected error structures
    errorResponse = {
      status: "error",
      statusCode: error.status || 500,
      message:
        error.message || "An error occurred while processing your request.",
      data: null,
    };
  }

  Swal.fire({
    icon: "error",
    title: title || "Oops!",
    text: errorResponse.message,
    confirmButtonText: "OK",
  });
}
