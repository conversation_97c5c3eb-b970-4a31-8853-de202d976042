import { Injectable } from "@angular/core";

@Injectable({
  providedIn: "root",
})
export class DateUtilsService {
  /**
   * Formats a date for reports in the format: DD Month YYYY (e.g., 25 January 2025).
   */
  formatDateForReports(date: Date | string): string {
    return new Date(date).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "long",
      year: "numeric",
    });
  }

  /**
   * Formats a date to ISO standard (YYYY-MM-DD) using 'en-CA' locale.
   */
  formatDateToISO(date: Date | string): string {
    const localDate = new Date(date);
    return localDate.toLocaleDateString("en-CA");
  }
}
