import { Injectable, inject } from '@angular/core';
import { MessageService } from 'primeng/api';

export interface ToastConfig {
  severity: 'success' | 'info' | 'warn' | 'error';
  summary?: string;
  detail: string;
  life?: number;
  sticky?: boolean; // Indicates if the toast should not disappear
  closable?: boolean;
  position?:
    | 'top-right'
    | 'top-left'
    | 'bottom-right'
    | 'bottom-left'
    | 'top-center'
    | 'bottom-center';
}

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  private messageService = inject(MessageService);

  private defaultConfig: Partial<ToastConfig> = {
    life: 3000,
    sticky: false,
    closable: true,
  };

  /**
   * Show a toast message with custom configuration
   * @param config Toast configuration object
   */
  show(config: ToastConfig): void {
    // If sticky is true, set life to 0 so the toast doesn't disappear
    const finalConfig = {
      ...this.defaultConfig,
      ...config,
      life: config.sticky ? 0 : config.life, // sticky to not disappear
    };
    this.messageService.add(finalConfig);
  }

  /**
   * Show success toast message
   * @param message Main message text
   * @param summary Optional summary/title
   * @param life Optional duration in milliseconds
   */
  success(
    message: string,
    summary: string = 'Success',
    life: number = 3000,
  ): void {
    this.show({
      severity: 'success',
      summary,
      detail: message,
      life,
    });
  }

  /**
   * Show error toast message
   * @param message Main message text
   * @param summary Optional summary/title
   * @param life Optional duration in milliseconds
   */
  error(message: string, summary: string = 'Error', life: number = 5000): void {
    this.show({
      severity: 'error',
      summary,
      detail: message,
      life,
    });
  }

  /**
   * Show info toast message
   * @param message Main message text
   * @param summary Optional summary/title
   * @param life Optional duration in milliseconds
   */
  info(message: string, summary: string = 'Info', life: number = 3000): void {
    this.show({
      severity: 'info',
      summary,
      detail: message,
      life,
    });
  }

  /**
   * Show warning toast message
   * @param message Main message text
   * @param summary Optional summary/title
   * @param life Optional duration in milliseconds
   */
  warn(
    message: string,
    summary: string = 'Warning',
    life: number = 4000,
  ): void {
    this.show({
      severity: 'warn',
      summary,
      detail: message,
      life,
    });
  }

  /**
   * Clear all active toast messages
   */
  clear(): void {
    this.messageService.clear();
  }

  /**
   * Show multiple toast messages at once
   * @param messages Array of toast configurations
   */
  showMultiple(messages: ToastConfig[]): void {
    this.messageService.addAll(messages);
  }
}
