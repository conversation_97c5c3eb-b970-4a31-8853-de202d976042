import { Exchange } from '@/app/core/enums/exchange.enum';
import { ApiResponse } from '@/app/core/interfaces/api-response.interface';
import { environment } from '@/environments/environment.development';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { lastValueFrom, Observable } from 'rxjs';
import { Market } from '../enums/market.enum';
import { Status } from '../enums/status.enum';

@Injectable({
  providedIn: 'root',
})
export class UtilityService {
  private baseUrl = `${environment.apiBaseUrl}/system`;
  constructor(private http: HttpClient) {}

  getStatus(status: Status): string {
    switch (status) {
      case Status.DRAFT:
        return 'Draft';
      case Status.APPROVED:
        return 'Approved';
      case Status.PUBLISHED:
        return 'Published';
      default:
        return status;
    }
  }

  getMarket(market: Market): string {
    switch (market) {
      case Market.ETF:
        return 'Exchange-Traded Fund (ETF)';
      case Market.REG:
        return 'Equities';
      case Market.ODD:
        return 'Odd Lot';
      case Market.REIT:
        return 'Real Estate Investment Trust (REIT)';
      case Market.BOND:
        return 'Bond';
      default:
        return market;
    }
  }

  async convertImageToBase64(imagePath: string): Promise<string> {
    const response = await lastValueFrom(
      this.http.get(imagePath, { responseType: 'blob' }),
    );
    return new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(response);
    });
  }

  getExchangeById(exchangeId: number): Observable<ApiResponse<Exchange>> {
    const url = `${this.baseUrl}/get-exchange-name?exchangeId=${exchangeId}`;
    return this.http.get<ApiResponse<Exchange>>(url);
  }
}
