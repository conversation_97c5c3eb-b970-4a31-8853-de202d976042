import { inject, Injectable } from "@angular/core";
import * as ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import { Exchange } from "../enums/exchange.enum";
import {
  ExcelExportOptions,
  ExcelHeaderOptions,
} from "../interfaces/export.interface";
import { UtilityService } from "./utility.service";

@Injectable({
  providedIn: "root",
})
export class ExcelExportService {
  private readonly utilityService = inject(UtilityService);
  private lastGeneratedFile: File | null = null;

  async getLastGeneratedFile(): Promise<File | null> {
    return this.lastGeneratedFile;
  }

  async exportToExcel<T>(
    data: T[],
    headers: ExcelHeaderOptions<T>[],
    options: ExcelExportOptions,
    exchange: Exchange,
    rawDate: string,
    addressStartColumn: number = 4,
    shouldSaveFile: boolean = true,
  ): Promise<void> {
    const workbook = new ExcelJS.Workbook();
    workbook.creator = options.author ?? "System";
    workbook.created = new Date();
    workbook.modified = new Date();

    const date = new Date(rawDate).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "long",
      year: "numeric",
    });

    const worksheet = workbook.addWorksheet(options.sheetName ?? "Sheet1", {
      pageSetup: { paperSize: 9, orientation: "landscape" },
    });

    // Convert image to Base64 and add it to the worksheet
    const logoImage = await this.utilityService.convertImageToBase64(
      exchange === Exchange.ZSE
        ? "images/logos/zselogo.png"
        : "images/logos/vfexlogo.png",
    );

    const imageId = workbook.addImage({
      base64: logoImage,
      extension: "png",
    });

    worksheet.addImage(imageId, {
      tl: { col: 0, row: 1 },
      ext: { width: exchange === Exchange.ZSE ? 201 : 254.9, height: 117 },
    });

    // Adding address opposite the logo
    const addressText = [
      "Zimbabwe Stock Exchange Limited",
      "44 Ridgeway North Highlands Harare",
      "P O Box CY 2231 Causeway",
      "Tel: (263-4) 886830-5",
      "Email: <EMAIL>",
      "Website: www.zse.co.zw",
    ];

    let addressStartRow = 2;
    let addressColumn = addressStartColumn;

    addressText.forEach((line, index) => {
      const row = worksheet.getRow(addressStartRow + index);
      const cell = row.getCell(addressColumn);
      cell.value = line;
      cell.font = { size: 10, bold: true };
      cell.alignment = { vertical: "middle", horizontal: "right" };
      cell.border = {
        top: index === 0 ? { style: "medium" } : undefined,
        bottom:
          index === addressText.length - 1 ? { style: "medium" } : undefined,
        left: { style: "medium" },
        right: { style: "medium" },
      };
    });

    worksheet.addRow([]);

    const titleRow1 = worksheet.addRow([options.exchangeName]);
    const titleRow2 = worksheet.addRow([options.reportTitle]);
    const titleRow3 = worksheet.addRow([date]);

    [titleRow1, titleRow2, titleRow3].forEach((row) => {
      const cell = row.getCell(1);
      cell.font = { size: 12, bold: true, color: { argb: "000000" } };
      cell.alignment = { horizontal: "left", vertical: "middle" };
    });

    worksheet.addRow([]);

    // Headers section
    const headerRow = worksheet.addRow(headers.map((h) => h.header));
    headerRow.height = 25;

    headers.forEach((header, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "2B5EA2" },
      };
      cell.font = { bold: true, color: { argb: "FFFFFF" }, size: 10 };
      cell.alignment = {
        horizontal: header.alignment ?? "left",
        vertical: "middle",
        wrapText: true,
      };
      cell.border = {
        top: { style: "thin" as ExcelJS.BorderStyle },
        left: { style: "thin" as ExcelJS.BorderStyle },
        bottom: { style: "thin" as ExcelJS.BorderStyle },
        right: { style: "thin" as ExcelJS.BorderStyle },
      };
    });

    data.forEach((item, rowIndex) => {
      const row = worksheet.addRow(
        headers.map((header) => {
          const value = item[header.key];
          return typeof value === "number" ? value : value?.toString();
        }),
      );

      row.height = 20;

      if (rowIndex % 2 === 0) {
        row.eachCell((cell) => {
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "f9fafb" },
          };
        });
      }

      headers.forEach((header, colIndex) => {
        const cell = row.getCell(colIndex + 1);
        cell.alignment = {
          vertical: "middle",
          horizontal: header.alignment ?? "left",
          wrapText: false,
        };
        cell.border = {
          top: { style: "thin", color: { argb: "E1E1E1" } },
          left: { style: "thin", color: { argb: "E1E1E1" } },
          bottom: { style: "thin", color: { argb: "E1E1E1" } },
          right: { style: "thin", color: { argb: "E1E1E1" } },
        };
      });
    });

    // Apply footer row formatting only if footer items are present
    const footerRowData = headers.map(
      (header) => header.footerItem?.value ?? "",
    );

    // Check if any footer item exists
    const hasFooter = footerRowData.some((item) => item !== "");

    // Only add footer row if there is any footer data
    if (hasFooter) {
      const footerRow = worksheet.addRow(footerRowData);

      headers.forEach((header, index) => {
        const cell = footerRow.getCell(index + 1);
        cell.alignment = {
          horizontal: header.alignment ?? "left",
          vertical: "middle",
        };
        cell.font = { bold: true };

        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "f9fafb" },
        };

        if (header.footerItem?.colSpan) {
          worksheet.mergeCells(
            footerRow.number,
            index + 1,
            footerRow.number,
            index + header.footerItem.colSpan,
          );
        }
      });
    }

    worksheet.columns.forEach((column) => {
      let maxLength = 0;
      column.eachCell!({ includeEmpty: true }, (cell) => {
        const columnLength = cell.value ? cell.value.toString().length : 10;
        if (columnLength > maxLength) {
          maxLength = columnLength;
        }
      });
      column.width = maxLength < 10 ? 10 : maxLength + 2;
    });

    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: "application/octet-stream" });

    // Store the file before saving
    this.lastGeneratedFile = new File([blob], `${options.fileName}.xlsx`, {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    if (shouldSaveFile) {
      saveAs(blob, `${options.fileName}.xlsx`);
    }
  }
}
