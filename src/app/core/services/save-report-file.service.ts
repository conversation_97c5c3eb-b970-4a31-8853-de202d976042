import { HttpClient } from "@angular/common/http";
import { inject, Injectable } from "@angular/core";
import { lastValueFrom } from "rxjs";
import { catchError } from "rxjs/operators";

import { Exchange } from "@/app/core/enums/exchange.enum";
import { ProductLinkTable } from "@/app/core/enums/product-link-table.enum";
import { ExcelExportService } from "@/app/core/services/excel-export.service";
import { environment } from "@/environments/environment.development";

interface SaveReportParams {
  file: File;
  exchange: Exchange;
  date: string;
  extension: string;
  marketLinkTable?: ProductLinkTable;
}

@Injectable({
  providedIn: "root",
})
export class SaveReportFileService {
  private readonly HISTORICAL_DATA_URL = `${environment.apiBaseUrl}/historical-data`;

  private readonly httpClient = inject(HttpClient);
  private readonly excelExportService = inject(ExcelExportService);

  public async saveReportFile(
    exchange: Exchange,
    date: string,
    extension: string,
    marketLinkTable: ProductLinkTable = ProductLinkTable.MARKET_CAPITALISATION,
  ): Promise<void> {
    try {
      const file = await this.excelExportService.getLastGeneratedFile();
      if (!file) {
        throw new Error("No file generated to save");
      }

      const formData = this.buildFormData({
        file,
        exchange,
        date,
        extension,
        marketLinkTable,
      });

      await lastValueFrom(
        this.httpClient
          .post(`${this.HISTORICAL_DATA_URL}/save-report`, formData)
          .pipe(catchError(this.handleError)),
      );
    } catch (error) {
      this.handleError(error);
    }
  }

  private buildFormData({
    file,
    exchange,
    date,
    extension,
    marketLinkTable,
  }: SaveReportParams): FormData {
    const formData = new FormData();

    formData.append("file", file);
    formData.append("exchange", exchange.toString());
    formData.append("extension", extension);
    formData.append("marketLinkTable", marketLinkTable!.toString());
    formData.append("date", date);

    return formData;
  }

  private handleError(error: unknown): never {
    console.error("SaveReportFileService error:", error);
    throw error instanceof Error ? error : new Error(String(error));
  }
}
