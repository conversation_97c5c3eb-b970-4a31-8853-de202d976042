import { AuthService } from "@/app/features/auth/core/services/auth.service";
import { inject, Injectable } from "@angular/core";
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router";
import { filter } from "rxjs/operators";
import { Roles } from "../enums/roles.enum";
import { RoutePermission } from "../interfaces/permissions.types";
import { AdminUser } from "../models/userInfo.model";

@Injectable({
  providedIn: "root",
})
export class PermissionsService {
  private currentFullRoute: string = "";
  private currentUserRole: Roles = Roles.UNAUTHORIZED;
  private authService = inject(AuthService);

  // Define permissions for each route
  private readonly permissions: RoutePermission[] = [
    {
      route: "/admin/dashboard",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.OPPS_MANAGER,
        Roles.VOT,
      ],
      actions: {
        view: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
        edit: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.OPPS_MANAGER],
        create: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.OPPS_MANAGER],
      },
    },
    {
      route: "/admin/admins",
      allowedRoles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY],
        edit: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY],
        delete: [Roles.SUPER_ADMIN],
        view: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      },
    },
    {
      route: "/admin/department",
      allowedRoles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY],
        edit: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY],
        delete: [Roles.SUPER_ADMIN],
        view: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      },
    },
    {
      route: "/admin/role",
      allowedRoles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY],
        edit: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY],
        delete: [Roles.SUPER_ADMIN],
        view: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      },
    },
    {
      route: "/admin/currency",
      allowedRoles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY],
        edit: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY],
        delete: [Roles.SUPER_ADMIN],
        view: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      },
    },
    {
      route: "/admin/periods",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.VOT,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.VOT,
        ],
      },
    },
    {
      route: "/admin/banks",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
        Roles.OPPS_MANAGER,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.OPPS_MANAGER],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
          Roles.OPPS_MANAGER,
        ],
      },
    },
    {
      route: "/admin/bank-deposits",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.FINANCE_ADMIN,
        Roles.VOT,
      ],
      actions: {
        create: [],
        edit: [Roles.SUPER_ADMIN, Roles.FINANCE_ADMIN],
        delete: [],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.FINANCE_ADMIN,
          Roles.VOT,
        ],
      },
    },
    {
      route: "/admin/clients",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
        Roles.FINANCE_ADMIN,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY],
        edit: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
          Roles.FINANCE_ADMIN,
        ],
      },
    },
    {
      route: "/admin/clients/client-types",
      allowedRoles: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY],
        edit: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY],
        delete: [Roles.SUPER_ADMIN],
        view: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      },
    },
    {
      route: "/admin/products-management",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.OPPS_MANAGER,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
          Roles.OPPS_OFFICER,
          Roles.OPPS_MANAGER,
        ],
      },
    },
    {
      route: "/admin/historical-products",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.OPPS_MANAGER,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
          Roles.OPPS_OFFICER,
          Roles.OPPS_MANAGER,
        ],
      },
    },
    {
      route: "/admin/addons",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.OPPS_MANAGER,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
          Roles.OPPS_OFFICER,
          Roles.OPPS_MANAGER,
        ],
      },
    },
    {
      route: "/admin/package-categories",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.VOT,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.VOT,
        ],
      },
    },
    {
      route: "/admin/product-packages",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.VOT,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.VOT,
        ],
      },
    },
    {
      route: "/admin/historical-packages",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.VOT,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.VOT,
        ],
      },
    },
    {
      route: "/admin/ats/products",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
        Roles.VOT,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER, Roles.OPPS_OFFICER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER, Roles.OPPS_OFFICER],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.OPPS_MANAGER,
          Roles.OPPS_OFFICER,
          Roles.VOT,
        ],
      },
    },
    {
      route: "/admin/subscriptions",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
        Roles.FINANCE_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER, Roles.FINANCE_ADMIN],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
          Roles.FINANCE_ADMIN,
        ],
      },
    },
    {
      route: "/admin/subscriptions/initiated",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
        Roles.FINANCE_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER, Roles.FINANCE_ADMIN],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
          Roles.FINANCE_ADMIN,
        ],
      },
    },
    {
      route: "/admin/products/vot-users",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
        Roles.FINANCE_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER, Roles.OPPS_OFFICER],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
          Roles.FINANCE_ADMIN,
        ],
      },
    },
    {
      route: "/admin/ats/vot-users",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
        Roles.FINANCE_ADMIN,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
      ],
      actions: {
        view: [
          Roles.SUPER_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.VOT,
          Roles.FINANCE_ADMIN,
        ],
      },
    },
    {
      route: "/admin/ats/products/:productType",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.VOT,
        Roles.OPPS_MANAGER,
        Roles.OPPS_OFFICER,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER, Roles.OPPS_OFFICER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER, Roles.OPPS_OFFICER],
        delete: [Roles.SUPER_ADMIN],
        view: [Roles.SUPER_ADMIN, Roles.QA_AND_SECURITY, Roles.VOT],
      },
    },

    {
      route: "/admin/group",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.OPPS_MANAGER,
        Roles.FINANCE_ADMIN,
        Roles.OPPS_OFFICER,
        Roles.VOT,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER, Roles.QA_AND_SECURITY],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER, Roles.QA_AND_SECURITY],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.FINANCE_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.OPPS_OFFICER,
          Roles.VOT,
        ],
      },
    },

    {
      route: "/admin/client-groups",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.OPPS_MANAGER,
        Roles.FINANCE_ADMIN,
        Roles.OPPS_OFFICER,
        Roles.VOT,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.FINANCE_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.OPPS_OFFICER,
          Roles.VOT,
        ],
      },
    },
    {
      route: "/admin/package-exemptions",
      allowedRoles: [
        Roles.SUPER_ADMIN,
        Roles.QA_AND_SECURITY,
        Roles.OPPS_MANAGER,
        Roles.FINANCE_ADMIN,
        Roles.OPPS_OFFICER,
        Roles.VOT,
      ],
      actions: {
        create: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        edit: [Roles.SUPER_ADMIN, Roles.OPPS_MANAGER],
        delete: [Roles.SUPER_ADMIN],
        view: [
          Roles.SUPER_ADMIN,
          Roles.OPPS_MANAGER,
          Roles.FINANCE_ADMIN,
          Roles.QA_AND_SECURITY,
          Roles.OPPS_OFFICER,
          Roles.VOT,
        ],
      },
    },
  ];

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
  ) {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentFullRoute = this.getFullCurrentRoute();
      });

    this.currentFullRoute = this.getFullCurrentRoute();
    this.setCurrentUserRole();
  }

  private getFullCurrentRoute(): string {
    let route = this.activatedRoute.root;
    let pathSegments: string[] = [];

    while (route.firstChild) {
      route = route.firstChild;
      const routeConfig = route.routeConfig?.path;
      if (routeConfig) {
        pathSegments.push(routeConfig);
      }
    }

    const fullPath = `/${pathSegments.join("/")}`;
    const queryParams = this.router.routerState.snapshot.root.queryParams;
    const queryString = new URLSearchParams(queryParams).toString();

    return queryString ? `${fullPath}?${queryString}` : fullPath;
  }

  hasPermission(action: "create" | "edit" | "delete" | "view"): boolean {
    const matchedPermission = this.permissions.find((permission) =>
      this.matchRoute(permission.route, this.currentFullRoute),
    );

    if (!matchedPermission) return false;

    const actionRoles = matchedPermission.actions[action];
    if (!actionRoles) return false;

    return actionRoles.includes(this.currentUserRole);
  }

  public setCurrentUserRole() {
    const userRole = this.currentUser?.role as Roles | undefined;
    if (userRole) {
      this.currentUserRole = userRole;
    } else {
      this.currentUserRole = Roles.UNAUTHORIZED;
    }
  }

  private get currentUser(): AdminUser | null {
    return this.authService.currentUser;
  }
  private matchRoute(permissionRoute: string, currentRoute: string): boolean {
    const [permissionPath, permissionQuery] = permissionRoute.split("?");
    const [currentPath, currentQuery] = currentRoute.split("?");

    const permissionSegments = permissionPath.split("/").filter(Boolean);
    const currentSegments = currentPath.split("/").filter(Boolean);

    if (permissionSegments.length !== currentSegments.length) {
      return false;
    }

    const pathMatch = permissionSegments.every(
      (segment, index) =>
        segment.startsWith(":") || segment === currentSegments[index],
    );

    if (!pathMatch) {
      return false;
    }

    if (permissionQuery) {
      const permissionParams = new URLSearchParams(permissionQuery);
      const currentParams = new URLSearchParams(currentQuery);

      for (const [key, value] of permissionParams.entries()) {
        if (currentParams.get(key) !== value) {
          return false;
        }
      }
    }

    return true;
  }
}
