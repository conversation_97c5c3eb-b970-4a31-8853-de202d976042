// report-export.service.ts
import { DatePipe } from "@angular/common";
import { Injectable, inject } from "@angular/core";
import { saveAs } from "file-saver";
import * as XLSX from "xlsx";

@Injectable({
  providedIn: "root",
})
export class ReportExportService {
  private datePipe = inject(DatePipe);

  /**
   * Exports data to Excel with enhanced formatting
   * @param data Array of objects to export
   * @param filename Name of the file to be exported (without extension)
   */
  exportToExcel(data: any[], filename: string): void {
    // Create worksheet from data
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);

    // Get all columns
    const range = XLSX.utils.decode_range(worksheet["!ref"] || "A1");
    const cols = [];

    // Set column width to auto-fit content
    for (let C = range.s.c; C <= range.e.c; ++C) {
      // Find the maximum width of text in each column
      let maxWidth = 0;
      for (let R = range.s.r; R <= range.e.r; ++R) {
        const cell_ref = XLSX.utils.encode_cell({ c: C, r: R });
        const cell = worksheet[cell_ref];
        if (!cell) continue;

        const cellText = String(cell.v || "");
        maxWidth = Math.max(maxWidth, cellText.length);
      }

      // Set column width with some padding
      cols.push({ wch: maxWidth + 2 });
    }

    worksheet["!cols"] = cols;

    // Create a style object for headers (bold)
    const headerStyle = {
      font: {
        bold: true,
      },
      alignment: {
        horizontal: "center",
      },
    };

    // Apply style to header row (row 0)
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const headerCell = XLSX.utils.encode_cell({ r: 0, c: C });
      if (!worksheet[headerCell]) continue;

      // Ensure the cell has an 's' property for style
      if (!worksheet[headerCell].s) worksheet[headerCell].s = {};
      // Apply bold style
      worksheet[headerCell].s = headerStyle;
    }

    // Set frozen panes (freeze the first row)
    worksheet["!freeze"] = { xSplit: 0, ySplit: 1 };

    // Create workbook with proper configuration
    const workbook: XLSX.WorkBook = {
      Sheets: { data: worksheet },
      SheetNames: ["data"],
    };

    // Generate Excel file with styles enabled
    const excelBuffer: any = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
      cellStyles: true,
      bookSST: false,
    });

    // Save the file with timestamp
    const timestamp = this.datePipe.transform(new Date(), "yyyyMMdd_HHmmss");
    const filenameWithTimestamp = `${filename}_${timestamp}.xlsx`;

    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    saveAs(blob, filenameWithTimestamp);
  }

  /**
   * Exports data to CSV format
   * @param data Array of objects to export
   * @param filename Name of the file to be exported (without extension)
   */
  exportToCsv(data: any[], filename: string): void {
    const replacer = (key: any, value: any) => (value === null ? "" : value);
    const header = Object.keys(data[0]);
    let csv = data.map((row) =>
      header
        .map((fieldName) => JSON.stringify(row[fieldName], replacer))
        .join(","),
    );
    csv.unshift(header.join(","));
    const csvArray = csv.join("\r\n");

    const timestamp = this.datePipe.transform(new Date(), "yyyyMMdd_HHmmss");
    const filenameWithTimestamp = `${filename}_${timestamp}.csv`;

    const blob = new Blob([csvArray], { type: "text/csv;charset=utf-8;" });
    saveAs(blob, filenameWithTimestamp);
  }

  /**
   * Exports data to PDF format
   * Note: This is a placeholder. Actual PDF export would require additional libraries like pdfmake
   * @param data Array of objects to export
   * @param filename Name of the file to be exported (without extension)
   */
  exportToPdf(data: any[], filename: string): void {
    // Placeholder for PDF export implementation
    console.log("PDF export not yet implemented");
    // Implementation would go here using a library like pdfmake
  }
}
