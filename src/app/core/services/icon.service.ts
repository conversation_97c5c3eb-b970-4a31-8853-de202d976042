import { Injectable } from '@angular/core';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { APP_ICONS } from '../constants/icon.constant';

@Injectable({
  providedIn: 'root',
})
export class IconService {
  getIcon(name: keyof typeof APP_ICONS): IconDefinition {
    const icon = APP_ICONS[name];
    if (!icon) {
      console.warn(`Icon "${name}" not found`);
    }
    return icon;
  }

  getAllIcons(): typeof APP_ICONS {
    return APP_ICONS;
  }
}
