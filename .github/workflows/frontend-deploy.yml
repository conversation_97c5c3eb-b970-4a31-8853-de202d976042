name: Deploy Angular to EC2

on:
  push:
    branches:
      - staging
      - prod

jobs:
  deploy:
    name: Deploy to ${{ github.ref_name }}
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout Repository
      - name: Checkout Code
        uses: actions/checkout@v4

      # Step 2: Set up Node.js
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      # Step 3: Install Dependencies
      - name: Install Dependencies
        run: |
          npm ci
          npm install -g @angular/cli

      # Step 4: Build Angular Application
      - name: Build Angular App
        run: |
          if [ "${{ github.ref_name }}" = "prod" ]; then
            ng build --configuration production
          else
            ng build --configuration staging
          fi

      # Step 5: Deploy Frontend to EC2
      - name: Deploy to EC2
        uses: appleboy/scp-action@master
        with:
          host: ${{ github.ref_name == 'prod' && secrets.PROD_EC2_HOST || secrets.STAGING_EC2_HOST }}
          username: ${{ github.ref_name == 'prod' && secrets.PROD_EC2_USER || secrets.STAGING_EC2_USER }}
          key: ${{ github.ref_name == 'prod' && secrets.PROD_EC2_SSH_KEY || secrets.STAGING_EC2_SSH_KEY }}
          source: "dist/datadirect-admin/browser/"
          target: "/var/www/html/datadirect-admin"
          strip_components: 3
          timeout: 600s

      # Step 6: Restart Apache to Serve the New Build
      - name: Restart Apache
        uses: appleboy/ssh-action@master
        with:
          host: ${{ github.ref_name == 'prod' && secrets.PROD_EC2_HOST || secrets.STAGING_EC2_HOST }}
          username: ${{ github.ref_name == 'prod' && secrets.PROD_EC2_USER || secrets.STAGING_EC2_USER }}
          key: ${{ github.ref_name == 'prod' && secrets.PROD_EC2_SSH_KEY || secrets.STAGING_EC2_SSH_KEY }}
          timeout: 600s
          script: |
            sudo systemctl restart httpd