// tailwind-bootstrap-plugin.js
const plugin = require("tailwindcss/plugin");

module.exports = plugin(function ({ addComponents, theme }) {
  addComponents({
    // Buttons
    ".btn": {
      display: "inline-block",
      padding: `${theme("spacing.2")} ${theme("spacing.4")}`,
      fontWeight: theme("fontWeight.medium"),
      borderRadius: theme("borderRadius.DEFAULT"),
      transition: "all 200ms",
      "&:focus": {
        outline: "none",
        boxShadow: `0 0 0 2px ${theme("colors.ring")}`,
      },
    },
    ".btn-primary": {
      backgroundColor: theme("colors.primary"),
      color: theme("colors.background"),
      "&:hover": {
        backgroundColor: theme("colors.primary"),
      },
    },
    ".btn-secondary": {
      backgroundColor: theme("colors.secondary"),
      color: theme("colors.background"),
      "&:hover": {
        backgroundColor: theme("colors.secondary"),
      },
    },
    ".btn-success": {
      backgroundColor: theme("colors.success"),
      color: theme("colors.background"),
      "&:hover": {
        backgroundColor: theme("colors.success"),
        filter: "brightness(90%)",
      },
    },
    ".btn-danger": {
      backgroundColor: theme("colors.danger"),
      color: theme("colors.background"),
      "&:hover": {
        backgroundColor: theme("colors.danger"),
        filter: "brightness(90%)",
      },
    },
    ".btn-warning": {
      backgroundColor: theme("colors.warning"),
      color: theme("colors.dark"),
      "&:hover": {
        backgroundColor: theme("colors.warning"),
        filter: "brightness(90%)",
      },
    },
    ".btn-outline-primary": {
      borderWidth: "1px",
      borderColor: theme("colors.primary"),
      color: theme("colors.primary"),
      "&:hover": {
        backgroundColor: theme("colors.primary"),
        color: theme("colors.background"),
      },
    },
    ".btn-outline-secondary": {
      borderWidth: "1px",
      borderColor: theme("colors.secondary"),
      color: theme("colors.secondary"),
      "&:hover": {
        backgroundColor: theme("colors.secondary"),
        color: theme("colors.background"),
      },
    },

    // Forms
    ".invalid-feedback": {
      color: theme("colors.danger"),
      fontSize: theme("fontSize.sm"),
      marginTop: theme("spacing.1"),
      display: "block",
      textAlign: "left",
      fontWeight: theme("fontWeight.medium"),
      lineHeight: theme("lineHeight.6"),
    },
    ".form-control": {
      display: "block",
      width: "100%",
      padding: `${theme("spacing.2")} ${theme("spacing.3")}`,
      color: theme("colors.foreground"),
      borderWidth: "1px",
      borderColor: theme("colors.ring"),
      borderRadius: theme("borderRadius.DEFAULT"),
      backgroundColor: theme("colors.background"),
      "&:focus": {
        outline: "none",
        borderColor: theme("colors.primary"),
        boxShadow: `0 0 0 2px ${theme("colors.primary")}`,
      },
    },
    ".form-label": {
      display: "block",
      marginBottom: theme("spacing.2"),
      color: theme("colors.foreground"),
      fontWeight: theme("fontWeight.medium"),
      fontSize: theme("fontSize.sm"),
    },
    ".form-text": {
      color: theme("colors.secondary"),
      fontSize: theme("fontSize.sm"),
      marginTop: theme("spacing.1"),
    },

    // Cards
    ".card": {
      backgroundColor: theme("colors.background"),
      borderRadius: theme("borderRadius.lg"),
      borderWidth: "1px",
      borderColor: theme("colors.ring"),
    },
    ".card-header": {
      padding: theme("spacing.4"),
      borderBottomWidth: "1px",
      borderColor: theme("colors.ring"),
      backgroundColor: "rgba(108, 117, 125, 0.05)",
    },
    ".card-body": {
      padding: theme("spacing.4"),
    },
    ".card-footer": {
      padding: theme("spacing.4"),
      borderTopWidth: "1px",
      borderColor: theme("colors.ring"),
      backgroundColor: "rgba(108, 117, 125, 0.05)",
    },

    // Alerts
    ".alert": {
      position: "relative",
      padding: `${theme("spacing.3")} ${theme("spacing.4")}`,
      marginBottom: theme("spacing.4"),
      borderWidth: "1px",
      borderRadius: theme("borderRadius.DEFAULT"),
    },
    ".alert-primary": {
      color: theme("colors.primary"),
      backgroundColor: `${theme("colors.primary")}10`,
      borderColor: theme("colors.primary"),
    },
    ".alert-secondary": {
      color: theme("colors.secondary"),
      backgroundColor: `${theme("colors.secondary")}10`,
      borderColor: theme("colors.secondary"),
    },
    ".alert-success": {
      color: theme("colors.success"),
      backgroundColor: `${theme("colors.success")}10`,
      borderColor: theme("colors.success"),
    },
    ".alert-danger": {
      color: theme("colors.danger"),
      backgroundColor: `${theme("colors.danger")}10`,
      borderColor: theme("colors.danger"),
    },
    ".alert-warning": {
      color: theme("colors.warning"),
      backgroundColor: `${theme("colors.warning")}10`,
      borderColor: theme("colors.warning"),
    },

    // Tables
    ".table": {
      width: "100%",
      marginBottom: "1rem",
      color: theme("colors.foreground"),
      verticalAlign: "top",
      borderColor: theme("colors.ring"),
      "th, td": {
        padding: `${theme("spacing.3")} ${theme("spacing.4")}`,
        borderBottomWidth: "1px",
        borderColor: "inherit",
      },
      th: {
        fontWeight: theme("fontWeight.semibold"),
        color: theme("colors.dark"),
      },
    },
    ".table-striped tbody tr:nth-child(odd)": {
      backgroundColor: "rgba(108, 117, 125, 0.05)",
    },
    ".table-hover tbody tr:hover": {
      backgroundColor: "rgba(108, 117, 125, 0.075)",
    },

    // Badges
    ".badge": {
      display: "inline-block",
      padding: `${theme("spacing.1")} ${theme("spacing.2")}`,
      fontWeight: theme("fontWeight.medium"),
      fontSize: theme("fontSize.sm"),
      borderRadius: theme("borderRadius.full"),
    },
    ".badge-primary": {
      backgroundColor: theme("colors.primary"),
      color: theme("colors.background"),
    },
    ".badge-secondary": {
      backgroundColor: theme("colors.secondary"),
      color: theme("colors.background"),
    },
    ".badge-success": {
      backgroundColor: theme("colors.success"),
      color: theme("colors.background"),
    },
    ".badge-danger": {
      backgroundColor: theme("colors.danger"),
      color: theme("colors.background"),
    },
    ".badge-warning": {
      backgroundColor: theme("colors.warning"),
      color: theme("colors.dark"),
    },

    // List Groups
    ".list-group": {
      borderRadius: theme("borderRadius.lg"),
      borderWidth: "1px",
      borderColor: theme("colors.ring"),
    },
    ".list-group-item": {
      position: "relative",
      display: "block",
      padding: `${theme("spacing.3")} ${theme("spacing.4")}`,
      borderBottomWidth: "1px",
      borderColor: theme("colors.ring"),
      "&:last-child": {
        borderBottomWidth: "0",
      },
    },

    // Navbar
    ".navbar": {
      display: "flex",
      alignItems: "center",
      padding: `${theme("spacing.2")} ${theme("spacing.4")}`,
      backgroundColor: theme("colors.background"),
      borderBottomWidth: "1px",
      borderColor: theme("colors.ring"),
    },
    ".navbar-brand": {
      fontSize: theme("fontSize.xl"),
      fontWeight: theme("fontWeight.semibold"),
      color: theme("colors.primary"),
    },
    ".nav-link": {
      padding: `${theme("spacing.2")} ${theme("spacing.3")}`,
      color: theme("colors.foreground"),
      "&:hover": {
        color: theme("colors.primary"),
      },
    },
    ".nav-link.active": {
      color: theme("colors.primary"),
      fontWeight: theme("fontWeight.medium"),
    },

    // Custom background utilities
    ".bg-primary": {
      backgroundColor: theme("colors.primary"),
    },
    ".bg-secondary": {
      backgroundColor: theme("colors.secondary"),
    },
    ".bg-success": {
      backgroundColor: theme("colors.success"),
    },
    ".bg-danger": {
      backgroundColor: theme("colors.danger"),
    },
    ".bg-warning": {
      backgroundColor: theme("colors.warning"),
    },
    ".bg-dark": {
      backgroundColor: theme("colors.dark"),
    },

    // Custom text colors
    ".text-primary": {
      color: theme("colors.primary"),
    },
    ".text-secondary": {
      color: theme("colors.secondary"),
    },
    ".text-success": {
      color: theme("colors.success"),
    },
    ".text-danger": {
      color: theme("colors.danger"),
    },
    ".text-warning": {
      color: theme("colors.warning"),
    },
    ".text-dark": {
      color: theme("colors.dark"),
    },

    // Dashboard specific utilities
    ".dashboard-header": {
      height: "4rem",
      backgroundColor: theme("colors.dark"),
      borderBottomWidth: "1px",
      borderColor: "rgba(108, 117, 125, 0.1)",
    },
    ".dashboard-sidebar": {
      width: "16rem",
      backgroundColor: theme("colors.dark"),
      borderRightWidth: "1px",
      borderColor: "rgba(108, 117, 125, 0.1)",
    },
    ".sidebar-nav-item": {
      display: "flex",
      alignItems: "center",
      padding: `${theme("spacing.2")} ${theme("spacing.3")}`,
      color: theme("colors.gray.300"),
      borderRadius: theme("borderRadius.md"),
      "&:hover": {
        backgroundColor: theme("colors.primary"),
        color: theme("colors.background"),
      },
      "&.active": {
        backgroundColor: theme("colors.primary"),
        color: theme("colors.background"),
      },
    },
  });
});
