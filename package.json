{"name": "datadirect-admin", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.6", "@angular/cdk": "^18.2.6", "@angular/common": "^18.2.6", "@angular/compiler": "^18.2.6", "@angular/core": "^18.2.6", "@angular/forms": "^18.2.6", "@angular/localize": "18.2.6", "@angular/material": "^18.2.6", "@angular/platform-browser": "^18.2.6", "@angular/platform-browser-dynamic": "^18.2.6", "@angular/router": "^18.2.6", "@fortawesome/angular-fontawesome": "^0.15.0", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@ng-bootstrap/ng-bootstrap": "^17.0.1", "@popperjs/core": "^2.11.8", "@primeng/themes": "^18.0.0", "@types/datatables.net": "^1.10.24", "@types/datatables.net-buttons": "^1.4.7", "angular-datatables": "^15.0.0", "apexcharts": "^3.46.0", "axios": "^1.6.2", "bootstrap": "^5.2.3", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.6", "clsx": "^2.1.1", "datadirect-admin": "file:", "datatables.net": "^1.13.2", "datatables.net-buttons": "^2.3.4", "datatables.net-buttons-dt": "^2.3.4", "datatables.net-dt": "^1.13.2", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.9.0", "jquery": "^3.6.3", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.3", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "moment": "^2.29.4", "ng-apexcharts": "^1.9.0", "ng2-charts": "^6.0.1", "ngx-editor": "^16.0.1", "ngx-toastr": "^16.1.1", "openai": "^4.33.1", "popper.js": "^1.16.1", "primeicons": "^7.0.0", "primeng": "^18.0.0", "rxjs": "~7.8.0", "sweetalert2": "^11.7.3", "tailwind-merge": "^2.5.4", "tailwind-scrollbar": "^3.1.0", "xlsx": "^0.18.5", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.6", "@angular/cli": "~18.2.6", "@angular/compiler-cli": "^18.2.6", "@types/file-saver": "^2.0.7", "@types/jasmine": "~4.3.0", "@types/jquery": "^3.5.16", "autoprefixer": "^10.4.20", "html-docx-js-typescript": "^0.1.5", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "postcss": "^8.4.49", "prettier": "^3.3.3", "tailwindcss": "^3.4.15", "tslib": "^2.6.2", "typescript": "~5.4.5"}}